<?php

namespace Database\Factories;

use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

class RecipeFactory extends Factory
{
    public function definition(): array
    {
        return [
            'user_id' => User::factory(),
            'title' => $title = $this->faker->unique()->md5(),
            'slug' => Str::slug($title),
            'description' => $this->faker->sentence(),
            'instructions' => $this->faker->sentence(),
            'video' => $this->faker->md5(),
            'prep_time' => 15,
            'cook_time' => 25,
            'servings' => 4,
            'cover_photo' => $this->faker->imageUrl(),
            'published' => true,
            'published_at' => now(),
        ];
    }
}
