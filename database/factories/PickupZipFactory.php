<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use App\Models\Pickup;
use App\Models\PickupZip;

class PickupZipFactory extends Factory
{
    /**
     * Define the model's default state.
     */
    public function definition(): array
    {
        return [
            'pickup_id' => Pickup::factory(),
            'zip' => $this->faker->postcode(),
        ];
    }
}
