<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use App\Models\Order;
use App\Models\Pickup;
use App\Models\Schedule;
use App\Models\User;
use Carbon\Carbon;

class RecurringOrderFactory extends Factory
{
    /**
     * Define the model's default state.
     */
    public function definition(): array
    {
        return [
            'customer_id' => User::factory(),
            'fulfillment_id' => Pickup::factory(),
            'last_order_id' => null,
            'schedule_id' => Schedule::factory(),
            'reorder_frequency' => 7,
            'skip_count' => 0,
            'next_deadline' => now()->addDays(3),
            'next_delivery' => now()->addDays(5),
            'last_reminder_sent_at' => null,
            'updated_by' => User::factory(),
            'paused_at' => null,
            'deleted_at' => null,
            'generate_at' => now()->addDays(2),
            'ready_at' => now()->addDays(7),
        ];
    }
}
