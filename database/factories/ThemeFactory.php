<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use App\Models\Theme;
use App\Models\User;

class ThemeFactory extends Factory
{
    /**
     * Define the model's default state.
     */
    public function definition(): array
    {
        return [
            'id' => $this->faker->unique()->md5(),
            'title' => $this->faker->word(),
            'view_path' =>  'some/path/to/views',
            'settings' => [],
            'css' => '',
            'css_preview' => '',
            'custom_css' => '',
            'active' => true,
            'user_id' => User::factory(),
        ];
    }
}
