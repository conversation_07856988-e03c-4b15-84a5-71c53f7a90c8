<?php

namespace Database\Factories;

use App\Models\Card;
use App\Models\Order;
use App\Models\Payment;
use App\Models\Pickup;
use App\Models\User;
use App\Support\Enums\OrderStatus;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\Factory;

class OrderFactory extends Factory
{
    public function definition(): array
    {
        return [
            'type_id' => 1,
            'is_recurring' => null,
            'total' => 0,
            'original_total' => 0,
            'subtotal' => 0,
            'original_subtotal' => 0,
            'tax' => 0,
            'original_tax' => 0,
            'tax_rate' => 0,
            'weight' => 0,
            'original_weight' => 0,
            'fees_subtotal' => '0',
            'original_fees_subtotal' => '0',
            'credit_applied' => 0,
            'delivery_rate' => 0,
            'delivery_fee' => 0,
            'order_discount' => 0,
            'coupon_subtotal' => 0,
            'deadline_date' => now()->addDay()->format('Y-m-d'),
            'pickup_date' => now()->addDays(3)->format('Y-m-d'),
            'paid' => false,
            'status_id' => OrderStatus::confirmed(),
            'canceled' => false,
            'canceled_at' => null,
            'processed' => false,
            'confirmed' => false,
            'confirmed_date' => null,
            'customer_first_name' => 'Test first',
            'customer_last_name' => 'Test last',
            'customer_notes' => 'This is a customer note example.',
            'invoice_notes' => 'This is an invoice note example.',
            'customer_email' => $this->faker->safeEmail(),
            'payment_id' => Payment::factory(),
            'customer_id' => User::factory(),
            'pickup_id' => Pickup::factory(),
            'order_number' => 0,
            'staff_id' => 0,
            'containers' => 0,
            'containers_2' => 0,
            'last_modified_by' => 0,
            'created_year' => now()->year,
            'created_month' => now()->month,
            'created_day' => now()->day,
        ];
    }

    public function currentUser()
    {
        return $this->state(function () {
            $user = auth()->user();
            $pickup = $user->pickup;

            if (is_null($pickup)) {
                $pickup = Pickup::first();
            }

            return [
                'customer_id' => $user->id,
                'total' => 0,
                'subtotal' => 0,
                'tax' => 0,
                'tax_rate' => 0,
                'weight' => 0,
                'fees_subtotal' => '2088',
                'credit_applied' => 0,
                'delivery_rate' => 30,
                'delivery_fee' => 0,
                'schedule_id' => $pickup->schedule_id,
                'pickup_id' => $pickup->id,
                'deadline_date' => now()->format('Y-m-d'),
                'pickup_date' => now()->addDays(3)->format('Y-m-d'),
                'customer_first_name' => $user->first_name,
                'customer_last_name' => $user->last_name,
                'customer_email' => $user->email,
                'customer_phone' => $user->phone,
                'shipping_street' => $user->street,
                'shipping_city' => $user->city,
                'shipping_state' => $user->state,
                'shipping_zip' => $user->zip,
                'paid' => false,
                'status_id' => 1,
                'processed' => false,
                'confirmed' => true,
                'confirmed_date' => Carbon::today(),
                'customer_notes' => 'This is a customer note example.',
                'invoice_notes' => 'This is an invoice note example.',
            ];
        });
    }

    public function setupPayment(User $user, Payment $payment): static
    {
        Card::factory()->create(['user_id' => $user->id,'source_id' => 'abc_123']);

        return $this->state(
            fn (array $attributes): array => [
                'pickup_id' => $user->pickup_point,
                'customer_id' => $user->id,
                'customer_first_name' => $user->first_name,
                'customer_last_name' => $user->last_name,
                'customer_email' => $user->email,
                'customer_phone' => $user->phone,
                'shipping_street' => $user->street,
                'shipping_city' => $user->city,
                'shipping_state' => $user->state,
                'shipping_zip' => $user->zip,
                'confirmed' => false,
                'payment_id' => $payment->id,
            ],
        )->afterCreating(function (Order $order) use ($user, $payment) {
            $order->pickup->payment_methods = [$payment->id];
            $order->pickup->save();
        });
    }
}
