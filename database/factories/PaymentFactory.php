<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use App\Models\Payment;

class PaymentFactory extends Factory
{
    /**
     * Define the model's default state.
     */
    public function definition(): array
    {
        return [
            'key' => $this->faker->unique()->md5(),
            'title' => $this->faker->title(),
            'instructions' => $this->faker->sentence(),
            'agreement' => $this->faker->sentence(),
            'enabled' => true
        ];
    }
}
