<?php

namespace Database\Factories;

use App\Models\Event;
use App\Models\Order;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<Event>
 */
class EventFactory extends Factory
{
    public function definition(): array
    {
        return [
            'user_id' => User::factory(),
            'event_id' => $this->faker->md5(),
            'description' => $this->faker->word(),
            'metadata' => json_encode(['foo' => 'bar']),
        ];
    }

    public function order()
    {
        return $this->state(function (array $attributes) {
            return [
                'model_type' => 'Order',
                'model_id' => Order::factory(),
            ];
        });
    }
}
