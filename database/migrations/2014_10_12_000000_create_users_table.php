<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

return new class extends Migration {

	/**
	 * Run the migrations.
	 */
	public function up(): void
	{
		Schema::create('users', function(Blueprint $table)
		{
			$table->increments('id');
			$table->string('email')->unique();
            $table->string('email_alt')->nullable();
			$table->string('password', 60)->nullable();
            $table->tinyInteger('role_id')->unsigned()->default(4);
            $table->integer('pricing_group_id')->unsigned()->nullable();
            $table->string('first_name');
            $table->string('last_name');
            $table->string('full_name');
            $table->string('company_name')->nullable();
            $table->string('profile_photo');
            $table->string('accounting_id')->unique()->nullable();
            $table->string('phone');
            $table->string('cell_phone');
            $table->string('street');
            $table->string('street_2');
            $table->string('city');
            $table->string('state', 2);
            $table->string('zip', 10)->nullable();
            $table->string('country');
            $table->string('billing_street');
            $table->string('billing_street_2');
            $table->string('billing_city');
            $table->string('billing_state', 2);
            $table->string('billing_zip', 10)->nullable();
            $table->string('billing_country');
            $table->double('lat')->nullable();
            $table->double('lng')->nullable();

            $table->integer('order_id')->unsigned();
            $table->integer('credit');
            $table->string('customer_id');
            $table->integer('checkout_card_id')->unsigned();
            $table->string('card_last_four');
            $table->string('card_brand');
            $table->integer('card_exp_month')->unsigned();
            $table->integer('card_exp_year')->unsigned();
            $table->string('card_name');
            $table->boolean('billing_same_as_shipping')->default(true);
            $table->boolean('newsletter')->default(false);
            $table->boolean('order_deadline_email_reminder')->default(true);
            $table->boolean('order_deadline_text_reminder')->default(true);
            $table->boolean('needs_reminded')->default(true);
            $table->boolean('active')->default(true);
            $table->integer('order_count')->unsigned()->default(0);
            $table->timestamp('last_purchase')->nullable();
            $table->timestamp('last_login')->nullable();
            $table->integer('pickup_point')->unsigned();
            $table->integer('delivery_zone')->unsigned();

            $table->integer('referral_source_id')->unsigned();
            $table->text('referral_description')->nullable();
            $table->integer('referral_user_id');
            $table->string('referral_code')->unique()->nullable();
            $table->string('referral_url')->nullable();
            $table->integer('referral_bonus')->unsigned()->nullable();
            $table->integer('referral_bonus_earned')->unsigned();
            $table->integer('referral_payout')->unsigned()->nullable();
            $table->boolean('can_backorder')->default(false);
            $table->boolean('has_profile')->default(false);
            $table->boolean('is_leader')->default(false);
            $table->boolean('exempt_from_fees')->default(false);
            $table->boolean('agrees_to_terms')->default(false);
            $table->text('settings')->nullable();
            $table->text('notes')->nullable();
			$table->rememberToken();
			$table->timestamps();
		});
	}

	/**
	 * Reverse the migrations.
	 */
	public function down(): void
	{
		Schema::drop('users');
	}

};
