<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('template_segments', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('template_id')->unsigned();
            $table->tinyInteger('type_id')->unsigned();
            $table->string('title');
            $table->string('view')->nullable();
            $table->text('settings')->nullable();
            $table->mediumText('content')->nullable();
            $table->mediumText('rendered_html')->nullable();
            $table->integer('sort')->unsigned();
            $table->timestamps();
            $table->foreign('template_id')->references('id')->on('templates')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('template_segments');
    }
};
