<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('proposals', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('pickup_id')->unsigned();
            $table->integer('user_id')->unsigned()->nullable();
            $table->string('first_name');
            $table->string('last_name');
            $table->string('phone');
            $table->string('email');
            $table->enum('status', ['new','pending','approved','rejected']);
            $table->tinyInteger('rating')->unsigned()->default(1);
            $table->text('details');
            $table->text('notes');
            $table->date('start_date')->nullable();
            $table->boolean('truck_parking')->default(false);
            $table->boolean('customer_parking')->default(false);
            $table->text('days_of_week');
            $table->string('street')->nullable();
            $table->string('street_2')->nullable();
            $table->string('city')->nullable();
            $table->string('state', 2)->nullable();
            $table->string('zip', 10)->nullable();
            $table->string('country');
            $table->double('lat')->nullable();
            $table->double('lng')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::drop('proposals');
    }
};
