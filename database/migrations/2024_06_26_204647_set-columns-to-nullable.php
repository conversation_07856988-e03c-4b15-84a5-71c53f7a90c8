<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('api_keys', function (Blueprint $table) {
            $table->string('prefix', 255)->nullable()->change();
        });

        Schema::table('collections', function (Blueprint $table) {
            $table->string('cover_photo', 255)->nullable()->change();
            $table->mediumText('description')->nullable()->change();
            $table->string('icon_photo', 255)->nullable()->change();
        });

        Schema::table('coupons', function (Blueprint $table) {
            $table->mediumText('settings')->nullable()->change();
            $table->integer('max_uses')->unsigned()->nullable()->change();
        });

        Schema::table('dates', function (Blueprint $table) {
            $table->mediumText('notes')->nullable()->change();
        });

        Schema::table('filters', function (Blueprint $table) {
            $table->string('type', 255)->nullable()->change();
        });

        Schema::table('ingredients', function (Blueprint $table) {
            $table->string('amount', 255)->nullable()->change();
        });

        Schema::table('locations', function (Blueprint $table) {
            $table->string('country', 255)->nullable()->change();
            $table->mediumText('description')->nullable()->change();
            $table->string('subtitle', 255)->nullable()->change();
        });

        Schema::table('media', function (Blueprint $table) {
            $table->mediumText('caption')->nullable()->change();
            $table->integer('height')->unsigned()->nullable()->change();
            $table->integer('width')->unsigned()->nullable()->change();
        });

        Schema::table('menu_items', function (Blueprint $table) {
            $table->integer('resource_id')->nullable()->unsigned()->change();
            $table->string('resource_type', 20)->nullable()->change();
        });

        Schema::table('order_fees', function (Blueprint $table) {
            $table->mediumText('note')->nullable()->change();
            $table->integer('discount')->unsigned()->nullable()->change();
            $table->integer('threshold')->unsigned()->nullable()->change();
            $table->integer('cap')->unsigned()->nullable()->change();
        });

        Schema::table('orders', function (Blueprint $table) {
            $table->string('billing_city', 255)->nullable()->change();
            $table->string('billing_state', 255)->nullable()->change();
            $table->string('billing_street', 255)->nullable()->change();
            $table->string('billing_street_2', 255)->nullable()->change();
            $table->string('billing_zip', 255)->nullable()->change();
            $table->mediumText('customer_notes')->nullable()->change();
            $table->string('customer_phone', 255)->nullable()->change();
            $table->string('customer_phone_2', 255)->nullable()->change();
            $table->mediumText('invoice_notes')->nullable()->change();
            $table->mediumText('packing_notes')->nullable()->change();
            $table->mediumText('payment_notes')->nullable()->change();
            $table->string('payment_terms', 255)->nullable()->change();
            $table->string('shipping_city', 255)->nullable()->change();
            $table->string('shipping_state', 255)->nullable()->change();
            $table->string('shipping_street', 255)->nullable()->change();
            $table->string('shipping_street_2', 255)->nullable()->change();
            $table->string('shipping_zip', 255)->nullable()->change();
            $table->string('tax_rate', 255)->nullable()->change();
        });

        Schema::table('pickups', function (Blueprint $table) {
            $table->string('country', 255)->nullable()->change();
            $table->text('description')->nullable()->change();
            $table->text('payment_methods')->nullable()->change();
            $table->string('subtitle', 255)->nullable()->change();
        });

        Schema::table('pickup_fees', function (Blueprint $table) {
            $table->text('note')->nullable()->change();
        });

        Schema::table('pages', function (Blueprint $table) {
            $table->longText('body')->nullable()->change();
            $table->string('description', 255)->nullable()->change();
            $table->string('page_title', 255)->nullable()->change();
            $table->string('subtitle', 255)->nullable()->change();
        });

        Schema::table('posts', function (Blueprint $table) {
            $table->longText('body')->nullable()->change();
            $table->text('summary', 255)->nullable()->change();
            $table->integer('visits')->unsigned()->nullable()->change();
        });

        Schema::table('products', function (Blueprint $table) {
            $table->mediumText('description')->nullable()->change();
            $table->string('unit_description')->nullable()->change();
            $table->integer('vendor_id')->unsigned()->nullable()->change();
            $table->integer('wholesale_unit_price')->unsigned()->nullable()->change();
            $table->integer('sale_unit_price')->unsigned()->nullable()->change();
            $table->integer('item_cost')->unsigned()->nullable()->change();
            $table->integer('inventory')->nullable()->change();
            $table->integer('processor_inventory')->nullable()->change();
            $table->integer('other_inventory')->nullable()->change();
            $table->integer('shared_inventory_id')->unsigned()->nullable()->change();
            $table->integer('back_order_limit')->unsigned()->nullable()->change();
            $table->integer('class_id')->unsigned()->nullable()->change();
            $table->integer('subclass_id')->unsigned()->nullable()->change();
            $table->string('cover_photo')->nullable()->change();
            $table->string('cover_photo_thumbnail')->nullable()->change();
            $table->text('ingredients')->nullable()->change();
            $table->text('keywords')->nullable()->change();
            $table->text('notes')->nullable()->change();
            $table->string('fulfillment_instructions')->nullable()->change();
            $table->string('accounting_class')->nullable()->change();
            $table->string('custom_sort')->nullable()->change();
            $table->string('seo_description', 255)->nullable()->change();
        });

        Schema::table('recipes', function (Blueprint $table) {
            $table->text('description')->nullable()->change();
            $table->longText('instructions')->nullable()->change();
        });

        Schema::table('subcollections', function (Blueprint $table) {
            $table->text('description')->nullable()->change();
        });

        Schema::table('templates', function (Blueprint $table) {
            $table->longText('body')->nullable()->change();
            $table->longText('plain_text')->nullable()->change();
        });

        Schema::table('themes', function (Blueprint $table) {
            $table->longText('settings')->nullable()->change();
            $table->longText('css')->nullable()->change();
            $table->longText('css_preview')->nullable()->change();
            $table->longText('custom_css')->nullable()->change();
        });

        Schema::table('schedules', function (Blueprint $table) {
            $table->text('notes')->nullable()->change();
            $table->integer('secondary_template_id')->nullable()->change();
        });

        Schema::table('users', function (Blueprint $table) {
            $table->string('first_name', 60)->nullable()->change();
            $table->string('last_name', 60)->nullable()->change();
            $table->string('full_name', 60)->nullable()->change();
            $table->string('profile_photo')->nullable()->change();
            $table->string('phone')->nullable()->change();
            $table->string('cell_phone')->nullable()->change();
            $table->string('street')->nullable()->change();
            $table->string('street_2')->nullable()->change();
            $table->string('city')->nullable()->change();
            $table->string('state', 2)->nullable()->change();
            $table->string('country')->nullable()->change();
            $table->string('billing_street')->nullable()->change();
            $table->string('billing_street_2')->nullable()->change();
            $table->string('billing_city')->nullable()->change();
            $table->string('billing_state', 2)->nullable()->change();
            $table->string('billing_country')->nullable()->change();
            $table->integer('order_id')->unsigned()->nullable()->change();
            $table->integer('credit')->nullable()->change();
            $table->string('customer_id')->nullable()->change();
            $table->integer('checkout_card_id')->unsigned()->nullable()->change();
            $table->string('card_last_four')->nullable()->change();
            $table->string('card_brand')->nullable()->change();
            $table->integer('card_exp_month')->unsigned()->nullable()->change();
            $table->integer('card_exp_year')->unsigned()->nullable()->change();
            $table->string('card_name')->nullable()->change();
            $table->integer('pickup_point')->unsigned()->nullable()->change();
            $table->integer('delivery_zone')->unsigned()->nullable()->change();
        });

        Schema::table('vendors', function (Blueprint $table) {
            $table->string('email')->nullable()->change();
            $table->string('phone')->nullable()->change();
            $table->string('website')->nullable()->change();
            $table->string('cover_photo')->nullable()->change();
            $table->text('description')->nullable()->change();
        });
    }
};
