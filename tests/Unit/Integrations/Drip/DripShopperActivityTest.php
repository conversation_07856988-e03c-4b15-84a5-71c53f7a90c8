<?php

namespace Tests\Unit\Integrations\Drip;

use App\Integrations\Drip\DripShopperActivity;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Product;
use App\Models\User;
use Illuminate\Http\Client\Request;
use Illuminate\Support\Facades\Http;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class DripShopperActivityTest extends TenantTestCase
{
    #[Test]
    public function it_can_be_configured(): void
    {
        $drip = (new DripShopperActivity)->configure('abc_123', 'def_456');

        $this->assertInstanceOf(DripShopperActivity::class, $drip);
    }

    #[Test]
    public function it_makes_the_expected_cart_created_request(): void
    {
        $expected_url = 'https://api.getdrip.com/v3/def_456/shopper_activity/cart';
        $expected_result = ['request_id' => '990c99a7-5cba-42e8-8f36-aec3419186ef'];

        Http::fake([
            $expected_url => Http::response($expected_result, 202),
        ]);

        $product = Product::factory()->create(['unit_price' => '1200.00']);
        $order_item = OrderItem::factory()->create(['product_id' => $product->id, 'unit_price' => 3442, 'qty' => 2]);

        $order_item->order->updateTotals();

        $drip = (new DripShopperActivity)->configure('abc_123', 'def_456');

        $this->assertEquals($expected_result, $drip->createCart($order_item->order, 'test.com'));

        Http::assertSent(function (Request $request) use ($expected_url, $order_item) {
            $auth = base64_encode('abc_123:');
            return $request->hasHeader('Authorization', "Basic {$auth}")
                && $request->hasHeader('User-Agent', 'Seven Sons (sevensons.net)')
                && $request->url() === $expected_url
                && $request->method() === 'POST'
                && $request->data() === [
                    'provider' => 'sevensons.net',
                    'email' => $order_item->order->customer->email,
                    'action' => 'created',
                    'cart_id' => (string) $order_item->order_id,
                    'cart_public_id' => "#{$order_item->order_id}",
                    'grand_total' => 68.84,
                    'total_discounts' => (float) money($order_item->order->order_discount + $order_item->order->coupon_subtotal + $order_item->order->credit_applied, ''),
                    'currency' => 'USD',
                    'cart_url' => 'https://test.com/cart',
                    'items' => [
                        [
                            'product_id' => (string) $order_item->product->id,
                            'sku' => $order_item->product->sku ?? '',
                            'name' => $order_item->product->title ?? '',
                            'price' => 1200.0,
                            'quantity' => 2,
                            'product_url' => 'https://test.com/store/product/' . $order_item->product->slug,
                            'image_url' => $order_item->product->cover_photo ?? '',
                        ]
                    ]
                ];
        });
    }

    #[Test]
    public function it_makes_the_expected_order_placed_request(): void
    {
        $expected_url = 'https://api.getdrip.com/v3/def_456/shopper_activity/order';
        $expected_result = ['request_id' => '990c99a7-5cba-42e8-8f36-aec3419186ef'];

        Http::fake([
            $expected_url => Http::response($expected_result, 202),
        ]);

        $product = Product::factory()->create(['unit_price' => '34.42']);
        $order_item = OrderItem::factory()->create(['product_id' => $product->id, 'unit_price' => 120000, 'qty' => 2]);

        $order_item->order->updateTotals();

        $drip = (new DripShopperActivity)->configure('abc_123', 'def_456');

        $this->assertEquals($expected_result, $drip->placeOrder($order_item->order, 'test.com'));

        Http::assertSent(function (Request $request) use ($expected_url, $order_item) {
            $auth = base64_encode('abc_123:');
            return $request->hasHeader('Authorization', "Basic {$auth}")
                && $request->hasHeader('User-Agent', 'Seven Sons (sevensons.net)')
                && $request->url() === $expected_url
                && $request->method() === 'POST'
                && $request->data() === [
                    'provider' => 'sevensons.net',
                    'email' => $order_item->order->customer->email,
                    'action' => 'placed',
                    'order_id' => (string) $order_item->order->id,
                    'order_public_id' => "#{$order_item->order->id}",
                    'grand_total' => (float) money($order_item->order->total, ''),
                    'total_discounts' => (float) money($order_item->order->order_discount + $order_item->order->coupon_subtotal + $order_item->order->credit_applied, ''),
                    'total_taxes' => (float) money($order_item->order->tax, ''),
                    'total_fees' => (float) money($order_item->order->fees_subtotal, ''),
                    'total_shipping' => (float) money($order_item->order->delivery_fee, ''),
                    'currency' => 'USD',
                    'order_url' => 'https://test.com/account/orders/' . $order_item->order->id,
                    'items' => [
                        [
                            'product_id' => (string) $order_item->product->id,
                            'sku' => $order_item->product->sku ?? '',
                            'name' => $order_item->product->title ?? '',
                            'price' => 1200.0,
                            'quantity' => (int) $order_item->qty,
                            'product_url' => 'https://test.com/store/product/' . $order_item->product->slug,
                            'image_url' => $order_item->product->cover_photo ?? '',
                        ]
                    ]
                ];
        });
    }

    #[Test]
    public function it_makes_the_expected_order_canceled_request(): void
    {
        $expected_url = 'https://api.getdrip.com/v3/def_456/shopper_activity/order';
        $expected_result = ['request_id' => '990c99a7-5cba-42e8-8f36-aec3419186ef'];

        Http::fake([
            $expected_url => Http::response($expected_result, 202),
        ]);

        $order = Order::factory()->create();

        $drip = (new DripShopperActivity)->configure('abc_123', 'def_456');

        $this->assertEquals($expected_result, $drip->cancelOrder($order, 'test.com'));

        Http::assertSent(function (Request $request) use ($expected_url, $order) {
            $auth = base64_encode('abc_123:');
            return $request->hasHeader('Authorization', "Basic {$auth}")
                && $request->hasHeader('User-Agent', 'Seven Sons (sevensons.net)')
                && $request->url() === $expected_url
                && $request->method() === 'POST'
                && $request->data() === [
                    'provider' => 'sevensons.net',
                    'email' => $order->customer->email,
                    'action' => 'canceled',
                    'order_id' => (string) $order->id,
                ];
        });
    }

    #[Test]
    public function it_converts_an_order_and_domain_to_shopper_activty_order(): void
    {
        $customer = User::factory()->create();
        $order = Order::factory()->create(['customer_id' => $customer->id]);
        $order_items = OrderItem::factory()->count(2)->create(['order_id' => $order->id]);

        // to ensure proper item orderBy check for the test
        $order->setRelation('items', $order_items);

        $this->assertEquals([
            'provider' => DripShopperActivity::PROVIDER,
            'email' => $customer->email,
            'action' => 'placed',
            'order_id' => (string) $order->id,
            'order_public_id' => "#{$order->id}",
            'grand_total' => (float) money($order->total, ''),
            'total_discounts' => (float) money($order->order_discount + $order->coupon_subtotal + $order->credit_applied, ''),
            'total_taxes' => (float) money($order->tax, ''),
            'total_fees' => (float) money($order->fees_subtotal, ''),
            'total_shipping' => (float) money($order->delivery_fee, ''),
            'currency' => DripShopperActivity::CURRENCY,
            'order_url' => 'https://test.com/account/orders/' . $order->id,
            'items' => $order_items
                ->map(fn(OrderItem $item) => DripShopperActivity::orderItemToDripArray($item, 'test.com'))
                ->toArray()
        ], DripShopperActivity::toOrder($order, 'test.com'));
    }

    #[Test]
    public function it_converts_an_order_item_and_domain_to_shopper_activty_order_item(): void
    {
        $product = Product::factory()->create();
        $order_item = OrderItem::factory()->create(['product_id' => $product->id, 'unit_price' => 120000, 'qty' => 2]);

        $this->assertEquals([
            'product_id' => (string) $product->id,
            'sku' => $product->sku ?? '',
            'name' => $product->title ?? '',
            'price' => (float) money($order_item->price, ''),
            'quantity' => $order_item->qty,
            'product_url' => 'https://test.com/store/product/' . $product->slug,
            'image_url' => $product->cover_photo ?? '',
        ], DripShopperActivity::orderItemToDripArray($order_item, 'test.com'));
    }
}
