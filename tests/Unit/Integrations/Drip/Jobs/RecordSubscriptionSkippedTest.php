<?php

namespace Tests\Unit\Integrations\Drip\Jobs;

use App\Integrations\Drip\Drip;
use App\Integrations\Drip\Jobs\RecordSubscriptionSkipped;
use App\Models\Integration;
use App\Models\Order;
use App\Models\RecurringOrder;
use App\Models\User;
use Mockery\MockInterface;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class RecordSubscriptionSkippedTest extends TenantTestCase
{
    #[Test]
    public function it_sends_the_expected_drip_request(): void
    {
        Integration::factory()->create([
            'name' => 'drip',
            'enabled' => true,
            'settings' => [
                'api_key' => 'abc123',
                'account_id' => 'def456'
            ]
        ]);

        $subscription = RecurringOrder::factory()->create(['skip_count' => 12]);
        Order::factory(2)->create(['blueprint_id' => $subscription->id, 'confirmed' => true, 'canceled' => false]);

        $this->mock(Drip::class, function (MockInterface $mock) use ($subscription) {
            $mock->shouldReceive('configure')->andReturnSelf();
            $mock->shouldReceive('recordEvent')->once()->with(
                \Mockery::on(fn($arg) => $arg === $subscription->customer->email),
                'Skipped Subscription',
                ['skip_count' => 12]
            )->andReturnUndefined();
            $mock->shouldReceive('patchSubscriber')->once()->with(
                \Mockery::on(fn(User $arg) => $arg->id === $subscription->customer_id),
                [
                    'subscription_order_count' => 2,
                    'subscription_skip_count' => 12
                ]
            )->andReturnUndefined();
        });

        (new RecordSubscriptionSkipped($subscription->id))->handle();
    }
}