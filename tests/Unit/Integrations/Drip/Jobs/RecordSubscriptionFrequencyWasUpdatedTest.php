<?php

namespace Tests\Unit\Integrations\Drip\Jobs;

use App\Integrations\Drip\Drip;
use App\Integrations\Drip\Jobs\RecordSubscriptionFrequencyWasUpdated;
use App\Models\Integration;
use App\Models\RecurringOrder;
use App\Models\User;
use Mo<PERSON>y\MockInterface;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class RecordSubscriptionFrequencyWasUpdatedTest extends TenantTestCase
{
    #[Test]
    public function it_records_expected_event_to_drip_when_subscription_frequency_is_updated(): void
    {
        Integration::factory()->create([
            'name' => 'drip',
            'settings' => [
                'api_key' => 'abc123',
                'account_id' => 'def456'
            ]
        ]);

        $recurring_order = RecurringOrder::factory()->create(['reorder_frequency' => 7]);

        $expected_customer_email = $recurring_order->customer->email;

        $this->mock(Drip::class, function (MockInterface $mock) use ($expected_customer_email) {
            $mock->shouldReceive('configure')->once()
                ->with('abc123', 'def456')->andReturnSelf();

            $mock->shouldReceive('recordEvent')->once()->with(
                \Mockery::on(fn ($arg) => $arg === $expected_customer_email),
                'Subscription delivery frequency updated',
                [
                    'old_frequency' => 5,
                    'new_frequency' => 7,
                ]
            )->andReturn(true);
        });

        (new RecordSubscriptionFrequencyWasUpdated($recurring_order->id, 5))->handle();
    }

    #[Test]
    public function it_does_not_send_an_event_to_drip_when_subscription_is_invalid(): void
    {
        Integration::factory()->create([
            'name' => 'drip',
            'settings' => [
                'api_key' => 'abc123',
                'account_id' => 'def456'
            ]
        ]);

        $this->mock(Drip::class, function (MockInterface $mock) {
            $mock->shouldReceive('configure')->once()
                ->with('abc123', 'def456')->andReturnSelf();

            $mock->shouldNotReceive('recordEvent');
        });

        (new RecordSubscriptionFrequencyWasUpdated(*********, 5))->handle();
    }
}
