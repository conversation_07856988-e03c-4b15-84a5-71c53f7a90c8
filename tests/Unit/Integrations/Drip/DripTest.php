<?php

namespace Tests\Unit\Integrations\Drip;

use App\Integrations\Drip\Drip;
use App\Models\Address;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Pickup;
use App\Models\RecurringOrder;
use App\Models\Schedule;
use App\Models\User;
use App\Support\Enums\UserRole;
use Carbon\Carbon;
use Illuminate\Http\Client\Request;
use Illuminate\Support\Facades\Http;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class DripTest extends TenantTestCase
{
    #[Test]
    public function it_can_be_configured(): void
    {
        $drip = (new Drip)->configure('abc_123', 'def_456');

        $this->assertInstanceOf(Drip::class, $drip);
    }

    #[Test]
    public function it_makes_the_expected_accounts_request(): void
    {
        $expected_url = 'https://api.getdrip.com/v2/accounts';
        $expected_result = ['accounts' => [['id' => '123']]];

        Http::fake([
            $expected_url => Http::response($expected_result),
        ]);

        $drip = (new Drip)->configure('abc_123', '');

        $this->assertEquals($expected_result, $drip->accounts());

        Http::assertSent(function (Request $request) use ($expected_url) {
            $auth = base64_encode('abc_123:');
            return $request->hasHeader('Authorization', "Basic {$auth}")
                && $request->hasHeader('User-Agent', 'Seven Sons (sevensons.net)')
                && $request->url() === $expected_url
                && $request->method() === 'GET';
        });
    }

    #[Test]
    public function it_throws_exception_when_creating_or_updating_subscriber_without_an_account_id(): void
    {
        $this->assertThrows(
            fn() => (new Drip)->configure('abc_123', '')->createOrUpdateSubscriber([]),
            \Exception::class,
            'Drip account ID not configured.'
        );
    }

    #[Test]
    public function it_makes_the_expected_create_or_update_subscriber_request(): void
    {
        $expected_url = 'https://api.getdrip.com/v2/def_456/subscribers';
        $expected_subscriber = ['id' => '123'];
        $expected_result = ['subscribers' => [['id' => '321']]];

        Http::fake([
            $expected_url => Http::response($expected_result),
        ]);

        $drip = (new Drip)->configure('abc_123', 'def_456');

        $this->assertEquals($expected_result, $drip->createOrUpdateSubscriber($expected_subscriber));

        Http::assertSent(function (Request $request) use ($expected_url, $expected_subscriber) {
            $auth = base64_encode('abc_123:');
            return $request->hasHeader('Authorization', "Basic {$auth}")
                && $request->hasHeader('User-Agent', 'Seven Sons (sevensons.net)')
                && $request->url() === $expected_url
                && $request->method() === 'POST'
                && $request->data() === ['subscribers' => [$expected_subscriber]];
        });
    }

    #[Test]
    public function it_throws_exception_when_unsubscribing_subscriber_without_an_account_id(): void
    {
        $this->assertThrows(
            fn() => (new Drip)->configure('abc_123', '')->unsubscribe(User::factory()->make(), 'some id'),
            \Exception::class,
            'Drip account ID not configured.'
        );
    }

    #[Test]
    public function it_makes_the_expected_unsubscribe_subscriber_request_with_a_campaign(): void
    {
        $user = User::factory()->make();
        $encoded_email = urlencode($user->email);

        $campaign_id = 'some campaign';
        $encoded_campaign_id = urlencode($campaign_id);

        $expected_url = "https://api.getdrip.com/v2/def_456/subscribers/{$encoded_email}/remove?campaign_id={$encoded_campaign_id}";
        $expected_subscriber = ['id' => '321'];

        Http::fake([
            $expected_url => Http::response(['subscribers' => [$expected_subscriber]]),
        ]);

        $drip = (new Drip)->configure('abc_123', 'def_456');

        $drip->unsubscribe($user, $campaign_id);

        Http::assertSent(function (Request $request) use ($expected_url, $expected_subscriber) {
            $auth = base64_encode('abc_123:');
            return $request->hasHeader('Authorization', "Basic {$auth}")
                && $request->hasHeader('User-Agent', 'Seven Sons (sevensons.net)')
                && $request->url() === $expected_url
                && $request->method() === 'POST';
        });
    }

    #[Test]
    public function it_makes_the_expected_unsubscribe_subscriber_request_without_a_campaign(): void
    {
        $user = User::factory()->make();
        $encoded_email = urlencode($user->email);

        $expected_url = "https://api.getdrip.com/v2/def_456/subscribers/{$encoded_email}/remove?campaign_id=";
        $expected_subscriber = ['id' => '321'];

        Http::fake([
            $expected_url => Http::response(['subscribers' => [$expected_subscriber]]),
        ]);

        $drip = (new Drip)->configure('abc_123', 'def_456');

        $drip->unsubscribe($user);

        Http::assertSent(function (Request $request) use ($expected_url, $expected_subscriber) {
            $auth = base64_encode('abc_123:');
            return $request->hasHeader('Authorization', "Basic {$auth}")
                && $request->hasHeader('User-Agent', 'Seven Sons (sevensons.net)')
                && $request->url() === $expected_url
                && $request->method() === 'POST';
        });
    }

    #[Test]
    public function it_throws_exception_when_tagging_subscriber_without_an_account_id(): void
    {
        $this->assertThrows(
            fn() => (new Drip)->configure('abc_123', '')->tagSubscriber('<EMAIL>', 'some tag'),
            \Exception::class,
            'Drip account ID not configured.'
        );
    }

    #[Test]
    public function it_makes_the_expected_tag_subscriber_request(): void
    {
        $expected_url = "https://api.getdrip.com/v2/def_456/tags";

        Http::fake([
            $expected_url => Http::response(null, 201),
        ]);

        $drip = (new Drip)->configure('abc_123', 'def_456');

        $this->assertTrue($drip->tagSubscriber('<EMAIL>', 'some tag'));

        Http::assertSent(function (Request $request) use ($expected_url) {
            $auth = base64_encode('abc_123:');
            return $request->hasHeader('Authorization', "Basic {$auth}")
                && $request->hasHeader('User-Agent', 'Seven Sons (sevensons.net)')
                && $request->url() === $expected_url
                && $request->method() === 'POST'
                && $request->data() === ['tags' => [['email' => '<EMAIL>', 'tag' => 'some tag']]];
        });
    }

    #[Test]
    public function it_throws_exception_when_recording_event_without_an_account_id(): void
    {
        $this->assertThrows(
            fn() => (new Drip)->configure('abc_123', '')->recordEvent(User::factory()->create()->email, 'action', []),
            \Exception::class,
            'Drip account ID not configured.'
        );
    }

    #[Test]
    public function it_makes_the_expected_record_event_request(): void
    {
        Carbon::setTestNow(now());

        $user = User::factory()->make();
        $properties = ['some' => 'props'];
        $expected_url = "https://api.getdrip.com/v2/def_456/events";

        Http::fake([
            $expected_url => Http::response(null, 204),
        ]);

        $drip = (new Drip)->configure('abc_123', 'def_456');

        $drip->recordEvent($user->email, 'some action', $properties);

        Http::assertSent(function (Request $request) use ($expected_url, $user, $properties) {
            $auth = base64_encode('abc_123:');
            return $request->hasHeader('Authorization', "Basic {$auth}")
                && $request->hasHeader('User-Agent', 'Seven Sons (sevensons.net)')
                && $request->url() === $expected_url
                && $request->method() === 'POST'
                && $request->data() === ['events' => [
                    [
                        'email' => $user->email,
                        'action' => 'some action',
                        'properties' => $properties,
                        'occurred_at' => now()->toIso8601String(),
                    ]
                ]];
        });

        Carbon::setTestNow();
    }


    #[Test]
    public function it_can_determine_if_the_configuration_is_valid(): void
    {
        $drip = (new Drip)->configure('', '');
        $this->assertFalse($drip->configurationIsValid());

        $drip = (new Drip)->configure('abc_123', '');
        $this->assertFalse($drip->configurationIsValid());

        $drip = (new Drip)->configure('', 'def_456');
        $this->assertFalse($drip->configurationIsValid());

        $expected_url = "https://api.getdrip.com/v2/accounts/def_456";

        Http::fake([
            $expected_url => Http::response(null, 401),
        ]);

        $drip = (new Drip)->configure('abc_123', 'def_456');
        $this->assertFalse($drip->configurationIsValid());

        Http::assertSent(function (Request $request) use ($expected_url) {
            $auth = base64_encode('abc_123:');
            return $request->hasHeader('Authorization', "Basic {$auth}")
                && $request->hasHeader('User-Agent', 'Seven Sons (sevensons.net)')
                && $request->url() === $expected_url
                && $request->method() === 'GET';
        });

        $expected_url = "https://api.getdrip.com/v2/accounts/xyz_456";

        Http::fake([
            $expected_url => Http::response(null, 201),
        ]);

        $drip = (new Drip)->configure('abc_123', 'xyz_456');
        $this->assertTrue($drip->configurationIsValid());

        Http::assertSent(function (Request $request) use ($expected_url) {
            $auth = base64_encode('abc_123:');
            return $request->hasHeader('Authorization', "Basic {$auth}")
                && $request->hasHeader('User-Agent', 'Seven Sons (sevensons.net)')
                && $request->url() === $expected_url
                && $request->method() === 'GET';
        });
    }

    #[Test]
    public function it_makes_the_expected_batch_record_event_request(): void
    {
        Carbon::setTestNow(now());

        $user = User::factory()->make();
        $expected_url = "https://api.getdrip.com/v2/def_456/events/batches";

        Http::fake([
            $expected_url => Http::response(null, 204),
        ]);

        $drip = (new Drip)->configure('abc_123', 'def_456');

        $events = [
            ['email' => '<EMAIL>', 'action' => 'some action', 'properties' => ['some' => 'props1']],
            ['email' => '<EMAIL>', 'action' => 'some action', 'properties' => ['some' => 'props2']]
        ];

        $drip->recordBatchOfEvents($events);

        Http::assertSent(function (Request $request) use ($expected_url, $user, $events) {
            $auth = base64_encode('abc_123:');
            return $request->hasHeader('Authorization', "Basic {$auth}")
                && $request->hasHeader('User-Agent', 'Seven Sons (sevensons.net)')
                && $request->url() === $expected_url
                && $request->method() === 'POST'
                && $request->data() === ['batches' => [['events' => $events]]];
        });

        Carbon::setTestNow();
    }

    #[Test]
    public function it_makes_the_expected_batch_subscriber_updates_request(): void
    {
        Carbon::setTestNow(now());

        $user = User::factory()->make();
        $expected_url = "https://api.getdrip.com/v2/def_456/subscribers/batches";

        Http::fake([
            $expected_url => Http::response(null, 204),
        ]);

        $drip = (new Drip)->configure('abc_123', 'def_456');

        $subscribers = [
            ['email' => '<EMAIL>', 'tags' => ['some' => 'props1']],
            ['email' => '<EMAIL>', 'tags' => ['some' => 'props2']]
        ];

        $drip->recordBatchOfSubscriberUpdates($subscribers);

        Http::assertSent(function (Request $request) use ($expected_url, $user, $subscribers) {
            $auth = base64_encode('abc_123:');
            return $request->hasHeader('Authorization', "Basic {$auth}")
                && $request->hasHeader('User-Agent', 'Seven Sons (sevensons.net)')
                && $request->url() === $expected_url
                && $request->method() === 'POST'
                && $request->data() === ['batches' => [['subscribers' => $subscribers]]];
        });

        Carbon::setTestNow();
    }


    #[Test]
    public function it_can_convert_an_one_time_order_to_an_order_confirmed_event(): void
    {
        $today = today();
        $user = User::factory()->create();
        $schedule = Schedule::factory()->create();
        $pickup = Pickup::factory()->create();
        $order = Order::factory()->create([
            'customer_id' => $user->id,
            'first_time_order' => false,
            'schedule_id' => $schedule->id,
            'pickup_id' => $pickup->id,
            'pickup_date' => $today->format('Y-m-d'),
            'total' => 1234,
            'blueprint_id' => null
        ]);

        $this->assertEquals([
            'email' => $user->email,
            'action' => 'Confirmed order',
            'properties' => [
                'is_recurring' => 'No',
                'order_id' => $order->id,
                'order_total' => 1234,
                'schedule_id' => $schedule->id,
                'fulfillment_id' => $pickup->id,
                'pickup_date' => $today->format('m/d/y')
            ]
        ], Drip::toConfirmedOrderEvent($order));
    }

    #[Test]
    public function it_can_convert_a_subscription_order_to_an_order_confirmed_event(): void
    {
        $today = today();
        $user = User::factory()->create();
        $schedule = Schedule::factory()->create();
        $pickup = Pickup::factory()->create();
        $subscription = RecurringOrder::factory()->create();
        $order = Order::factory()->create([
            'customer_id' => $user->id,
            'first_time_order' => false,
            'schedule_id' => $schedule->id,
            'pickup_id' => $pickup->id,
            'pickup_date' => $today->format('Y-m-d'),
            'total' => 1234,
            'blueprint_id' => $subscription->id
        ]);

        $this->assertEquals([
            'email' => $user->email,
            'action' => 'Confirmed recurring order',
            'properties' => [
                'is_recurring' => 'Yes',
                'order_id' => $order->id,
                'order_total' => 1234,
                'schedule_id' => $schedule->id,
                'fulfillment_id' => $pickup->id,
                'pickup_date' => $today->format('m/d/y')
            ]
        ], Drip::toConfirmedOrderEvent($order));
    }

    #[Test]
    public function it_can_convert_a_first_time_order_to_an_order_confirmed_event(): void
    {
        $today = today();
        $user = User::factory()->create();
        $schedule = Schedule::factory()->create();
        $pickup = Pickup::factory()->create();
        $subscription = RecurringOrder::factory()->create();
        $order = Order::factory()->create([
            'customer_id' => $user->id,
            'first_time_order' => true,
            'schedule_id' => $schedule->id,
            'pickup_id' => $pickup->id,
            'pickup_date' => $today->format('Y-m-d'),
            'total' => 1234,
            'blueprint_id' => $subscription->id
        ]);

        $this->assertEquals([
            'email' => $user->email,
            'action' => 'Confirmed first order',
            'properties' => [
                'order_id' => $order->id,
                'order_total' => 1234,
                'schedule_id' => $schedule->id,
                'fulfillment_id' => $pickup->id,
                'pickup_date' => $today->format('m/d/y')
            ]
        ], Drip::toConfirmedOrderEvent($order));
    }

    #[Test]
    public function it_converts_an_order_to_subscriber_with_purchase_without_default_address(): void
    {
        $schedule = Schedule::factory()->create();
        $pickup = Pickup::factory()->create(['schedule_id' => $schedule->id]);
        $customer = User::factory()->create([
            'pickup_point' => $pickup->id,
            'zip' => '',
            'city' => '',
            'state' => '',
            'street' => '',
            'street_2' => '',
            'country' => '',
        ]);
        $order = Order::factory()->create(['customer_id' => $customer->id]);
        $order_items = OrderItem::factory()->count(2)->create(['order_id' => $order->id]);

        // to ensure correct order of array for the test
        $order->setRelation('items', $order_items);

        $this->assertEquals([
            'email' => $customer->email,
            'custom_fields' => [
                'role' => UserRole::CUSTOMER->label(),
                'first_name' => $customer->first_name,
                'last_name' => $customer->last_name,
                'postal_code' => '',
                'city' => '',
                'state' => '',
                'street' => '',
                'street_2' => '',
                'country' => '',
                'referral_code' => $customer->referral_code,
                'referral_url' => $customer->referral_url,
                'fulfillment_id' => $customer->pickup_point,
                'fulfillment_name' => $pickup->title,
                'fulfillment_display_name' => $pickup->display_name,
                'schedule_id' => $schedule->id,
                'store_credit' => $customer->credit,
                'is_active' => 'Yes',
                'last_purchase' => $order->created_at->format('m/d/y'),
                'last_purchase_timestamp' => $order->created_at->timestamp,
                'order_count' => $customer->order_count ?? 0,
                'subscription_order_count' => $customer->recurring_order_count ?? 0,
                'newsletter_recipient' => 'No',
                'notifications_recipient' => 'Yes',
                'alt_email' => $customer->email_alt,
                'sms_marketing_status' => null,
            ],
            'tags' => [
                'Buyer',
                'Purchased - ' . $order_items->first()->title . ' #' . $order_items->first()->product_id,
                'Purchased - ' . $order_items->last()->title . ' #' . $order_items->last()->product_id
            ]
        ], Drip::toSubscriberWithPurchase($order));
    }

    #[Test]
    public function it_converts_an_order_to_subscriber_with_purchase_with_default_address(): void
    {
        $schedule = Schedule::factory()->create();
        $pickup = Pickup::factory()->create(['schedule_id' => $schedule->id]);
        $customer = User::factory()->create([
            'pickup_point' => $pickup->id,
            'zip' => '',
            'city' => '',
            'state' => '',
            'street' => '',
            'street_2' => '',
            'country' => '',
        ]);

        $address = Address::factory()->create();

        $customer->addresses()->attach($address->id, ['is_default' => true, 'street_2' => 'Apt 123']);

        $order = Order::factory()->create(['customer_id' => $customer->id]);
        $order_items = OrderItem::factory()->count(2)->create(['order_id' => $order->id]);

        // to ensure correct order of array for the test
        $order->setRelation('items', $order_items);

        $this->assertEquals([
            'email' => $customer->email,
            'custom_fields' => [
                'role' => UserRole::CUSTOMER->label(),
                'first_name' => $customer->first_name,
                'last_name' => $customer->last_name,
                'postal_code' => $address->postal_code,
                'city' => $address->city,
                'state' => $address->state,
                'street' => $address->street,
                'street_2' => 'Apt 123',
                'country' => $address->country,
                'referral_code' => $customer->referral_code,
                'referral_url' => $customer->referral_url,
                'fulfillment_id' => $customer->pickup_point,
                'fulfillment_name' => $pickup->title,
                'fulfillment_display_name' => $pickup->display_name,
                'schedule_id' => $schedule->id,
                'store_credit' => $customer->credit,
                'is_active' => 'Yes',
                'last_purchase' => $order->created_at->format('m/d/y'),
                'last_purchase_timestamp' => $order->created_at->timestamp,
                'order_count' => $customer->order_count ?? 0,
                'subscription_order_count' => $customer->recurring_order_count ?? 0,
                'newsletter_recipient' => 'No',
                'notifications_recipient' => 'Yes',
                'alt_email' => $customer->email_alt,
                'sms_marketing_status' => null,
            ],
            'tags' => [
                'Buyer',
                'Purchased - ' . $order_items->first()->title . ' #' . $order_items->first()->product_id,
                'Purchased - ' . $order_items->last()->title . ' #' . $order_items->last()->product_id
            ]
        ], Drip::toSubscriberWithPurchase($order));
    }

    #[Test]
    public function it_convert_an_order_an_item_to_purchased_product_event(): void
    {
        Carbon::setTestNow(now());

        $customer = User::factory()->create();
        $order = Order::factory()->create(['customer_id' => $customer->id]);
        $order_item = OrderItem::factory()->create(['order_id' => $order->id]);
        
        $this->assertEquals([
            'email' => $customer->email,
            'action' => 'Purchased product',
            'properties' => [
                'product_name' => $order_item->title,
                'product_id' => $order_item->product_id,
                'value' => $order_item->subtotal,
                'order_id' => $order_item->order_id,
                'order_item_id' => $order_item->id,
                'quantity' => $order_item->qty,
                'fulfilled_quantity' => $order_item->qty,
                'stock_status' => 'full',
                'unit_of_issue' => $order_item->unit_of_issue,
                'unit_price' => $order_item->unit_price,
                'weight' => $order_item->weight,
                'sku' => $order_item->product->sku,
                'vendor_id' => $order_item->product->vendor_id,
                'vendor' => $order_item->product->vendor?->title,
                'on_sale' => 'No',
                'first_time_order' => 'No',
                'schedule_id' => $order->schedule_id,
                'fulfillment_id' => $order->pickup_id,
                'pickup_date' => $order->pickup_date?->format('m/d/y'),
            ],
            'occurred_at' => now()->toIso8601String(),
        ], Drip::toPurchasedProductEvent($order, $order_item));

        Carbon::setTestNow();
    }
}
