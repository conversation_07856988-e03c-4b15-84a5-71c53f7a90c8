<?php

namespace Tests\Unit\Presenters;

use App\Models\Order;
use App\Models\Pickup;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class OrderPresenterTest extends TenantTestCase
{
    #[Test]
    public function it_can_present_a_pickup_delivery_fee_a_pickup_location(): void
    {
        /** @var Order $order */
        $order = Order::factory()->create(['pickup_id' => 0]);

        $this->assertEquals('0.00', $order->present()->pickupDeliveryFeeWithoutCap());
    }

    #[Test]
    public function it_can_present_a_fixed_amount_pickup_delivery_fee_without_cap(): void
    {
        $pickup = Pickup::factory()->create(['delivery_rate' => 22.34, 'settings' => ['delivery_fee_type' => '2']]);
        /** @var Order $order */
        $order = Order::factory()->create(['pickup_id' => $pickup->id]);

        $this->assertEquals('22.34', $order->present()->pickupDeliveryFeeWithoutCap());
    }

    #[Test]
    public function it_can_present_a_weight_based_pickup_delivery_fee_without_cap(): void
    {
        $pickup = Pickup::factory()->create(['delivery_rate' => '0.1', 'settings' => ['delivery_fee_type' => '1']]);
        /** @var Order $order */
        $order = Order::factory()->create(['pickup_id' => $pickup->id, 'weight' => '11.23']);

        $this->assertEquals('1.12', $order->present()->pickupDeliveryFeeWithoutCap());
    }
}