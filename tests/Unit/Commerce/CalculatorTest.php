<?php

namespace Tests\Unit\Commerce;

use App\Commerce\Calculator;
use App\Commerce\CreditManager;
use App\Models\Order;
use App\Models\OrderFee;
use App\Models\OrderItem;
use App\Models\Pickup;
use App\Models\PickupFee;
use App\Models\Product;
use App\Models\User;
use App\Support\Enums\OrderStatus;
use App\Support\Enums\ProductType;
use Mockery\MockInterface;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class CalculatorTest extends TenantTestCase
{
    #[Test]
    function it_can_sum_items_of_confirmed_orders(): void
    {
        $order = Order::factory()->create([
            'confirmed' => true,
            'subtotal' => 50,
            'original_subtotal' => 50
        ]);

        $items = OrderItem::factory()->count(2)->create([
            'order_id' => $order->id,
            'qty' => 2,
            'unit_price' => 250,
            'subtotal' => 0,
            'discount_type' => 'fixed',
            'discount' => 50
        ]);

        $order->setRelation('items', $items);

        $calculator = new Calculator($order);

        $result = $calculator->sumItems();

        $this->assertInstanceOf(Calculator::class, $result);

        $calculated_order = $result->getOrder();

        $this->assertTrue($calculated_order->isDirty());
        $this->assertEquals(900, $calculated_order->subtotal); // (250*2 * 2) - (50*2)
        $this->assertEquals(50, $calculated_order->original_subtotal);

        $calculated_order->items->each(function (OrderItem $item) {
            $this->assertEquals(450, $item->subtotal); // (250*2) - (50)
        });
    }

    #[Test]
    function it_can_sum_items_of_unconfirmed_orders(): void
    {
        $order = Order::factory()->create(['confirmed' => false, 'subtotal' => 50, 'original_subtotal' => 50]);
        $items = OrderItem::factory()->count(2)->create([
            'order_id' => $order->id,
            'qty' => 2,
            'unit_price' => 250,
            'subtotal' => 0,
            'discount_type' => 'fixed',
            'discount' => 50
        ]);

        $order->setRelation('items', $items);

        $calculator = new Calculator($order);

        $result = $calculator->sumItems();

        $this->assertInstanceOf(Calculator::class, $result);

        $calculated_order = $result->getOrder();

        $this->assertEquals(900, $calculated_order->subtotal);  // (250*2 * 2) - (50*2)
        $this->assertEquals(900, $calculated_order->original_subtotal); // original updated when order is not confirmed

        $calculated_order->items->each(function (OrderItem $item) {
            $this->assertEquals(450, $item->subtotal); // (250*2) - (50)
        });
    }

    #[Test]
    function it_can_sum_weights_of_confirmed_orders(): void
    {
        $order = Order::factory()->create(['confirmed' => true, 'weight' => 6, 'original_weight' => 6]);
        $items = OrderItem::factory()->count(2)->create(['weight' => 5, 'original_weight' => 3]);
        $order->setRelation('items', $items);

        $calculator = new Calculator($order);

        $result = $calculator->sumWeight();

        $this->assertInstanceOf(Calculator::class, $result);

        $calculated_order = $result->getOrder();

        $this->assertEquals(10, $calculated_order->weight);
        $this->assertEquals(6, $calculated_order->original_weight);
    }

    #[Test]
    function it_can_sum_weights_of_unconfirmed_orders(): void
    {
        $order = Order::factory()->create(['confirmed' => false, 'weight' => 6, 'original_weight' => 6]);
        $items = OrderItem::factory()->count(2)->create(['weight' => 5, 'original_weight' => 3]);
        $order->setRelation('items', $items);

        $calculator = new Calculator($order);

        $result = $calculator->sumWeight();

        $this->assertInstanceOf(Calculator::class, $result);

        $calculated_order = $result->getOrder();

        $this->assertEquals(10, $calculated_order->weight);
        $this->assertEquals(10, $calculated_order->original_weight);
    }

    #[Test]
    function it_can_calculate_delivery_fee_of_confirmed_order_when_delivery_rate_is_fixed(): void
    {
        $order = Order::factory()->create(['confirmed' => true, 'delivery_rate' => 600, 'delivery_fee_type' => 2]); // fee type 2 = fixed

        $calculator = new Calculator($order);

        $this->assertEquals(600, $calculator->calculateDeliveryFee());
    }

    #[Test]
    function it_can_calculate_delivery_fee_of_confirmed_order_when_delivery_rate_is_variable(): void
    {
        $order = Order::factory()->create(['confirmed' => true, 'delivery_rate' => 1.5, 'delivery_fee_type' => 1, 'weight' => 20]);

        $calculator = new Calculator($order);

        $this->assertEquals(30, $calculator->calculateDeliveryFee()); // 1.5 * 20
    }

    #[Test]
    function it_can_calculate_delivery_fee_of_an_unconfirmed_order_when_delivery_rate_is_fixed(): void
    {
        $order = Order::factory()->create(['confirmed' => false, 'delivery_rate' => 600, 'delivery_fee_type' => 1]);

        $pickup = Pickup::factory()->create(['delivery_rate' => 100]); // before persist, this value is converted to cents (value * 100)
        $pickup->settings = ['delivery_fee_type' => 2];
        $order->setRelation('pickup', $pickup);

        $calculator = new Calculator($order);

        $this->assertEquals(10000, $calculator->calculateDeliveryFee());
    }

    #[Test]
    function it_can_calculate_delivery_fee_of_an_unconfirmed_order_when_delivery_rate_is_variable(): void
    {
        $order = Order::factory()->create(['confirmed' => false, 'delivery_rate' => 600, 'delivery_fee_type' => 2, 'weight' => 20]);

        $pickup = Pickup::factory()->create(['delivery_rate' => 1.5]);
        $pickup->settings = ['delivery_fee_type' => 1];
        $order->setRelation('pickup', $pickup);

        $calculator = new Calculator($order);

        $this->assertEquals(3000, $calculator->calculateDeliveryFee()); // 20 * 1.5
    }

    #[Test]
    function it_can_sum_delivery_fee_when_customer_is_exempt_from_fees(): void
    {
        $order = Order::factory()->create(['delivery_fee' => 10]);

        $items = OrderItem::factory()->count(2)->create();
        $order->setRelation('items', $items);

        $customer = User::factory()->create(['exempt_from_fees' => 1]);
        $order->setRelation('customer', $customer);

        $pickup = Pickup::factory()->create();
        $order->setRelation('pickup', $pickup);

        $calculator = new Calculator($order);

        $result = $calculator->sumDeliveryFee();

        $this->assertInstanceOf(Calculator::class, $result);

        $calculated_order = $result->getOrder();

        $this->assertEquals(0, $calculated_order->delivery_fee);
    }

    #[Test]
    function it_can_sum_delivery_fee_when_it_has_no_pickup_point(): void
    {
        $order = Order::factory()->create(['delivery_fee' => 10]);

        $items = OrderItem::factory()->count(2)->create();
        $order->setRelation('items', $items);

        $customer = User::factory()->create(['exempt_from_fees' => 0]);
        $order->setRelation('customer', $customer);

        $order->setRelation('pickup', null);

        $calculator = new Calculator($order);

        $result = $calculator->sumDeliveryFee();

        $this->assertInstanceOf(Calculator::class, $result);

        $calculated_order = $result->getOrder();

        $this->assertEquals(0, $calculated_order->delivery_fee);
    }

    #[Test]
    function it_can_sum_delivery_fee_for_a_confirmed_new_order_whose_delivery_fee_is_equal_to_delivery_fee_cap(): void
    {
        $order = Order::factory()->create([
            'confirmed' => true,
            'delivery_fee' => 1000,
            'status_id' => OrderStatus::processing()
        ]);

        $customer = User::factory()->create(['exempt_from_fees' => 0]);
        $order->setRelation('customer', $customer);

        $pickup = Pickup::factory()->create(['delivery_fee_cap' => 10, 'delivery_rate' => 1.5]);
        $pickup->settings = ['delivery_fee_type' => 2]; // fixed
        $order->setRelation('pickup', $pickup);

        $calculator = new Calculator($order);

        $result = $calculator->sumDeliveryFee();

        $this->assertInstanceOf(Calculator::class, $result);

        $calculated_order = $result->getOrder();

        $this->assertEquals(1000, $calculated_order->delivery_fee); // should remain unchanged
    }

    #[Test]
    function it_can_sum_delivery_fee_for_a_confirmed_order_whose_delivery_fee_meets_the_pickup_delivery_cap(): void
    {
        $order = Order::factory()->create([
            'confirmed' => true,
            'status_id' => OrderStatus::processing(),
            'delivery_fee' => 1000,
            'subtotal' => 2000,
            'delivery_rate' => 1500,
            'delivery_fee_type' => 2,
        ]);

        $customer = User::factory()->create(['exempt_from_fees' => 0]);
        $order->setRelation('customer', $customer);

        $pickup = Pickup::factory()->create([
            'delivery_fee_cap' => 5, // 500 cents
            'delivery_total_threshold' => 19, // 1900 cents
            'apply_limit' => true
        ]);
        $pickup->settings = ['delivery_fee_type' => 2]; // fixed
        $order->setRelation('pickup', $pickup);

        $calculator = new Calculator($order);

        $result = $calculator->sumDeliveryFee();

        $this->assertInstanceOf(Calculator::class, $result);

        $calculated_order = $result->getOrder();

        $this->assertEquals(500, $calculated_order->delivery_fee); // delivery fee is equal to pickup cap
    }

    #[Test]
    function it_can_sum_delivery_fee_for_an_unconfirmed_order_whose_delivery_fee_meets_the_pickup_delivery_cap_but_limit_is_not_applied(): void
    {
        $order = Order::factory()->create([
            'confirmed' => true,
            'status_id' => OrderStatus::processing(),
            'delivery_fee' => 1000,
            'subtotal' => 2000,
            'delivery_rate' => 1500,
            'delivery_fee_type' => 2,
        ]);

        $customer = User::factory()->create(['exempt_from_fees' => 0]);
        $order->setRelation('customer', $customer);

        $pickup = Pickup::factory()->create([
            'delivery_fee_cap' => 5, // 500 cents
            'delivery_total_threshold' => 19, // 1900 cents
            'apply_limit' => false
        ]);
        $pickup->settings = ['delivery_fee_type' => 2]; // fixed
        $order->setRelation('pickup', $pickup);

        $calculator = new Calculator($order);

        $result = $calculator->sumDeliveryFee();

        $this->assertInstanceOf(Calculator::class, $result);

        $calculated_order = $result->getOrder();

        $this->assertEquals(1500, $calculated_order->delivery_fee); // delivery fee is equal to pickup delivery rate
    }

    #[Test]
    function it_can_sum_delivery_fee_for_an_unconfirmed_order_whose_delivery_fee_meets_the_pickup_delivery_cap_but_subtotal_doesnt_meet_threshold(): void
    {
        $order = Order::factory()->create([
            'confirmed' => true,
            'status_id' => OrderStatus::processing(),
            'delivery_fee' => 1000,
            'subtotal' => 2000,
            'delivery_rate' => 1500,
            'delivery_fee_type' => 2,
        ]);

        $customer = User::factory()->create(['exempt_from_fees' => 0]);
        $order->setRelation('customer', $customer);

        $pickup = Pickup::factory()->create([
            'delivery_fee_cap' => 5, // 500 cents
            'delivery_total_threshold' => 2100, // 2100 cents
            'apply_limit' => true
        ]);
        $pickup->settings = ['delivery_fee_type' => 2]; // fixed
        $order->setRelation('pickup', $pickup);

        $calculator = new Calculator($order);

        $result = $calculator->sumDeliveryFee();

        $this->assertInstanceOf(Calculator::class, $result);

        $calculated_order = $result->getOrder();

        $this->assertEquals(1500, $calculated_order->delivery_fee); // delivery fee is equal to pickup delivery rate
    }

    #[Test]
    function it_can_sum_delivery_fee_for_an_unconfirmed_order_whose_delivery_fee_is_less_than_cap(): void
    {
        $order = Order::factory()->create([
            'confirmed' => true,
            'status_id' => OrderStatus::processing(),
            'delivery_fee' => 1000,
            'subtotal' => 2000,
            'delivery_rate' => 1500,
            'delivery_fee_type' => 2,
        ]);

        $customer = User::factory()->create(['exempt_from_fees' => 0]);
        $order->setRelation('customer', $customer);

        $pickup = Pickup::factory()->create([
            'delivery_fee_cap' => 20, // 2000 cents
            'delivery_total_threshold' => 500, // 500 cents
            'apply_limit' => true
        ]);
        $pickup->settings = ['delivery_fee_type' => 2]; // fixed
        $order->setRelation('pickup', $pickup);

        $calculator = new Calculator($order);

        $result = $calculator->sumDeliveryFee();

        $this->assertInstanceOf(Calculator::class, $result);

        $calculated_order = $result->getOrder();

        $this->assertEquals(1500, $calculated_order->delivery_fee); // delivery fee is equal to pickup delivery rate
    }

    #[Test]
    function it_can_sum_delivery_fee_for_a_confirmed_new_order_whose_delivery_fee_meets_the_pickup_delivery_cap(): void
    {
        $order = Order::factory()->create([
            'confirmed' => true,
            'status_id' => OrderStatus::confirmed(),
            'delivery_fee' => 1000,
            'subtotal' => 2000,
            'delivery_rate' => 1500,
            'delivery_fee_type' => 2,
        ]);

        $customer = User::factory()->create(['exempt_from_fees' => 0]);
        $order->setRelation('customer', $customer);

        $pickup = Pickup::factory()->create([
            'delivery_fee_cap' => 5, // 500 cents
            'delivery_total_threshold' => 19, // 1900 cents
            'apply_limit' => true
        ]);
        $pickup->settings = ['delivery_fee_type' => 2]; // fixed
        $order->setRelation('pickup', $pickup);

        $calculator = new Calculator($order);

        $result = $calculator->sumDeliveryFee();

        $this->assertInstanceOf(Calculator::class, $result);

        $calculated_order = $result->getOrder();

        $this->assertEquals(500, $calculated_order->delivery_fee); // delivery fee is equal to pickup cap
    }

    #[Test]
    function it_can_sum_delivery_fee_for_a_confirmed_new_order_whose_delivery_fee_meets_the_pickup_delivery_cap_but_limit_is_not_applied(): void
    {
        $order = Order::factory()->create([
            'confirmed' => true,
            'status_id' => OrderStatus::confirmed(),
            'delivery_fee' => 1000,
            'subtotal' => 2000,
            'delivery_rate' => 1500,
            'delivery_fee_type' => 2,
        ]);

        $customer = User::factory()->create(['exempt_from_fees' => 0]);
        $order->setRelation('customer', $customer);

        $pickup = Pickup::factory()->create([
            'delivery_fee_cap' => 5, // 500 cents
            'delivery_total_threshold' => 19, // 1900 cents
            'apply_limit' => false
        ]);
        $pickup->settings = ['delivery_fee_type' => 2]; // fixed
        $order->setRelation('pickup', $pickup);

        $calculator = new Calculator($order);

        $result = $calculator->sumDeliveryFee();

        $this->assertInstanceOf(Calculator::class, $result);

        $calculated_order = $result->getOrder();

        $this->assertEquals(1500, $calculated_order->delivery_fee); // delivery fee is equal to pickup delivery rate
    }

    #[Test]
    function it_can_sum_delivery_fee_for_a_confirmed_new_order_whose_delivery_fee_meets_the_pickup_delivery_cap_but_subtotal_doesnt_meet_threshold(): void
    {
        $order = Order::factory()->create([
            'confirmed' => true,
            'status_id' => OrderStatus::confirmed(),
            'delivery_fee' => 1000,
            'subtotal' => 2000,
            'delivery_rate' => 1500,
            'delivery_fee_type' => 2,
        ]);

        $customer = User::factory()->create(['exempt_from_fees' => 0]);
        $order->setRelation('customer', $customer);

        $pickup = Pickup::factory()->create([
            'delivery_fee_cap' => 5, // 500 cents
            'delivery_total_threshold' => 2100, // 2100 cents
            'apply_limit' => true
        ]);
        $pickup->settings = ['delivery_fee_type' => 2]; // fixed
        $order->setRelation('pickup', $pickup);

        $calculator = new Calculator($order);

        $result = $calculator->sumDeliveryFee();

        $this->assertInstanceOf(Calculator::class, $result);

        $calculated_order = $result->getOrder();

        $this->assertEquals(1500, $calculated_order->delivery_fee); // delivery fee is equal to pickup delivery rate
    }

    #[Test]
    function it_can_sum_delivery_fee_for_a_confirmed_new_order_whose_delivery_fee_is_less_than_cap(): void
    {
        $order = Order::factory()->create([
            'confirmed' => true,
            'status_id' => OrderStatus::confirmed(),
            'delivery_fee' => 1000,
            'subtotal' => 2000,
            'delivery_rate' => 1500,
            'delivery_fee_type' => 2,
        ]);

        $customer = User::factory()->create(['exempt_from_fees' => 0]);
        $order->setRelation('customer', $customer);

        $pickup = Pickup::factory()->create([
            'delivery_fee_cap' => 20, // 2000 cents
            'delivery_total_threshold' => 500, // 500 cents
            'apply_limit' => true
        ]);
        $pickup->settings = ['delivery_fee_type' => 2]; // fixed
        $order->setRelation('pickup', $pickup);

        $calculator = new Calculator($order);

        $result = $calculator->sumDeliveryFee();

        $this->assertInstanceOf(Calculator::class, $result);

        $calculated_order = $result->getOrder();

        $this->assertEquals(1500, $calculated_order->delivery_fee); // delivery fee is equal to pickup delivery rate
    }

    #[Test]
    function it_can_sum_fees_when_customer_is_exempt_from_fees(): void
    {
        $order = Order::factory()->create(['delivery_fee' => 10]);

        $items = OrderItem::factory()->count(2)->create();
        $order->setRelation('items', $items);

        $customer = User::factory()->create(['exempt_from_fees' => 1]);
        $order->setRelation('customer', $customer);

        $pickup = Pickup::factory()->create();
        $order->setRelation('pickup', $pickup);


        $calculator = new Calculator($order);

        $result = $calculator->sumFees();

        $this->assertInstanceOf(Calculator::class, $result);

        $calculated_order = $result->getOrder();

        $this->assertEquals(0, $calculated_order->fees_subtotal);
    }

    #[Test]
    function it_can_sum_fees_when_it_has_no_pickup_point(): void
    {
        $order = Order::factory()->create(['delivery_fee' => 10]);

        $items = OrderItem::factory()->count(2)->create();
        $order->setRelation('items', $items);

        $customer = User::factory()->create(['exempt_from_fees' => 0]);
        $order->setRelation('customer', $customer);

        $order->setRelation('pickup', null);

        $calculator = new Calculator($order);

        $result = $calculator->sumFees();

        $this->assertInstanceOf(Calculator::class, $result);

        $calculated_order = $result->getOrder();

        $this->assertEquals(0, $calculated_order->fees_subtotal);
    }

    #[Test]
    function it_can_sum_fees_for_an_unconfirmed_order(): void
    {
        $order = Order::factory()->create(['confirmed' => false, 'delivery_fee' => 10]); // 10 cents
        $order_fees = OrderFee::factory()->count(2)->create(['subtotal' => 300]); // 600 cents total
        $order->setRelation('fees', $order_fees);


        $items = OrderItem::factory()->count(2)->create();
        $order->setRelation('items', $items);

        $customer = User::factory()->create(['exempt_from_fees' => 0]);
        $order->setRelation('customer', $customer);

        $pickup = Pickup::factory()->create();
        $pickup_fees = PickupFee::factory()->count(2)->create(['amount' => 4]); // 800 cents total
        $pickup->setRelation('fees', $pickup_fees);

        $order->setRelation('pickup', $pickup);

        $calculator = new Calculator($order);

        $result = $calculator->sumFees();

        $this->assertInstanceOf(Calculator::class, $result);

        $calculated_order = $result->getOrder();

        $this->assertEquals(1410, $calculated_order->fees_subtotal); // order fee subtotal + order delivery_fee + pickup fee total
        $this->assertEquals(1410, $calculated_order->original_fees_subtotal);
    }

    #[Test]
    function it_can_sum_fees_for_a_confirmed_order(): void
    {
        $order = Order::factory()->create(['confirmed' => true, 'delivery_fee' => 10, 'original_fees_subtotal' => 500]); // 10 cents
        $order_fees = OrderFee::factory()->count(2)->create(['subtotal' => 300]); // 600 cents total
        $order->setRelation('fees', $order_fees);


        $items = OrderItem::factory()->count(2)->create();
        $order->setRelation('items', $items);

        $customer = User::factory()->create(['exempt_from_fees' => 0]);
        $order->setRelation('customer', $customer);

        $pickup = Pickup::factory()->create();
        $pickup_fees = PickupFee::factory()->count(2)->create(['amount' => 4]); // 800 cents total
        $pickup->setRelation('fees', $pickup_fees);

        $order->setRelation('pickup', $pickup);

        $calculator = new Calculator($order);

        $result = $calculator->sumFees();

        $this->assertInstanceOf(Calculator::class, $result);

        $calculated_order = $result->getOrder();

        $this->assertEquals(610, $calculated_order->fees_subtotal); // order fee subtotal + order delivery_fee (no pickup fees)
        $this->assertEquals(500, $calculated_order->original_fees_subtotal);
    }

    #[Test]
    function it_can_calculate_taxes_when_customer_is_exempt_from_taxes(): void
    {
        $order = Order::factory()->create(['tax' => 5]);

        $items = OrderItem::factory()->count(2)->create();
        $order->setRelation('items', $items);

        $customer = User::factory()->create();
        $customer->settings = ['exempt_from_tax' => true];
        $order->setRelation('customer', $customer);

        $pickup = Pickup::factory()->create();
        $order->setRelation('pickup', $pickup);

        $calculator = new Calculator($order);

        $result = $calculator->calculateTax();

        $this->assertInstanceOf(Calculator::class, $result);

        $calculated_order = $result->getOrder();

        $this->assertEquals(0, $calculated_order->tax);
    }

    #[Test]
    function it_can_calculate_taxes_when_order_has_no_pickup_point(): void
    {
        $order = Order::factory()->create(['tax' => 5]);

        $items = OrderItem::factory()->count(2)->create();
        $order->setRelation('items', $items);

        $customer = User::factory()->create();
        $customer->settings = ['exempt_from_tax' => false];
        $order->setRelation('customer', $customer);

        $order->setRelation('pickup', null);

        $calculator = new Calculator($order);

        $result = $calculator->calculateTax();

        $this->assertInstanceOf(Calculator::class, $result);

        $calculated_order = $result->getOrder();

        $this->assertEquals(0, $calculated_order->tax);
    }

    #[Test]
    function it_can_calculate_taxes_when_there_are_no_taxable_products_on_confirmed_order(): void
    {
        $order = Order::factory()->create(['tax' => 5, 'confirmed' => true]);

        $product = Product::factory()->create(['taxable' => false]);
        $items = OrderItem::factory()->count(2)->create(['subtotal' => 1200])
            ->each->setRelation('product', $product);

        $order->setRelation('items', $items);

        $customer = User::factory()->create();
        $customer->settings = ['exempt_from_tax' => false];
        $order->setRelation('customer', $customer);

        $pickup = Pickup::factory()->create();
        $order->setRelation('pickup', $pickup);

        $calculator = new Calculator($order);

        $result = $calculator->calculateTax();

        $this->assertInstanceOf(Calculator::class, $result);

        $calculated_order = $result->getOrder();

        $this->assertEquals(0, $calculated_order->tax);
    }

    #[Test]
    function it_can_calculate_taxes_from_all_taxable_products_on_confirmed_order(): void
    {
        $order = Order::factory()->create(['tax' => 5, 'confirmed' => true]);

        $product_one = Product::factory()->create(['taxable' => true]);
        $items_with_product_one = OrderItem::factory()->count(2)->create(['subtotal' => 1200])
            ->each->setRelation('product', $product_one);

        $product_two = Product::factory()->create(['taxable' => false]);
        $items_with_product_two = OrderItem::factory()->count(2)->create(['subtotal' => 1300])
            ->each->setRelation('product', $product_two);

        $order->setRelation('items', $items_with_product_one->concat($items_with_product_two));

        $customer = User::factory()->create();
        $customer->settings = ['exempt_from_tax' => false];
        $order->setRelation('customer', $customer);

        $pickup = Pickup::factory()->create(['tax_rate' => 0.10]);
        $order->setRelation('pickup', $pickup);

        $calculator = new Calculator($order);

        $result = $calculator->calculateTax();

        $this->assertInstanceOf(Calculator::class, $result);

        $calculated_order = $result->getOrder();

        $this->assertEquals((1200 * 2) * .1, $calculated_order->tax);
    }

    #[Test]
    function it_can_calculate_taxes_for_a_confirmed_order_with_taxable_order_fees(): void
    {
        $order = Order::factory()->create(['tax' => 5, 'confirmed' => true]);
        $taxable_order_fees = OrderFee::factory()->count(2)->create(['order_id' => $order->id, 'subtotal' => 700, 'taxable' => true]); // 1400 cents total
        $non_taxable_order_fees = OrderFee::factory()->count(2)->create(['order_id' => $order->id, 'subtotal' => 700, 'taxable' => false]); // 1400 cents total
        $order->setRelation('fees', $taxable_order_fees->concat($non_taxable_order_fees));

        $product_one = Product::factory()->create(['taxable' => true]);
        $items_with_product_one = OrderItem::factory()->count(2)->create(['subtotal' => 1200])
            ->each->setRelation('product', $product_one);

        $product_two = Product::factory()->create(['taxable' => false]);
        $items_with_product_two = OrderItem::factory()->count(2)->create(['subtotal' => 1300])
            ->each->setRelation('product', $product_two);

        $order->setRelation('items', $items_with_product_one->concat($items_with_product_two));

        $customer = User::factory()->create();
        $customer->settings = ['exempt_from_tax' => false];
        $order->setRelation('customer', $customer);

        $pickup = Pickup::factory()->create(['tax_rate' => 0.10]);
        $taxable_pickup_fees = PickupFee::factory()->count(2)->create(['pickup_id' => $pickup->id, 'amount' => 800, 'taxable' => true]); // 1600 cents total
        $non_taxable_pickup_fees = PickupFee::factory()->count(2)->create(['pickup_id' => $pickup->id, 'amount' => 800, 'taxable' => false]); // 1600 cents total
        $pickup->setRelation('fees', $taxable_pickup_fees->concat($non_taxable_pickup_fees));

        $order->setRelation('pickup', $pickup);

        $calculator = new Calculator($order);

        $result = $calculator->calculateTax();

        $this->assertInstanceOf(Calculator::class, $result);

        $calculated_order = $result->getOrder();

        $this->assertEquals(((1200 * 2) + (700 * 2)) * .1, $calculated_order->tax);
    }

    #[Test]
    function it_can_calculate_taxes_for_an_unconfirmed_order_with_taxable_pickup_fees(): void
    {
        $order = Order::factory()->create(['tax' => 5, 'confirmed' => false]);
        $taxable_order_fees = OrderFee::factory()->count(2)->create(['order_id' => $order->id, 'subtotal' => 700, 'taxable' => true]); // 1400 cents total
        $non_taxable_order_fees = OrderFee::factory()->count(2)->create(['order_id' => $order->id, 'subtotal' => 700, 'taxable' => false]); // 1400 cents total
        $order->setRelation('fees', $taxable_order_fees->concat($non_taxable_order_fees));

        $product_one = Product::factory()->create(['taxable' => true]);
        $items_with_product_one = OrderItem::factory()->count(2)->create(['subtotal' => 1200])
            ->each->setRelation('product', $product_one);

        $product_two = Product::factory()->create(['taxable' => false]);
        $items_with_product_two = OrderItem::factory()->count(2)->create(['subtotal' => 1300])
            ->each->setRelation('product', $product_two);

        $order->setRelation('items', $items_with_product_one->concat($items_with_product_two));

        $customer = User::factory()->create();
        $customer->settings = ['exempt_from_tax' => false];
        $order->setRelation('customer', $customer);

        $pickup = Pickup::factory()->create(['tax_rate' => 0.10]);

        $taxable_pickup_fees = PickupFee::factory()->count(2)->create(['pickup_id' => $pickup->id, 'amount' => 800, 'taxable' => true]); // 1600 cents total
        $non_taxable_pickup_fees = PickupFee::factory()->count(2)->create(['pickup_id' => $pickup->id, 'amount' => 800, 'taxable' => false]); // 1600 cents total
        $pickup->setRelation('fees', $taxable_pickup_fees->concat($non_taxable_pickup_fees));

        $order->setRelation('pickup', $pickup);

        $calculator = new Calculator($order);

        $result = $calculator->calculateTax();

        $this->assertInstanceOf(Calculator::class, $result);

        $calculated_order = $result->getOrder();

        $this->assertEquals(((1200 * 2) + (800 * 2)) * .1, $calculated_order->tax);
    }

    #[Test]
    function it_can_calculate_taxes_for_an_unconfirmed_order_with_taxable_delivery_fee(): void
    {
        $order = Order::factory()->create(['tax' => 5, 'confirmed' => false, 'delivery_fee' => 500]);
        $taxable_order_fees = OrderFee::factory()->count(2)->create(['order_id' => $order->id, 'subtotal' => 700, 'taxable' => true]); // 1400 cents total
        $non_taxable_order_fees = OrderFee::factory()->count(2)->create(['order_id' => $order->id, 'subtotal' => 700, 'taxable' => false]); // 1400 cents total
        $order->setRelation('fees', $taxable_order_fees->concat($non_taxable_order_fees));

        $product_one = Product::factory()->create(['taxable' => true]);
        $items_with_product_one = OrderItem::factory()->count(2)->create(['subtotal' => 1200])
            ->each->setRelation('product', $product_one);

        $product_two = Product::factory()->create(['taxable' => false]);
        $items_with_product_two = OrderItem::factory()->count(2)->create(['subtotal' => 1300])
            ->each->setRelation('product', $product_two);

        $order->setRelation('items', $items_with_product_one->concat($items_with_product_two));

        $customer = User::factory()->create();
        $customer->settings = ['exempt_from_tax' => false];
        $order->setRelation('customer', $customer);

        $pickup = Pickup::factory()->create(['tax_rate' => 0.10, 'tax_delivery_fee' => true]);
        $taxable_pickup_fees = PickupFee::factory()->count(2)->create(['pickup_id' => $pickup->id, 'amount' => 800, 'taxable' => true]); // 1600 cents total
        $non_taxable_pickup_fees = PickupFee::factory()->count(2)->create(['pickup_id' => $pickup->id, 'amount' => 800, 'taxable' => false]); // 1600 cents total
        $pickup->setRelation('fees', $taxable_pickup_fees->concat($non_taxable_pickup_fees));

        $order->setRelation('pickup', $pickup);

        $calculator = new Calculator($order);

        $result = $calculator->calculateTax();

        $this->assertInstanceOf(Calculator::class, $result);

        $calculated_order = $result->getOrder();

        $this->assertEquals(((1200 * 2) + (800 * 2) + 500) * .1, $calculated_order->tax);
    }

    #[Test]
    function it_can_calculate_taxes_for_an_unconfirmed_order_with_non_taxable_delivery_fee(): void
    {
        $order = Order::factory()->create(['tax' => 5, 'confirmed' => false, 'delivery_fee' => 500]);
        $taxable_order_fees = OrderFee::factory()->count(2)->create(['order_id' => $order->id, 'subtotal' => 700, 'taxable' => true]); // 1400 cents total
        $non_taxable_order_fees = OrderFee::factory()->count(2)->create(['order_id' => $order->id, 'subtotal' => 700, 'taxable' => false]); // 1400 cents total
        $order->setRelation('fees', $taxable_order_fees->concat($non_taxable_order_fees));

        $product_one = Product::factory()->create(['taxable' => true]);
        $items_with_product_one = OrderItem::factory()->count(2)->create(['subtotal' => 1200])
            ->each->setRelation('product', $product_one);

        $product_two = Product::factory()->create(['taxable' => false]);
        $items_with_product_two = OrderItem::factory()->count(2)->create(['subtotal' => 1300])
            ->each->setRelation('product', $product_two);

        $order->setRelation('items', $items_with_product_one->concat($items_with_product_two));

        $customer = User::factory()->create();
        $customer->settings = ['exempt_from_tax' => false];
        $order->setRelation('customer', $customer);

        $pickup = Pickup::factory()->create(['tax_rate' => 0.10, 'tax_delivery_fee' => false]);
        $taxable_pickup_fees = PickupFee::factory()->count(2)->create(['pickup_id' => $pickup->id, 'amount' => 800, 'taxable' => true]); // 1600 cents total
        $non_taxable_pickup_fees = PickupFee::factory()->count(2)->create(['pickup_id' => $pickup->id, 'amount' => 800, 'taxable' => false]); // 1600 cents total
        $pickup->setRelation('fees', $taxable_pickup_fees->concat($non_taxable_pickup_fees));

        $order->setRelation('pickup', $pickup);

        $calculator = new Calculator($order);

        $result = $calculator->calculateTax();

        $this->assertInstanceOf(Calculator::class, $result);

        $calculated_order = $result->getOrder();

        $this->assertEquals(((1200 * 2) + (800 * 2)) * .1, $calculated_order->tax);
    }

    #[Test]
    function it_can_calculate_rounded_up_taxes(): void
    {
        $order = Order::factory()->create(['tax' => 5, 'confirmed' => false, 'delivery_fee' => 500]);
        $taxable_order_fees = OrderFee::factory()->count(2)->create(['order_id' => $order->id, 'subtotal' => 700, 'taxable' => true]); // 1400 cents total
        $non_taxable_order_fees = OrderFee::factory()->count(2)->create(['order_id' => $order->id, 'subtotal' => 700, 'taxable' => false]); // 1400 cents total
        $order->setRelation('fees', $taxable_order_fees->concat($non_taxable_order_fees));

        $product_one = Product::factory()->create(['taxable' => true]);
        $items_with_product_one = OrderItem::factory()->count(2)->create(['subtotal' => 1200])
            ->each->setRelation('product', $product_one);

        $product_two = Product::factory()->create(['taxable' => false]);
        $items_with_product_two = OrderItem::factory()->count(2)->create(['subtotal' => 1300])
            ->each->setRelation('product', $product_two);

        $order->setRelation('items', $items_with_product_one->concat($items_with_product_two));

        $customer = User::factory()->create();
        $customer->settings = ['exempt_from_tax' => false];
        $order->setRelation('customer', $customer);

        $pickup = Pickup::factory()->create(['tax_rate' => 0.0973, 'tax_delivery_fee' => true]);
        $taxable_pickup_fees = PickupFee::factory()->count(2)->create(['pickup_id' => $pickup->id, 'amount' => 800, 'taxable' => true]); // 1600 cents total
        $non_taxable_pickup_fees = PickupFee::factory()->count(2)->create(['pickup_id' => $pickup->id, 'amount' => 800, 'taxable' => false]); // 1600 cents total
        $pickup->setRelation('fees', $taxable_pickup_fees->concat($non_taxable_pickup_fees));

        $order->setRelation('pickup', $pickup);

        $calculator = new Calculator($order);

        $result = $calculator->calculateTax();

        $this->assertInstanceOf(Calculator::class, $result);

        $calculated_order = $result->getOrder();

        // ((1200 * 2) + (800 * 2) + 500) * .0973 = 437.85
        $this->assertEquals(ceil($calculated_order->tax), $calculated_order->tax);  // ensure whole decimal
        $this->assertEquals(438, $calculated_order->tax);
    }

    #[Test]
    function it_can_calculate_rounded_down_taxes(): void
    {
        $order = Order::factory()->create(['tax' => 5, 'confirmed' => false, 'delivery_fee' => 500]);
        $taxable_order_fees = OrderFee::factory()->count(2)->create(['order_id' => $order->id, 'subtotal' => 700, 'taxable' => true]); // 1400 cents total
        $non_taxable_order_fees = OrderFee::factory()->count(2)->create(['order_id' => $order->id, 'subtotal' => 700, 'taxable' => false]); // 1400 cents total
        $order->setRelation('fees', $taxable_order_fees->concat($non_taxable_order_fees));

        $product_one = Product::factory()->create(['taxable' => true]);
        $items_with_product_one = OrderItem::factory()->count(2)->create(['subtotal' => 1200])
            ->each->setRelation('product', $product_one);

        $product_two = Product::factory()->create(['taxable' => false]);
        $items_with_product_two = OrderItem::factory()->count(2)->create(['subtotal' => 1300])
            ->each->setRelation('product', $product_two);

        $order->setRelation('items', $items_with_product_one->concat($items_with_product_two));

        $customer = User::factory()->create();
        $customer->settings = ['exempt_from_tax' => false];
        $order->setRelation('customer', $customer);

        $pickup = Pickup::factory()->create(['tax_rate' => 0.0921, 'tax_delivery_fee' => true]);
        $taxable_pickup_fees = PickupFee::factory()->count(2)->create(['pickup_id' => $pickup->id, 'amount' => 800, 'taxable' => true]); // 1600 cents total
        $non_taxable_pickup_fees = PickupFee::factory()->count(2)->create(['pickup_id' => $pickup->id, 'amount' => 800, 'taxable' => false]); // 1600 cents total
        $pickup->setRelation('fees', $taxable_pickup_fees->concat($non_taxable_pickup_fees));

        $order->setRelation('pickup', $pickup);

        $calculator = new Calculator($order);

        $result = $calculator->calculateTax();

        $this->assertInstanceOf(Calculator::class, $result);

        $calculated_order = $result->getOrder();

        // ((1200 * 2) + (800 * 2) + 500) * .0921 = 414.45
        $this->assertEquals(floor($calculated_order->tax), $calculated_order->tax); // ensure whole decimal
        $this->assertEquals(414, $calculated_order->tax);
    }

    #[Test]
    function it_can_calculate_subtotal_of_an_unconfirmed_order(): void
    {
        $order = Order::factory()->create(['confirmed' => false]);

        $order->original_total = 1100;
        $order->subtotal = 1300;
        $order->fees_subtotal = 1200;
        $order->tax = 250;

        $calculator = new Calculator($order);

        $result = $calculator->calculateSubtotal();

        $this->assertInstanceOf(Calculator::class, $result);

        $calculated_order = $result->getOrder();

        $this->assertEquals(2750, $calculated_order->total);
        $this->assertEquals(2750, $calculated_order->original_total);
    }

    #[Test]
    function it_can_calculate_subtotal_of_a_confirmed_order(): void
    {
        $order = Order::factory()->create(['confirmed' => true]);

        $order->original_total = 1100;
        $order->subtotal = 1300;
        $order->fees_subtotal = 1200;
        $order->tax = 250;

        $calculator = new Calculator($order);

        $result = $calculator->calculateSubtotal();

        $this->assertInstanceOf(Calculator::class, $result);

        $calculated_order = $result->getOrder();

        $this->assertEquals(2750, $calculated_order->total);
        $this->assertEquals(1100, $calculated_order->original_total);
    }

    #[Test]
    function it_can_calculate_discounts_of_fixed_value_order_coupons(): void
    {
        $this->markTestIncomplete('Need to figure out how to handle pivot updates on order coupons being applied.');
    }

    #[Test]
    function it_can_calculate_discounts_of_percentage_value_order_coupons(): void
    {
        $this->markTestIncomplete('Need to figure out how to handle pivot updates on order coupons being applied.');
    }

    #[Test]
    function it_does_not_apply_order_coupons_when_order_subtotal_does_not_meet_minimum_required_value(): void
    {
        $this->markTestIncomplete('Need to figure out how to handle pivot updates on order coupons being applied.');
    }

    #[Test]
    function it_remove_already_applied_order_coupons_when_order_subtotal_does_not_meet_minimum_required_value(): void
    {
        $this->markTestIncomplete('Need to figure out how to handle pivot updates on order coupons being applied.');
    }

    #[Test]
    function it_can_calculate_discounts_of_fixed_value_delivery_coupons(): void
    {
        $this->markTestIncomplete('Need to figure out how to handle pivot updates on delivery coupons being applied.');
    }

    #[Test]
    function it_can_calculate_discounts_of_percentage_value_delivery_coupons(): void
    {
        $this->markTestIncomplete('Need to figure out how to handle pivot updates on delivery coupons being applied.');
    }

    #[Test]
    function it_does_not_apply_delivery_coupons_when_order_subtotal_does_not_meet_minimum_required_value(): void
    {
        $this->markTestIncomplete('Need to figure out how to handle pivot updates on delivery coupons being applied.');
    }

    #[Test]
    function it_does_not_apply_customer_credit_if_order_is_already_paid(): void
    {
        $this->mock(CreditManager::class, function (MockInterface $mock) {
            $mock->shouldNotReceive('ApplyCreditToOrder');
        });

        $order = Order::factory()->create(['paid' => true, 'confirmed' => false]);
        $calculator = new Calculator($order);

        $calculator->applyCustomerCredit();
    }

    #[Test]
    function it_does_not_apply_customer_credit_if_order_is_not_confirmed(): void
    {
        $this->mock(CreditManager::class, function (MockInterface $mock) {
            $mock->shouldNotReceive('ApplyCreditToOrder');
        });

        $order = Order::factory()->create(['paid' => false, 'confirmed' => false]);
        $calculator = new Calculator($order);

        $calculator->applyCustomerCredit();
    }

    #[Test]
    function it_does_not_apply_customer_credit_if_order_is_already_processed(): void
    {
        $this->mock(CreditManager::class, function (MockInterface $mock) {
            $mock->shouldNotReceive('ApplyCreditToOrder');
        });

        $order = Order::factory()->create(['paid' => false, 'confirmed' => true, 'processed' => true]);
        $calculator = new Calculator($order);

        $calculator->applyCustomerCredit();
    }

    #[Test]
    function it_does_not_apply_customer_credit_if_order_is_canceled(): void
    {
        $this->mock(CreditManager::class, function (MockInterface $mock) {
            $mock->shouldNotReceive('ApplyCreditToOrder');
        });

        $order = Order::factory()->create(['paid' => false, 'confirmed' => true, 'processed' => false, 'canceled' => true]);
        $calculator = new Calculator($order);

        $calculator->applyCustomerCredit();
    }

    #[Test]
    function it_does_not_apply_customer_credit_if_order_contains_gift_cards(): void
    {
        $this->mock(CreditManager::class, function (MockInterface $mock) {
            $mock->shouldNotReceive('ApplyCreditToOrder');
        });

        $product = Product::factory()->create(['type_id' =>  ProductType::GIFT_CARD->value]);

        $order = Order::factory()->create(['paid' => false, 'confirmed' => true, 'processed' => false, 'canceled' => false]);
        OrderItem::factory()->create(['order_id' => $order->id, 'product_id' => $product->id]);

        $calculator = new Calculator($order);

        $calculator->applyCustomerCredit();
    }


    #[Test]
    function it_can_apply_credit_to_final_total_for_a_confirmed_order(): void
    {
        $order = Order::factory()->create(['confirmed' => true, 'total' => 1500, 'credit_applied' => 145]);

        $calculator = new Calculator($order);

        $result = $calculator->applyCreditToFinalTotal();

        $calculated_order = $result->getOrder();

        $this->assertEquals(1500 - 145, $calculated_order->total);
    }

    #[Test]
    function it_can_apply_credit_to_final_total_for_an_unconfirmed_order(): void
    {
        $order = Order::factory()->create(['confirmed' => false, 'total' => 1500, 'credit_applied' => 145]);

        $customer = User::factory()->create(['credit' => 245]);
        $order->setRelation('customer', $customer);

        $calculator = new Calculator($order);

        $result = $calculator->applyCreditToFinalTotal();

        $calculated_order = $result->getOrder();

        $this->assertEquals(1500 - 245, $calculated_order->total);
    }

    #[Test]
    function it_can_apply_credit_to_final_total_of_a_confirmed_order_when_discount_exceeds_order_total(): void
    {
        $order = Order::factory()->create(['confirmed' => true, 'total' => 1500, 'credit_applied' => 1501]);

        $calculator = new Calculator($order);

        $result = $calculator->applyCreditToFinalTotal();

        $calculated_order = $result->getOrder();

        $this->assertEquals(0, $calculated_order->total);
    }

    #[Test]
    function it_can_apply_credit_to_final_total_of_an_unconfirmed_order_when_discount_exceeds_order_total(): void
    {
        $order = Order::factory()->create(['confirmed' => false, 'total' => 1500, 'credit_applied' => 145]);

        $customer = User::factory()->create(['credit' => 1501]);
        $order->setRelation('customer', $customer);

        $calculator = new Calculator($order);

        $result = $calculator->applyCreditToFinalTotal();

        $calculated_order = $result->getOrder();

        $this->assertEquals(0, $calculated_order->total);
    }
    
        #[Test]
    function it_includes_non_refunded(): void
    {
        $this->markTestIncomplete('Need to determine if/how we could update amount refunded at time of refund, rather than in this function');
    }

    function testTotals(): void
    {
        $this->markTestIncomplete('Need to implement');
    }

    #[Test]
    function it_can_apply_customer_credit_if_all_canAcceptCredit_check_returns_true(): void
    {
        $order = Order::factory()->create(['paid' => false, 'confirmed' => true, 'processed' => false, 'canceled' => false]);
        $this->mock(CreditManager::class, function (MockInterface $mock) use ($order) {
            $mock->shouldReceive('ApplyCreditToOrder')->once()->with($order);
        });

        $calculator = new Calculator($order);

        $calculator->applyCustomerCredit();
    }
}
