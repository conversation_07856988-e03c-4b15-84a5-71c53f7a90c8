<?php

namespace Tests\Unit\Listeners\Subscription;

use App\Events\Subscription\SubscriptionPromoProductWasUpdated;
use App\Listeners\Subscription\RecordSubscriptionPromoProductWasUpdated;
use App\Models\RecurringOrder;
use Carbon\Carbon;
use Illuminate\Support\Facades\Event;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class RecordCustomerUpdatedSubscriptionPromoProductTest extends TenantTestCase
{
    #[Test]
    public function the_listener_is_registered(): void
    {
        Event::fake([SubscriptionPromoProductWasUpdated::class]);
        Event::assertListening(SubscriptionPromoProductWasUpdated::class, RecordSubscriptionPromoProductWasUpdated::class);
    }

    #[Test]
    public function it_records_the_expected_event(): void
    {
        Carbon::setTestNow(now());

        $subscription = RecurringOrder::factory()->create();

        event(new SubscriptionPromoProductWasUpdated(
            $subscription,
            4,
            6
        ));

        $this->assertDatabaseHas(\App\Models\Event::class, [
            'user_id' => $subscription->customer_id,
            'model_id' => $subscription->id,
            'model_type' => RecurringOrder::class,
            'event_id' => SubscriptionPromoProductWasUpdated::class,
            'description' => 'The customer updated their subscription promo product.',
            'metadata' => json_encode([
                'old_promo_product_id' => 4,
                'new_promo_product_id' => 6
            ])
        ]);

        Carbon::setTestNow();
    }
}
