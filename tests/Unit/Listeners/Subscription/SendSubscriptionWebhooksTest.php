<?php

namespace Tests\Unit\Listeners\Subscription;

use App\Events\Subscription\CustomerSubscribed;
use App\Jobs\SendWebhook;
use App\Listeners\Subscription\SendSubscriptionWebhooks;
use App\Models\Pickup;
use App\Models\RecurringOrder;
use App\Models\RecurringOrderItem;
use App\Models\Webhook;
use App\Services\SubscriptionSettingsService;
use App\Support\Enums\ProductType;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\Event;
use Mockery\MockInterface;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class SendSubscriptionWebhooksTest extends TenantTestCase
{
    #[Test]
    public function it_listens_to_the_customer_subscribed_event(): void
    {
        Event::fake([CustomerSubscribed::class]);

        Event::assertListening(CustomerSubscribed::class, SendSubscriptionWebhooks::class);
    }

    #[Test]
    public function it_sends_the_expected_webhook(): void
    {
        Bus::fake([SendWebhook::class]);

        $subscription = RecurringOrder::factory()->create();
        $items = RecurringOrderItem::factory(2)->create(['order_id' => $subscription->id]);
        $expected_payload = $this->payloadForSubscription($subscription, $items);

        $webhook = Webhook::factory()->create(['topic' => 'subscriptions.created']);

        $this->mock(SubscriptionSettingsService::class, function (MockInterface $mock) {
            $mock->shouldReceive('discountIncentive')->withNoArgs()->andReturn(4);
        });

        (new SendSubscriptionWebhooks())->handle(new CustomerSubscribed($subscription));

        Bus::assertDispatched(function (SendWebhook $job) use ($webhook, $expected_payload) {
            return $job->webhook->id === $webhook->id
                && $job->payload === $expected_payload;
        });
    }

    #[Test]
    public function it_does_not_send_the_webhook_when_their_are_no_configured_webhooks(): void
    {
        Bus::fake([SendWebhook::class]);

        $subscription = RecurringOrder::factory()->create();
        RecurringOrderItem::factory(2)->create(['order_id' => $subscription->id]);

        Webhook::factory()->create(['topic' => 'orders.created']);

        $this->mock(SubscriptionSettingsService::class, function (MockInterface $mock) {
            $mock->shouldReceive('discountIncentive')->once()->withNoArgs()->andReturn(4);
        });

        (new SendSubscriptionWebhooks())->handle(new CustomerSubscribed($subscription));

        Bus::assertNotDispatched(SendWebhook::class);

    }

    #[Test]
    public function it_sends_configured_webhooks_by_delivery_method(): void
    {
        Bus::fake([SendWebhook::class]);

        $pickup_one = Pickup::factory()->create();
        $pickup_two = Pickup::factory()->create();
        $webhook_one = Webhook::factory()->create([
            'topic' => 'subscriptions.created',
            'settings' => ['filters' => ['delivery_methods' => []]]
        ]);
        $webhook_two = Webhook::factory()->create([
            'topic' => 'subscriptions.created',
            'settings' => ['filters' => ['delivery_methods' => [$pickup_one->id]]]
        ]);
        $webhook_three = Webhook::factory()->create([
            'topic' => 'subscriptions.created',
            'settings' => ['filters' => ['delivery_methods' => [$pickup_two->id]]]
        ]);
        $webhook_four = Webhook::factory()->create([
            'topic' => 'subscriptions.created',
            'settings' => ['filters' => ['delivery_methods' => [0]]]
        ]);

        $subscription = RecurringOrder::factory()->create(['fulfillment_id' => $pickup_one->id]);
        $items = RecurringOrderItem::factory(2)->create(['order_id' => $subscription->id]);
        $expected_payload = $this->payloadForSubscription($subscription, $items);

        $this->mock(SubscriptionSettingsService::class, function (MockInterface $mock) {
            $mock->shouldReceive('discountIncentive')->once()->withNoArgs()->andReturn(4);
        });

        (new SendSubscriptionWebhooks())->handle(new CustomerSubscribed($subscription));

        Bus::assertDispatchedTimes(SendWebhook::class, 3);

        Bus::assertDispatched(function (SendWebhook $job) use ($webhook_one, $webhook_two, $webhook_four, $expected_payload) {
            return in_array($job->webhook->id, [$webhook_one->id, $webhook_two->id, $webhook_four->id])
                && $job->payload === $expected_payload;
        });
    }

    #[Test]
    public function it_sends_configured_webhooks_by_sales_channel(): void
    {
        Bus::fake([SendWebhook::class]);

        $webhook_one = Webhook::factory()->create([
            'topic' => 'subscriptions.created',
            'settings' => ['filters' => ['sales_channels' => []]]
        ]);
        $webhook_two = Webhook::factory()->create([
            'topic' => 'subscriptions.created',
            'settings' => ['filters' => ['sales_channels' => [1]]]
        ]);
        $webhook_three = Webhook::factory()->create([
            'topic' => 'subscriptions.created',
            'settings' => ['filters' => ['sales_channels' => [2]]]
        ]);
        $webhook_four = Webhook::factory()->create([
            'topic' => 'subscriptions.created',
            'settings' => ['filters' => ['sales_channels' => [0]]]
        ]);

        $pickup = Pickup::factory()->create(['settings' => ['sales_channel_id' => 1]]);
        $subscription = RecurringOrder::factory()->create(['fulfillment_id' => $pickup->id]);
        $items = RecurringOrderItem::factory(2)->create(['order_id' => $subscription->id]);
        $expected_payload = $this->payloadForSubscription($subscription, $items);

        $this->mock(SubscriptionSettingsService::class, function (MockInterface $mock) {
            $mock->shouldReceive('discountIncentive')->once()->withNoArgs()->andReturn(4);
        });

        (new SendSubscriptionWebhooks())->handle(new CustomerSubscribed($subscription));

        Bus::assertDispatchedTimes(SendWebhook::class, 3);

        Bus::assertDispatched(function (SendWebhook $job) use ($webhook_one, $webhook_two, $webhook_four, $expected_payload) {
            return in_array($job->webhook->id, [$webhook_one->id, $webhook_two->id, $webhook_four->id])
                && $job->payload === $expected_payload;
        });
    }

    private function payloadForSubscription(RecurringOrder $subscription, Collection $items): array
    {
        return [
            'id' => $subscription->id,
            'customer' => [
                'id' => $subscription->customer->id,
                'first_name' => $subscription->customer->first_name,
                'last_name' => $subscription->customer->last_name,
                'phone_number' => $subscription->customer->phone,
                'email' => $subscription->customer->email,
                'delivery_method' => [
                    'id' => $subscription->customer->pickup->id,
                    'title' => $subscription->customer->pickup->title,
                    'type_id' => $subscription->fulfillment->fulfillment_type,
                    'status_id' => $subscription->fulfillment->status_id,
                ],
                'shipping_address' =>  $subscription->customer->street ? [
                    'street' => $subscription->customer->street,
                    'street_2' => $subscription->customer->street_2,
                    'city' => $subscription->customer->city,
                    'state' => $subscription->customer->state,
                    'postal_code' => $subscription->customer->zip,
                    'country' => $subscription->customer->country
                ] : null,
                'order_count' => $subscription->customer->order_count,
                'is_subscriber' => ! is_null($subscription->customer->recurringOrder),
                'created_at' => $subscription->customer->created_at->toIso8601String(),
                'updated_at' => $subscription->customer->updated_at->toIso8601String(),
            ],
            'delivery_method' =>  [
                'id' => $subscription->fulfillment_id,
                'title' => $subscription->fulfillment->title,
                'type_id' => $subscription->fulfillment->fulfillment_type,
                'status_id' => $subscription->fulfillment->status_id,
            ],
            'discount_percentage' => 4,
            'reorder_frequency' => $subscription->reorder_frequency,
            'items' => [
                [
                    'id' => $items[0]->id,
                    'type' => 'recurring',
                    'quantity' => $items[0]->qty,
                    'product_id' => $items[0]->product_id,
                    'product' => [
                        'id' => $items[0]->product->id,
                        'type_id' => $items[0]->product->type_id,
                        'type' => ProductType::get($items[0]->product->type_id),
                        'name' => $items[0]->product->title,
                        'description' => $items[0]->product->description,
                        'retail_unit_price' => $items[0]->product->unit_price,
                        'retail_sale_unit_price' => $items[0]->product->sale_unit_price,
                        'unit_description' => $items[0]->product->unit_description,
                        'unit_cost' => $items[0]->product->item_cost,
                        'sku' => $items[0]->product->sku,
                        'vendor' => null,
                        'accounting_class' => $items[0]->product->accounting_class,
                        'storage_location_id' => $items[0]->product->custom_sort,
                        'created_at' => $items[0]->product->created_at->toIso8601String(),
                        'updated_at' => $items[0]->product->updated_at->toIso8601String(),
                    ],
                    'created_at' => $items[0]->created_at->toIso8601String(),
                    'updated_at' => $items[0]->updated_at->toIso8601String(),
                ],
                [
                    'id' => $items[1]->id,
                    'type' => 'recurring',
                    'quantity' => $items[1]->qty,
                    'product_id' => $items[1]->product_id,
                    'product' => [
                        'id' => $items[1]->product->id,
                        'type_id' => $items[1]->product->type_id,
                        'type' => ProductType::get($items[1]->product->type_id),
                        'name' => $items[1]->product->title,
                        'description' => $items[1]->product->description,
                        'retail_unit_price' => $items[1]->product->unit_price,
                        'retail_sale_unit_price' => $items[1]->product->sale_unit_price,
                        'unit_description' => $items[1]->product->unit_description,
                        'unit_cost' => $items[1]->product->item_cost,
                        'sku' => $items[1]->product->sku,
                        'vendor' => null,
                        'accounting_class' => $items[1]->product->accounting_class,
                        'storage_location_id' => $items[1]->product->custom_sort,
                        'created_at' => $items[1]->product->created_at->toIso8601String(),
                        'updated_at' => $items[1]->product->updated_at->toIso8601String(),
                    ],
                    'created_at' => $items[1]->created_at->toIso8601String(),
                    'updated_at' => $items[1]->updated_at->toIso8601String(),
                ]
            ],
            'created_at' => $subscription->created_at->toIso8601String(),
            'updated_at' => $subscription->updated_at->toIso8601String(),
        ];
    }
}
