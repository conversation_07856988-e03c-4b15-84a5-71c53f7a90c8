<?php

namespace Tests\Unit\Listeners\User\UserWasRegistered;

use App\Events\User\UserWasRegistered;
use App\Jobs\SendWebhook;
use App\Listeners\User\UserWasRegistered\SendCustomerCreatedWebhooksListener;
use App\Models\User;
use App\Models\Webhook;
use App\Support\Enums\UserRole;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\Event;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class SendCustomerCreatedWebhooksListenerTest extends TenantTestCase
{
    #[Test]
    public function it_listens_to_expected_event(): void
    {
        Event::fake([UserWasRegistered::class]);

        Event::assertListening(UserWasRegistered::class, SendCustomerCreatedWebhooksListener::class);
    }

    #[Test]
    public function it_doesnt_send_configured_webhooks_when_user_role_is_not_customer(): void
    {
        Bus::fake([SendWebhook::class]);

        Webhook::factory()->create(['topic' => 'customers.created']);

        $user = User::factory()->create(['role_id' => UserRole::admin()]);

        (new SendCustomerCreatedWebhooksListener())->handle(new UserWasRegistered($user));

        Bus::assertNotDispatched(SendWebhook::class);
    }

    #[Test]
    public function it_sends_configured_webhooks_by_topic_when_user_role_is_customer(): void
    {
        Bus::fake([SendWebhook::class]);

        $webhook_one = Webhook::factory()->create(['topic' => 'customers.created']);
        $webhook_two = Webhook::factory()->create(['topic' => 'customers.created']);
        Webhook::factory()->create(['topic' => 'customers.updated']);

        $user = User::factory()->create(['role_id' => UserRole::customer()]);

        $expected_payload = [
            'id' => $user->id,
            'first_name' => $user->first_name,
            'last_name' => $user->last_name,
            'phone_number' => $user->phone,
            'email' => $user->email,
            'delivery_method' => [
                'id' => $user->pickup->id,
                'title' => $user->pickup->title,
                'type_id' => $user->pickup->fulfillment_type,
                'status_id' => $user->pickup->status_id,
            ],
            'shipping_address' =>  $user->street ? [
                'street' => $user->street,
                'street_2' => $user->street_2,
                'city' => $user->city,
                'state' => $user->state,
                'postal_code' => $user->zip,
                'country' => $user->country
            ] : null,
            'order_count' => $user->order_count,
            'is_subscriber' => ! is_null($user->recurringOrder),
            'created_at' => $user->created_at->toIso8601String(),
            'updated_at' => $user->updated_at->toIso8601String(),
        ];

        (new SendCustomerCreatedWebhooksListener())->handle(new UserWasRegistered($user));

        Bus::assertDispatchedTimes(SendWebhook::class, 2);

        Bus::assertDispatched(function (SendWebhook $job) use ($webhook_one, $webhook_two, $expected_payload) {
            return in_array($job->webhook->id, [$webhook_one->id, $webhook_two->id])
                && $job->payload === $expected_payload;
        });
    }
}
