<?php

namespace Tests\Unit\Listeners;

use App\Events\Subscription\RecurringOrderWasConfirmed;
use App\Listeners\Order\RecurringOrderWasConfirmed\SendRecurringOrderConfirmedNotification;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Product;
use App\Models\Setting;
use App\Models\User;
use App\Notifications\RecurringOrderConfirmed;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\Notification;
use Illuminate\Support\Str;
use NotificationChannels\Twilio\TwilioChannel;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class SendRecurringOrderConfirmedNotificationTest extends TenantTestCase
{
    #[Test]
    public function the_expected_listener_is_attached(): void
    {
        Event::fake();

        Event::assertListening(
            RecurringOrderWasConfirmed::class,
            SendRecurringOrderConfirmedNotification::class
        );
    }

    #[Test]
    public function it_sends_expected_mail_notification(): void
    {
        Notification::fake();

        $user = User::factory()->create();
        $order = Order::factory()->create(['customer_id' => $user->id]);

        (new SendRecurringOrderConfirmedNotification)
            ->handle(new RecurringOrderWasConfirmed($order));

        Notification::assertSentTo(
            $order,
            RecurringOrderConfirmed::class,
            function (RecurringOrderConfirmed $notification, $channels, $notifiable) use ($order) {
                return $notifiable->id === $order->id
                    && in_array('mail', $channels);
            }
        );
    }

    #[Test]
    public function it_sends_expected_twilio_notification_when_there_is_a_valid_order_phone(): void
    {
        Notification::fake();

        Setting::updateOrCreate(['key' => 'recurring_orders_reorder_sms_enabled'], ['value' => true]);

        $user = User::factory()->create(['phone' => '']);
        $order = Order::factory()->create([
            'customer_id' => $user->id,
            'customer_phone' => '************'
        ]);

        (new SendRecurringOrderConfirmedNotification)
            ->handle(new RecurringOrderWasConfirmed($order));

        Notification::assertSentTo(
            $order,
            RecurringOrderConfirmed::class,
            function (RecurringOrderConfirmed $notification, $channels, $notifiable) use ($order) {
                return $notifiable->id === $order->id
                    && in_array(TwilioChannel::class, $channels);
            }
        );
    }

    #[Test]
    public function it_sends_expected_twilio_notification_message_when_order_items_are_in_stock(): void
    {
        Notification::fake();
        Setting::updateOrCreate(['key' => 'recurring_orders_reorder_sms_enabled'], ['value' => true]);

        $user = User::factory()->create(['phone' => '']);
        $product = Product::factory()->create(['inventory' => 5]);
        $order = Order::factory()->create([
            'customer_id' => $user->id,
            'customer_phone' => '************',
            'confirmed' => true
        ]);

        OrderItem::factory()->create([
            'product_id' => $product->id,
            'order_id' => $order->id,
            'stock_status' => 'full',
            'qty' => 3,
            'fulfilled_qty' => 3,
        ]);

        (new SendRecurringOrderConfirmedNotification)
            ->handle(new RecurringOrderWasConfirmed($order));

        Notification::assertSentTo(
            $order,
            RecurringOrderConfirmed::class,
            function (RecurringOrderConfirmed $notification, $channels, $notifiable) use ($order) {
                $message = $notification->toTwilio($order)->content;
                return Str::contains($message, 'ACTION NEEDED!') === false;
            }
        );
    }

    #[Test]
    public function it_sends_expected_twilio_notification_message_when_order_items_are_out_of_stock(): void
    {
        Notification::fake();
        Setting::updateOrCreate(['key' => 'recurring_orders_reorder_sms_enabled'], ['value' => true]);

        $user = User::factory()->create(['phone' => '']);
        $product = Product::factory()->create(['inventory' => 2]);
        $order = Order::factory()->create([
            'customer_id' => $user->id,
            'customer_phone' => '************',
            'confirmed' => true
        ]);

        OrderItem::factory()->create([
            'product_id' => $product->id,
            'order_id' => $order->id,
            'stock_status' => 'out',
            'qty' => 3,
            'fulfilled_qty' => 2,
        ]);

        (new SendRecurringOrderConfirmedNotification)
            ->handle(new RecurringOrderWasConfirmed($order));

        Notification::assertSentTo(
            $order,
            RecurringOrderConfirmed::class,
            function (RecurringOrderConfirmed $notification) use ($order) {
                $message = $notification->toTwilio($order)->content;
                return Str::contains($message, 'ACTION NEEDED!');
            }
        );
    }

    #[Test]
    public function it_sends_expected_twilio_notification_message_when_some_order_items_are_out_of_stock(): void
    {
        Notification::fake();
        Setting::updateOrCreate(['key' => 'recurring_orders_reorder_sms_enabled'], ['value' => true]);

        $user = User::factory()->create(['phone' => '']);
        $product = Product::factory()->create(['inventory' => 4]);
        $order = Order::factory()->create([
            'customer_id' => $user->id,
            'customer_phone' => '************',
            'confirmed' => true
        ]);

        OrderItem::factory()->create([
            'product_id' => $product->id,
            'order_id' => $order->id,
            'stock_status' => 'full',
            'qty' => 3,
            'fulfilled_qty' => 3,
        ]);

        OrderItem::factory()->create([
            'product_id' => $product->id,
            'order_id' => $order->id,
            'stock_status' => 'short',
            'qty' => 3,
            'fulfilled_qty' => 1,
        ]);

        (new SendRecurringOrderConfirmedNotification)
            ->handle(new RecurringOrderWasConfirmed($order));

        Notification::assertSentTo(
            $order,
            RecurringOrderConfirmed::class,
            function (RecurringOrderConfirmed $notification) use ($order) {
                $message = $notification->toTwilio($order)->content;
                return Str::contains($message, 'ACTION NEEDED!');
            }
        );
    }

    #[Test]
    public function it_sends_expected_twilio_notification_when_there_is_a_valid_customer_phone(): void
    {
        Notification::fake();

        Setting::updateOrCreate(['key' => 'recurring_orders_reorder_sms_enabled'], ['value' => true]);

        $user = User::factory()->create(['phone' => '************']);
        $order = Order::factory()->create([
            'customer_id' => $user->id,
            'customer_phone' => ''
        ]);

        (new SendRecurringOrderConfirmedNotification)
            ->handle(new RecurringOrderWasConfirmed($order));

        Notification::assertSentTo(
            $order,
            RecurringOrderConfirmed::class,
            function (RecurringOrderConfirmed $notification, $channels, $notifiable) use ($order) {
                return $notifiable->id === $order->id
                    && in_array(TwilioChannel::class, $channels);
            }
        );
    }

    #[Test]
    public function it_does_not_send_twilio_notification_when_there_is_not_a_valid_phone(): void
    {
        Notification::fake();

        Setting::updateOrCreate(['key' => 'recurring_orders_reorder_sms_enabled'], ['value' => true]);

        $user = User::factory()->create(['phone' => '222']);
        $order = Order::factory()->create([
            'customer_id' => $user->id,
            'customer_phone' => '333'
        ]);


        (new SendRecurringOrderConfirmedNotification)
            ->handle(new RecurringOrderWasConfirmed($order));

        Notification::assertSentTo(
            $order,
            RecurringOrderConfirmed::class,
            function (RecurringOrderConfirmed $notification, $channels, $notifiable) use ($order) {
                return $notifiable->id === $order->id
                    && ! in_array(TwilioChannel::class, $channels);
            }
        );
    }

    #[Test]
    public function it_does_not_send_any_notification_when_schedule_reminders_are_disabled_globally(): void
    {
        Notification::fake();

        Setting::updateOrCreate(['key' => 'recurring_orders_reorder_sms_enabled'], ['value' => false]);

        $user = User::factory()->create();

        $order = Order::factory()->create([
            'customer_id' => $user->id,
            'customer_phone' => '************'
        ]);

        (new SendRecurringOrderConfirmedNotification)
            ->handle(new RecurringOrderWasConfirmed($order));

        Notification::assertSentTo(
            $order,
            RecurringOrderConfirmed::class,
            function (RecurringOrderConfirmed $notification, $channels, $notifiable) use ($order) {
                return ! in_array(TwilioChannel::class, $channels);
            }
        );
    }

    #[Test]
    public function it_delays_notifications_during_early_quiet_hours_when_sms_is_enabled(): void
    {
        Setting::updateOrCreate(['key' => 'recurring_orders_reorder_sms_enabled'], ['value' => true]);

        $now = Carbon::create('today 7:00AM');

        Carbon::setTestNow($now);

        Notification::fake();

        /** @var Order $order */
        $order = Order::factory()->create();

        (new SendRecurringOrderConfirmedNotification)->handle(new RecurringOrderWasConfirmed($order));

        Notification::assertSentTo($order, RecurringOrderConfirmed::class, function ($notification) {
            return Carbon::create('today 8:00AM')->eq($notification->delay);
        });

        Carbon::setTestNow();
    }

    #[Test]
    public function it_delays_notifications_during_early_late_hours_when_sms_is_enabled(): void
    {
        Setting::updateOrCreate(['key' => 'recurring_orders_reorder_sms_enabled'], ['value' => true]);

        $now = Carbon::create('today 10:00PM');

        Carbon::setTestNow($now);

        Notification::fake();

        /** @var Order $order */
        $order = Order::factory()->create();

        (new SendRecurringOrderConfirmedNotification)->handle(new RecurringOrderWasConfirmed($order));

        Notification::assertSentTo($order, RecurringOrderConfirmed::class, function ($notification) {
            return Carbon::create('tomorrow 8:00AM')->eq($notification->delay);
        });

        Carbon::setTestNow();
    }

    #[Test]
    public function it_does_not_delay_notifications_during_early_quiet_hours_when_sms_is_not_enabled(): void
    {
        Setting::updateOrCreate(['key' => 'recurring_orders_reorder_sms_enabled'], ['value' => false]);

        $now = Carbon::create('today 7:00AM');

        Carbon::setTestNow($now);

        Notification::fake();

        /** @var Order $order */
        $order = Order::factory()->create();

        (new SendRecurringOrderConfirmedNotification)->handle(new RecurringOrderWasConfirmed($order));

        Notification::assertSentTo($order, RecurringOrderConfirmed::class, function ($notification) use ($now) {
            return $now->eq($notification->delay);
        });

        Carbon::setTestNow();
    }

    #[Test]
    public function it_does_not_delay_notifications_during_late_quiet_hours_when_sms_is_not_enabled(): void
    {
        Setting::updateOrCreate(['key' => 'recurring_orders_reorder_sms_enabled'], ['value' => false]);

        $now = Carbon::create('today 10:00PM');

        Carbon::setTestNow($now);

        Notification::fake();

        /** @var Order $order */
        $order = Order::factory()->create();

        (new SendRecurringOrderConfirmedNotification)->handle(new RecurringOrderWasConfirmed($order));

        Notification::assertSentTo($order, RecurringOrderConfirmed::class, function ($notification) use ($now) {
            return $now->eq($notification->delay);
        });

        Carbon::setTestNow();
    }

    #[Test]
    public function it_does_not_delay_notifications_outside_late_quiet_hours_when_sms_is_enabled(): void
    {
        Setting::updateOrCreate(['key' => 'recurring_orders_reorder_sms_enabled'], ['value' => true]);

        $now = Carbon::create('today 9:59PM');

        Carbon::setTestNow($now);

        Notification::fake();

        /** @var Order $order */
        $order = Order::factory()->create();

        (new SendRecurringOrderConfirmedNotification)
            ->handle(new RecurringOrderWasConfirmed($order));

        Notification::assertSentTo($order, RecurringOrderConfirmed::class, function ($notification) use ($now) {
            return $now->eq($notification->delay);
        });
    }
}
