<?php

namespace Tests\Unit\Billing;

use App\Billing\Stripe\StripeStandardAccount;
use App\Contracts\Billing;
use App\Models\Setting;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class BillingTest extends TenantTestCase
{
    #[Test]
    public function it_can_resolve_the_billing_class(): void
    {
        Setting::updateOrCreate(['key' => 'payment_gateway'], ['value' => '']);
        Setting::updateOrCreate(['key' => 'stripe_account_type'], ['value' => '']);

        $gateway = app(Billing::class);
        $this->assertInstanceOf(StripeStandardAccount::class, $gateway);

        Setting::updateOrCreate(['key' => 'stripe_account_type'], ['value' => 'standard']);
        Setting::updateOrCreate(['key' => 'stripe_secret_key'], ['value' => encrypt('standard_123')]);

        $gateway = app(Billing::class);
        $this->assertInstanceOf(StripeStandardAccount::class, $gateway);
    }
}
