<?php

namespace Tests\Unit\Geocoding;

use App\Exceptions\NoGeocodeResultsException;
use App\Services\Geocoding\GeocodedAddress;
use App\Services\Geocoding\GoogleGeocoder;
use GuzzleHttp\Promise\PromiseInterface;
use Illuminate\Http\Client\Request;
use Illuminate\Support\Facades\Http;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class GoogleGeocoderTest extends TenantTestCase
{
    #[Test]
    public function it_can_geocode_an_address_string_correctly(): void
    {
        Http::fake([ 'https://maps.googleapis.com/*' => $this->geocodeResult() ]);
        config(['services.google.geocoder_api_key' => 'some-api-key']);

        $address_string = ' 123 Fake St, Fakeville, FK, 12345 ';

        $result = (new GoogleGeocoder)->fromAddress($address_string);

        $this->assertEquals(new GeocodedAddress(
            123.45,
            67.89,
            'Fakeville',
            'FK',
            '12345',
            'country response',
            1
        ), $result);

        Http::assertSent(function (Request $request) use ($address_string) {
            return $request->method() && 'GET' &&
                $request->url() && 'https://maps.googleapis.com/maps/api/geocode/json?' &&
                $request['address'] == trim($address_string) &&
                $request['key'] == 'some-api-key';
        });
    }

    #[Test]
    public function it_can_geocode_an_address_array_correctly(): void
    {
        Http::fake([ 'https://maps.googleapis.com/*' => $this->geocodeResult() ]);
        config(['services.google.geocoder_api_key' => 'some-api-key']);

        $result = (new GoogleGeocoder)->fromAddressParts([
            'street' => ' 123 Fake St ',
            'city' => ' Fakeville ',
            'state' => ' FK ',
            'zip' => ' 12345 '
        ]);

        $this->assertEquals(new GeocodedAddress(
            123.45,
            67.89,
            'Fakeville',
            'FK',
            '12345',
            'country response',
            1
        ), $result);

        Http::assertSent(function (Request $request) {
            return $request->method() && 'GET' &&
                $request->url() && 'https://maps.googleapis.com/maps/api/geocode/json?' &&
                $request['address'] == '123 Fake St, Fakeville, FK, 12345' &&
                $request['key'] == 'some-api-key';
        });
    }

    #[Test]
    public function it_can_geocode_a_zipcode_correctly(): void
    {
        Http::fake([ 'https://maps.googleapis.com/*' => $this->geocodeResult() ]);
        config(['services.google.geocoder_api_key' => 'some-api-key']);

        $result = (new GoogleGeocoder)->fromZipcode(' 12345 ');

        $this->assertEquals(new GeocodedAddress(
            123.45,
            67.89,
            'Fakeville',
            'FK',
            '12345',
            'country response',
            1
        ), $result);

        Http::assertSent(function (Request $request) {
            return $request->method() && 'GET' &&
                $request->url() && 'https://maps.googleapis.com/maps/api/geocode/json?' &&
                $request['address'] == '12345' &&
                $request['key'] == 'some-api-key';
        });
    }

    #[Test]
    public function it_can_geocode_a_zipcode_when_neighborhood_instead_of_locality_present_in_the_result(): void
    {
        Http::fake([ 'https://maps.googleapis.com/*' => Http::response([
            'results' => [
                [
                    'address_components' => [
                        [
                            "long_name" => "Fakeville",
                            "short_name" => "Fakeville",
                            "types" => ["neighborhood", "political"]
                        ],
                        [
                            "long_name" => "Fakest Countyville",
                            "short_name" => "FK",
                            "types" => ["administrative_area_level_1", "political"]
                        ],
                        [
                            "long_name" => "country response",
                            "short_name" => "country response",
                            "types" => ["country", "political"]
                        ],
                        [
                            "long_name" => "12345",
                            "short_name" => "12345",
                            "types" => ["postal_code"]
                        ]
                    ],
                    'geometry' => ["location" => ["lat" => 123.45, "lng" => 67.89], "location_type" => "ROOFTOP"],
                    "types" => ["street_address"]
                ]
            ],
            'status' => "OK",
        ])]);
        config(['services.google.geocoder_api_key' => 'some-api-key']);

        $result = (new GoogleGeocoder)->fromZipcode(' 12345 ');

        $this->assertEquals(new GeocodedAddress(
            123.45,
            67.89,
            'Fakeville',
            'FK',
            '12345',
            'country response',
            1
        ), $result);

        Http::assertSent(function (Request $request) {
            return $request->method() && 'GET' &&
                $request->url() && 'https://maps.googleapis.com/maps/api/geocode/json?' &&
                $request['address'] == '12345' &&
                $request['key'] == 'some-api-key';
        });
    }

    #[Test]
    public function it_can_geocode_a_zipcode_when_administrative_area_level_3_of_locality_present_in_the_result(): void
    {
        Http::fake([ 'https://maps.googleapis.com/*' => Http::response([
            'results' => [
                [
                    'address_components' => [
                        [
                            "long_name" => "Fakeville",
                            "short_name" => "Fakeville",
                            "types" => ["administrative_area_level_3", "political"]
                        ],
                        [
                            "long_name" => "Fakest Countyville",
                            "short_name" => "FK",
                            "types" => ["administrative_area_level_1", "political"]
                        ],
                        [
                            "long_name" => "country response",
                            "short_name" => "country response",
                            "types" => ["country", "political"]
                        ],
                        [
                            "long_name" => "12345",
                            "short_name" => "12345",
                            "types" => ["postal_code"]
                        ]
                    ],
                    'geometry' => ["location" => ["lat" => 123.45, "lng" => 67.89], "location_type" => "ROOFTOP"],
                    "types" => ["street_address"]
                ]
            ],
            'status' => "OK",
        ])]);
        config(['services.google.geocoder_api_key' => 'some-api-key']);

        $result = (new GoogleGeocoder)->fromZipcode(' 12345 ');

        $this->assertEquals(new GeocodedAddress(
            123.45,
            67.89,
            'Fakeville',
            'FK',
            '12345',
            'country response',
            1
        ), $result);

        Http::assertSent(function (Request $request) {
            return $request->method() && 'GET' &&
                $request->url() && 'https://maps.googleapis.com/maps/api/geocode/json?' &&
                $request['address'] == '12345' &&
                $request['key'] == 'some-api-key';
        });
    }

    #[Test]
    public function it_throws_an_exception_when_geocoding_an_address_string_that_returns_no_results(): void
    {
        Http::fake([ 'https://maps.googleapis.com/*' => $this->geocodeEmptyResult() ]);
        config(['services.google.geocoder_api_key' => 'some-api-key']);

        $address_string = ' 123 Fake St, Fakeville, FK, 12345 ';

        $this->expectException(NoGeocodeResultsException::class);

        (new GoogleGeocoder)->fromAddress($address_string);

        Http::assertSent(function (Request $request) use ($address_string) {
            return $request->method() && 'GET' &&
                $request->url() && 'https://maps.googleapis.com/maps/api/geocode/json?' &&
                $request['address'] == trim($address_string) &&
                $request['key'] == 'some-api-key';
        });
    }

    private function geocodeResult($accuracy = 1): PromiseInterface
    {
        return Http::response([
            'results' => [
                [
                    'address_components' => [
                        [
                            "long_name" => "Fakeville",
                            "short_name" => "Fakeville",
                            "types" => ["locality", "political"]
                        ],
                        [
                            "long_name" => "Fakest Countyville",
                            "short_name" => "FK",
                            "types" => ["administrative_area_level_1", "political"]
                        ],
                        [
                            "long_name" => "country response",
                            "short_name" => "country response",
                            "types" => ["country", "political"]
                        ],
                        [
                            "long_name" => "12345",
                            "short_name" => "12345",
                            "types" => ["postal_code"]
                        ]
                    ],
                    'geometry' => ["location" => ["lat" => 123.45, "lng" => 67.89], "location_type" => "ROOFTOP"],
                    "types" => ["street_address"]
                ]
            ],
            'status' => "OK",
        ]);
    }

    private function geocodeEmptyResult(): PromiseInterface
    {
        return Http::response(['results' => [], 'status' => 'ZERO_RESULTS',]);
    }
}
