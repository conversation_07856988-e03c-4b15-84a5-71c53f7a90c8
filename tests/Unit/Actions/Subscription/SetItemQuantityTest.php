<?php

namespace Tests\Unit\Actions\Subscription;

use App\Actions\Subscription\SetItemQuantity;
use App\Exceptions\BackOrderException;
use App\Exceptions\ProductNotFoundException;
use App\Models\Product;
use App\Models\RecurringOrder;
use App\Models\RecurringOrderItem;
use Exception;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class SetItemQuantityTest extends TenantTestCase
{
    #[Test]
    public function it_throws_an_exception_when_attempting_to_update_quantity_to_a_item_whose_product_has_been_removed(): void
    {
        /** @var RecurringOrderItem $item */
        $item = RecurringOrderItem::factory()->create(['qty' => 1]);

        $item->product->delete();

        $this->expectException(ProductNotFoundException::class);
        $this->expectExceptionMessage("{$item->product->title} is no longer available. You can not add any more to your subscription.");

        (new SetItemQuantity)->handle($item->order, $item, 2);
    }

    #[Test]
    public function it_throws_an_exception_when_attempting_to_increase_quantity_greater_than_the_product_limit(): void
    {
        /** @var Product $product */
        $product = Product::factory()->create(['settings' => ['quantity_limit' => 2]]);

        /** @var RecurringOrderItem $item */
        $item = RecurringOrderItem::factory()->create(['product_id' => $product->id, 'qty' => 1]);

        $this->expectException(BackOrderException::class);
        $this->expectExceptionMessage("There is a limit of 2 per customer for this product.");

        (new SetItemQuantity)->handle($item->order, $item, 3);
    }

    #[Test]
    public function it_throws_an_exception_when_attempting_to_increase_quantity_without_available_quantity(): void
    {
        /** @var Product $product */
        $product = Product::factory()->create(['track_inventory' => 'yes', 'inventory' => 1]);

        /** @var RecurringOrderItem $item */
        $item = RecurringOrderItem::factory()->create(['product_id' => $product->id, 'qty' => 1]);

        $this->expectException(BackOrderException::class);
        $this->expectExceptionMessage("There is not enough {$product->title} left in stock.");

        (new SetItemQuantity)->handle($item->order, $item, 3);
    }

    #[Test]
    public function it_handles_no_change_in_quantity(): void
    {
        /** @var RecurringOrderItem $item */
        $item = RecurringOrderItem::factory()->create(['qty' => 1]);

        (new SetItemQuantity)->handle($item->order, $item, 1);

        $this->assertDatabaseHas(RecurringOrderItem::class, [
            'id' => $item->id,
            'qty' => 1,
        ]);
    }

    #[Test]
    public function it_increases_quantity_of_an_item(): void
    {
       /** @var RecurringOrderItem $item */
        $item = RecurringOrderItem::factory()->create(['qty' => 1]);

        $result = (new SetItemQuantity)->handle($item->order, $item, 2);

        $this->assertNotEquals(1, $result->qty);

        $this->assertDatabaseHas(RecurringOrderItem::class, [
            'id' => $result->id,
            'qty' => 2,
        ]);
    }

    #[Test]
    public function it_does_not_decrement_inventory_when_increasing_quantity_of_an_item(): void
    {
        /** @var RecurringOrder $subscription */
        $subscription = RecurringOrder::factory()->create();

        /** @var Product $product */
        $product = Product::factory()->create(['track_inventory' => 'yes', 'inventory' => 10]);

        /** @var RecurringOrderItem $item */
        $item = RecurringOrderItem::factory()->create(['order_id' => $subscription->id, 'product_id' => $product->id, 'qty' => 1]);

        $result = (new SetItemQuantity)->handle($subscription, $item, 2);

        $this->assertInstanceOf(RecurringOrderItem::class, $result);

        $this->assertDatabaseHas(Product::class, [
            'id' => $product->id,
            'inventory' => 10
        ]);
    }

    #[Test]
    public function it_decreases_quantity_of_an_item(): void
    {
        /** @var RecurringOrderItem $item */
        $item = RecurringOrderItem::factory()->create(['qty' => 3]);

        $result = (new SetItemQuantity)->handle($item->order, $item, 1);

        $this->assertDatabaseHas(RecurringOrderItem::class, [
            'id' => $result->id,
            'qty' => 1,
        ]);
    }

    #[Test]
    public function it_does_not_increment_inventory_when_decreasing_quantity_of_an_item(): void
    {
        /** @var RecurringOrder $subscription */
        $subscription = RecurringOrder::factory()->create();

        /** @var Product $product */
        $product = Product::factory()->create(['track_inventory' => 'yes', 'inventory' => 10]);

        /** @var RecurringOrderItem $item */
        $item = RecurringOrderItem::factory()->create(['order_id' => $subscription->id, 'product_id' => $product->id, 'qty' => 3]);

        $result = (new SetItemQuantity)->handle($subscription, $item, 1);

        $this->assertInstanceOf(RecurringOrderItem::class, $result);

        $this->assertDatabaseHas(Product::class, [
            'id' => $product->id,
            'inventory' => 10
        ]);
    }

    #[Test]
    public function it_decrease_the_quantity_of_the_last_recurring_item(): void
    {
        /** @var RecurringOrderItem $item */
        $item = RecurringOrderItem::factory()->create(['qty' => 3, 'type' => 'recurring']);
        RecurringOrderItem::factory()->create(['order_id' => $item->order_id, 'qty' => 3, 'type' => 'promo']);

        (new SetItemQuantity)->handle($item->order, $item, 2);

        $this->assertDatabaseHas(RecurringOrderItem::class, [
            'id' => $item->id,
            'qty' => 2
        ]);
    }

    #[Test]
    public function it_cannot_remove_an_addon_item_with_at_least_one_remaining_recurring_item(): void
    {
        /** @var RecurringOrderItem $item */
        $item = RecurringOrderItem::factory()->create(['qty' => 1, 'type' => 'recurring']);

        /** @var RecurringOrderItem $addon */
        $addon = RecurringOrderItem::factory()->create(['order_id' => $item->order_id, 'qty' => 3, 'type' => 'addon']);

        (new SetItemQuantity)->handle($addon->order, $addon, 0);

        $this->assertDatabaseHas(RecurringOrderItem::class, [
            'id' => $item->id,
        ]);

        $this->assertDatabaseMissing(RecurringOrderItem::class, [
            'id' => $addon->id,
        ]);
    }

    #[Test]
    public function it_cannot_remove_the_last_recurring_item(): void
    {
        /** @var RecurringOrderItem $item */
        $item = RecurringOrderItem::factory()->create(['qty' => 3, 'type' => 'recurring']);
        RecurringOrderItem::factory()->create(['order_id' => $item->order_id, 'qty' => 3, 'type' => 'promo']);

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('The last recurring item of a subscription cannot be removed.');

        (new SetItemQuantity)->handle($item->order, $item, 0);

        $this->assertDatabaseHas(RecurringOrderItem::class, [
            'id' => $item->id,
        ]);
    }

    #[Test]
    public function it_removes_item_when_setting_quantity_to_zero(): void
    {
        /** @var RecurringOrderItem $item */
        $item = RecurringOrderItem::factory()->create(['qty' => 3, 'type' => 'recurring']);
        RecurringOrderItem::factory()->create(['order_id' => $item->order_id, 'qty' => 3, 'type' => 'recurring']);

        $result = (new SetItemQuantity)->handle($item->order, $item, 0);

        $this->assertNull($result);

        $this->assertDatabaseMissing(RecurringOrderItem::class, [
            'id' => $item->id,
        ]);
    }

    #[Test]
    public function it_does_not_update_promo_item_quantities(): void
    {
        /** @var RecurringOrder $subscription */
        $subscription = RecurringOrder::factory()->create();

        /** @var RecurringOrderItem $item */
        $item = RecurringOrderItem::factory()->create(['order_id' => $subscription->id, 'qty' => 2, 'type' => 'promo']);

        $result = (new SetItemQuantity)->handle($subscription, $item, 3);

        $this->assertEquals($item->id, $result->id);
        $this->assertEquals(2, $result->qty);

        $this->assertDatabaseHas(RecurringOrderItem::class, [
            'id' => $item->id,
            'qty' => 2
        ]);

        $result = (new SetItemQuantity)->handle($subscription, $item, 1);

        $this->assertEquals($item->id, $result->id);
        $this->assertEquals(2, $result->qty);

        $this->assertDatabaseHas(RecurringOrderItem::class, [
            'id' => $item->id,
            'qty' => 2
        ]);

        $result = (new SetItemQuantity)->handle($subscription, $item, 0);

        $this->assertEquals($item->id, $result->id);
        $this->assertEquals(2, $result->qty);

        $this->assertDatabaseHas(RecurringOrderItem::class, [
            'id' => $item->id,
            'qty' => 2
        ]);
    }
}
