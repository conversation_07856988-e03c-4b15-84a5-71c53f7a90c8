<?php

namespace Tests\Unit\Actions;

use App\Actions\ActivateMultiDaySchedule;
use App\Actions\CreateRepeatingDates;
use App\Models\Date;
use App\Models\Schedule;
use Carbon\Carbon;
use Mockery\MockInterface;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class ActivateMultiDayScheduleTest extends TenantTestCase
{
    #[Test]
    public function it_can_create_repeating_dates_for_custom_schedule(): void
    {
        Carbon::setTestNow(Carbon::parse('2022-06-21'));

        $expectedArgs = [
            [
                'today' => today(),
                'deliveryDate' => Carbon::parse('2022-06-24'),
                'deadlineDate' => Carbon::parse('2022-06-22'),
            ],
            [
                'today' => today(),
                'deliveryDate' => Carbon::parse('2022-06-27'),
                'deadlineDate' => Carbon::parse('2022-06-25'),
            ],
            [
                'today' => today(),
                'deliveryDate' => Carbon::parse('2022-06-28'),
                'deadlineDate' => Carbon::parse('2022-06-26'),
            ],
        ];

        $schedule = Schedule::factory()->create([
            'first_delivery_date' => null,
            'first_delivery_deadline' => null,
            'active' => false,
            'ordering_in_advance' => 2,
            'type_id' => Schedule::TYPE_CUSTOM
        ]);

        $expected_dates = [
            [
                'schedule_id' => $schedule->id,
                'user_id' => null,
                'order_start_date' => today()->format('Y-m-d'),
                'order_end_date' => Carbon::parse('2022-06-22'),
                'pickup_date' => Carbon::parse('2022-06-24'),
            ],
            [
                'schedule_id' => $schedule->id,
                'user_id' => null,
                'order_start_date' => today()->format('Y-m-d'),
                'order_end_date' => Carbon::parse('2022-06-25'),
                'pickup_date' => Carbon::parse('2022-06-27'),
            ],
            [
                'schedule_id' => $schedule->id,
                'user_id' => null,
                'order_start_date' => today()->format('Y-m-d'),
                'order_end_date' => Carbon::parse('2022-06-26'),
                'pickup_date' => Carbon::parse('2022-06-28'),
            ]
        ];

        $this->mock(CreateRepeatingDates::class, function (MockInterface $mock) use ($expectedArgs, $expected_dates, $schedule) {
            foreach ($expectedArgs as $index => $arg) {
                $mock->shouldReceive('execute')
                    ->with(
                        \Mockery::on(fn ($today) => $arg['today']->format('Y-m-d') === $today->format('Y-m-d')),
                        \Mockery::on(fn ($deadlineDate) => $arg['deadlineDate']->format('Y-m-d') === $deadlineDate->format('Y-m-d')),
                        \Mockery::on(fn ($deliveryDate) => $arg['deliveryDate']->format('Y-m-d') === $deliveryDate->format('Y-m-d')),
                        null,
                        $schedule->id,
                        7,
                        'Y-m-d',
                        false
                    )->andReturn($expected_dates[$index]);
            }
        });

        app(ActivateMultiDaySchedule::class)->execute([
            'days_of_week' => [1,2,5],
            'first_delivery_date' => Carbon::parse('2022-06-24'),
            'order_cutoff' => 2,
            'ordering_in_advance' => 1
        ], $schedule);

        $this->assertDatabaseHas(Schedule::class, [
            'id' => $schedule->id,
            'first_delivery_date' => Carbon::parse('2022-06-24')->format('Y-m-d H:i:s'),
            'first_delivery_deadline' => Carbon::parse('2022-06-22')->format('Y-m-d H:i:s'),
            'active' => 1,
            'ordering_in_advance' => 1,
        ]);

        $this->assertTrue($schedule->extra_attributes['days_of_week'] === [1,2,5]);

        foreach ($expected_dates as $expected_date) {
            $this->assertDatabaseHas(Date::class, $expected_date);
        }

        Carbon::setTestNow();
    }

    #[Test]
    public function it_can_create_repeating_dates_for_repeating_schedule(): void
    {
        Carbon::setTestNow(Carbon::parse('2022-06-21'));

        $expectedArgs = [
            [
                'today' => today(),
                'deliveryDate' => Carbon::parse('2022-06-24'),
                'deadlineDate' => Carbon::parse('2022-06-22'),
            ],
            [
                'today' => today(),
                'deliveryDate' => Carbon::parse('2022-06-27'),
                'deadlineDate' => Carbon::parse('2022-06-25'),
            ],
            [
                'today' => today(),
                'deliveryDate' => Carbon::parse('2022-06-28'),
                'deadlineDate' => Carbon::parse('2022-06-26'),
            ],
        ];

        $schedule = Schedule::factory()->create([
            'first_delivery_date' => null,
            'first_delivery_deadline' => null,
            'active' => false,
            'ordering_in_advance' => 2,
            'type_id' => Schedule::TYPE_REPEATING
        ]);

        $expected_dates = [
            [
                'schedule_id' => $schedule->id,
                'user_id' => null,
                'order_start_date' => today()->format('Y-m-d'),
                'order_end_date' => Carbon::parse('2022-06-22'),
                'pickup_date' => Carbon::parse('2022-06-24'),
            ],
            [
                'schedule_id' => $schedule->id,
                'user_id' => null,
                'order_start_date' => today()->format('Y-m-d'),
                'order_end_date' => Carbon::parse('2022-06-25'),
                'pickup_date' => Carbon::parse('2022-06-27'),
            ],
            [
                'schedule_id' => $schedule->id,
                'user_id' => null,
                'order_start_date' => today()->format('Y-m-d'),
                'order_end_date' => Carbon::parse('2022-06-26'),
                'pickup_date' => Carbon::parse('2022-06-28'),
            ]
        ];

        $this->mock(CreateRepeatingDates::class, function (MockInterface $mock) use ($expectedArgs, $expected_dates, $schedule) {
            foreach ($expectedArgs as $index => $arg) {
                $mock->shouldReceive('execute')
                    ->with(
                        \Mockery::on(fn ($today) => $arg['today']->format('Y-m-d') === $today->format('Y-m-d')),
                        \Mockery::on(fn ($deadlineDate) => $arg['deadlineDate']->format('Y-m-d') === $deadlineDate->format('Y-m-d')),
                        \Mockery::on(fn ($deliveryDate) => $arg['deliveryDate']->format('Y-m-d') === $deliveryDate->format('Y-m-d')),
                        null,
                        $schedule->id,
                        7,
                        'Y-m-d',
                        true
                    )->andReturn($expected_dates[$index]);
            }
        });

        app(ActivateMultiDaySchedule::class)->execute([
            'days_of_week' => [1,2,5],
            'first_delivery_date' => Carbon::parse('2022-06-24'),
            'order_cutoff' => 2,
            'ordering_in_advance' => 1
        ], $schedule);

        $this->assertDatabaseHas(Schedule::class, [
            'id' => $schedule->id,
            'first_delivery_date' => Carbon::parse('2022-06-24')->format('Y-m-d H:i:s'),
            'first_delivery_deadline' => Carbon::parse('2022-06-22')->format('Y-m-d H:i:s'),
            'active' => 1,
            'ordering_in_advance' => 1,
        ]);

        $this->assertTrue($schedule->extra_attributes['days_of_week'] === [1,2,5]);

        foreach ($expected_dates as $expected_date) {
            $this->assertDatabaseHas(Date::class, $expected_date);
        }

        Carbon::setTestNow();
    }

    #[Test]
    public function it_throws_an_exception_when_the_first_delivery_date_is_in_the_past(): void {
        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('The first delivery date must be in the future.');

        $schedule = Schedule::factory()->create();

        app(ActivateMultiDaySchedule::class)->execute([
            'days_of_week' => [1,2,5],
            'first_delivery_date' => today()->subDay(),
            'order_cutoff' => 2,
        ], $schedule);
    }

    #[Test]
    public function it_throws_an_exception_when_the_first_delivery_date_does_not_match_delivery_days(): void {
        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('The first delivery date day of the week must match one of your delivery days.');

        $schedule = Schedule::factory()->create();

        app(ActivateMultiDaySchedule::class)->execute([
            'days_of_week' => [1],
            'first_delivery_date' => today()->startOfWeek(Carbon::SUNDAY)->addWeek(),
            'order_cutoff' => 2,
        ], $schedule);
    }
}
