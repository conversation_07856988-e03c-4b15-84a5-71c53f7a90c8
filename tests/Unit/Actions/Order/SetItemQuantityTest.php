<?php

namespace Tests\Unit\Actions\Order;

use App\Actions\Order\SetItemQuantity;
use App\Exceptions\BackOrderException;
use App\Exceptions\ProductNotFoundException;
use App\Models\GiftCertificate;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Pickup;
use App\Models\Product;
use App\Models\RecurringOrder;
use Exception;
use Illuminate\Support\Carbon;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class SetItemQuantityTest extends TenantTestCase
{
    #[Test]
    public function it_throws_an_exception_when_attempting_to_increase_quantity_of_an_item_for_an_order_that_has_already_been_paid(): void
    {
        /** @var Order $order */
        $order = Order::factory()->create(['paid' => true]);

        /** @var OrderItem $item */
        $item = OrderItem::factory()->create(['order_id' => $order->id, 'qty' => 1]);

        $this->expectException(BackOrderException::class);
        $this->expectExceptionMessage('This order has already been paid for and cannot be updated.');

        (new SetItemQuantity)->handle($item->order, $item, 2);
    }

    #[Test]
    public function it_throws_an_exception_when_attempting_to_update_quantity_to_a_item_whose_product_has_been_removed(): void
    {
        /** @var OrderItem $item */
        $item = OrderItem::factory()->create(['qty' => 1]);
        $item->product->delete();

        $this->expectException(ProductNotFoundException::class);
        $this->expectExceptionMessage("{$item->title} is no longer available. You can not add any more to your order.");

        (new SetItemQuantity)->handle($item->order, $item, 2);
    }

    #[Test]
    public function it_throws_an_exception_when_attempting_to_increase_quantity_greater_than_the_product_limit(): void
    {
        /** @var Product $product */
        $product = Product::factory()->create(['settings' => ['quantity_limit' => 2]]);

        /** @var OrderItem $item */
        $item = OrderItem::factory()->create(['product_id' => $product->id, 'qty' => 1]);

        $this->expectException(BackOrderException::class);
        $this->expectExceptionMessage("There is a limit of 2 per customer for this product.");

        (new SetItemQuantity)->handle($item->order, $item, 3);
    }

    #[Test]
    public function it_throws_an_exception_when_attempting_to_increase_quantity_without_available_quantity(): void
    {
        /** @var Product $product */
        $product = Product::factory()->create(['track_inventory' => 'yes', 'inventory' => 1]);

        /** @var OrderItem $item */
        $item = OrderItem::factory()->create(['product_id' => $product->id, 'qty' => 1]);

        $this->expectException(BackOrderException::class);
        $this->expectExceptionMessage("There is not enough {$product->title} left in stock.");

        (new SetItemQuantity)->handle($item->order, $item, 2);
    }

    #[Test]
    public function it_handles_no_change_in_quantity(): void
    {
        /** @var OrderItem $item */
        $item = OrderItem::factory()->create(['qty' => 1, 'subtotal' => 1, 'fulfilled_qty' => 1]);

        $result = (new SetItemQuantity)->handle($item->order, $item, 1);

        $this->assertEquals(1, $result->subtotal);

        $this->assertDatabaseHas(OrderItem::class, [
            'id' => $item->id,
            'qty' => 1,
            'fulfilled_qty' => 1,
            'weight' => $item->weight,
            'subtotal' => 1
        ]);
    }

    #[Test]
    public function it_increases_quantity_of_a_one_time_order_item(): void
    {
       /** @var OrderItem $item */
        $item = OrderItem::factory()->create(['qty' => 1, 'subtotal' => 1, 'fulfilled_qty' => 5]);

        $result = (new SetItemQuantity)->handle($item->order, $item, 2);

        $this->assertNotEquals(1, $result->subtotal);

        $this->assertDatabaseHas(OrderItem::class, [
            'id' => $result->id,
            'qty' => 2,
            'fulfilled_qty' => 2,
            'weight' => $item->product->weight * 2,
            'subtotal' => $result->subtotal
        ]);
    }

    #[Test]
    public function it_increases_quantity_of_a_subscription_order_item(): void
    {
        $subscription = RecurringOrder::factory()->create();
        $order = Order::factory()->create(['blueprint_id' => $subscription->id]);

        /** @var OrderItem $item */
        $item = OrderItem::factory()->create(['order_id' => $order->id, 'qty' => 1, 'subtotal' => 1, 'fulfilled_qty' => 1]);

        $result = (new SetItemQuantity)->handle($item->order, $item, 2);

        $this->assertNotEquals(1, $result->subtotal);

        $this->assertDatabaseHas(OrderItem::class, [
            'id' => $result->id,
            'qty' => 2,
            'fulfilled_qty' => 2,
            'weight' => $item->product->weight * 2,
            'subtotal' => $result->subtotal
        ]);
    }

    #[Test]
    public function it_does_not_decrement_inventory_when_increasing_quantity_of_an_item_from_a_unconfirmed_order(): void
    {
        /** @var Order $order */
        $order = Order::factory()->create(['confirmed' => false]);

        /** @var Product $product */
        $product = Product::factory()->create(['track_inventory' => 'yes', 'inventory' => 10]);

        /** @var OrderItem $item */
        $item = OrderItem::factory()->create(['order_id' => $order->id, 'product_id' => $product->id, 'qty' => 1, 'fulfilled_qty' => 1]);

        $result = (new SetItemQuantity)->handle($order, $item, 2);

        $this->assertInstanceOf(OrderItem::class, $result);

        $this->assertDatabaseHas(Product::class, [
            'id' => $product->id,
            'inventory' => 10
        ]);
    }

    #[Test]
    public function it_decrements_inventory_when_increasing_quantity_of_an_item_from_a_confirmed_order(): void
    {
        /** @var Order $order */
        $order = Order::factory()->create(['confirmed' => true]);

        /** @var Product $product */
        $product = Product::factory()->create(['track_inventory' => 'yes', 'inventory' => 10]);

        /** @var OrderItem $item */
        $item = OrderItem::factory()->create(['order_id' => $order->id, 'product_id' => $product->id, 'qty' => 1, 'fulfilled_qty' => 1]);

        $this->actingAsAdmin();

        $result = (new SetItemQuantity)->handle($order, $item, 2);

        $this->assertInstanceOf(OrderItem::class, $result);

        $this->assertDatabaseHas(Product::class, [
            'id' => $product->id,
            'inventory' => 9
        ]);
    }

    #[Test]
    public function it_does_not_record_event_when_increasing_quantity_of_an_item_on_a_confirmed_order(): void
    {
        /** @var Order $order */
        $order = Order::factory()->create(['confirmed' => false]);

        /** @var OrderItem $item */
        $item = OrderItem::factory()->create(['order_id' => $order->id, 'qty' => 1, 'fulfilled_qty' => 1]);

        $result = (new SetItemQuantity)->handle($order, $item, 2);

        $this->assertInstanceOf(OrderItem::class, $result);

        $this->assertDatabaseMissing(\App\Models\Event::class, [
            'model_type' => \App\Models\Order::class,
            'model_id' => $order->id,
        ]);
    }

    #[Test]
    public function it_records_event_when_increasing_quantity_of_an_item_from_a_confirmed_order(): void
    {
        Carbon::setTestNow(now());

        /** @var Order $order */
        $order = Order::factory()->create(['confirmed' => true]);

        /** @var OrderItem $item */
        $item = OrderItem::factory()->create(['order_id' => $order->id, 'qty' => 1, 'fulfilled_qty' => 1]);

        $this->actingAsAdmin();

        $result = (new SetItemQuantity)->handle($order, $item, 2);

        $this->assertInstanceOf(OrderItem::class, $result);

        $this->assertDatabaseHas(\App\Models\Event::class, [
            'model_type' => \App\Models\Order::class,
            'model_id' => $order->id,
            'description' => "{$item->title} quantity changed from 1 to 2",
            'event_id' => 'order_item_qty_updated',
            'user_id' => auth()->id(), // So we can know who did this...
            'created_at' => now()->format('Y-m-d H:i:s'),
            'metadata' => json_encode([
                'item_id' => $result->id,
                'old_qty' => 1,
                'qty' => 2,
            ])
        ]);

        Carbon::setTestNow();
    }

    #[Test]
    public function it_decreases_quantity_of_a_one_time_order_item(): void
    {
        /** @var OrderItem $item */
        $item = OrderItem::factory()->create(['qty' => 3, 'subtotal' => 1, 'fulfilled_qty' => 5]);

        $result = (new SetItemQuantity)->handle($item->order, $item, 1);

        $this->assertNotEquals(1, $result->subtotal);

        $this->assertDatabaseHas(OrderItem::class, [
            'id' => $result->id,
            'qty' => 1,
            'fulfilled_qty' => 1,
            'weight' => $item->product->weight,
            'subtotal' => $result->subtotal
        ]);
    }

    #[Test]
    public function it_decreases_quantity_of_a_subscription_order_item(): void
    {
        $subscription = RecurringOrder::factory()->create();
        $order = Order::factory()->create(['blueprint_id' => $subscription->id]);

        /** @var OrderItem $item */
        $item = OrderItem::factory()->create(['order_id' => $order->id, 'qty' => 3, 'subtotal' => 1, 'fulfilled_qty' => 3]);

        $result = (new SetItemQuantity)->handle($item->order, $item, 1);

        $this->assertNotEquals(1, $result->subtotal);

        $this->assertDatabaseHas(OrderItem::class, [
            'id' => $result->id,
            'qty' => 1,
            'fulfilled_qty' => 1,
            'weight' => $item->product->weight,
            'subtotal' => $result->subtotal
        ]);
    }

    #[Test]
    public function it_does_not_decrement_inventory_when_decreasing_quantity_of_an_item_from_a_unconfirmed_order(): void
    {
        /** @var Order $order */
        $order = Order::factory()->create(['confirmed' => false]);

        /** @var Product $product */
        $product = Product::factory()->create(['track_inventory' => 'yes', 'inventory' => 10]);

        /** @var OrderItem $item */
        $item = OrderItem::factory()->create(['order_id' => $order->id, 'product_id' => $product->id, 'qty' => 3, 'fulfilled_qty' => 3]);

        $result = (new SetItemQuantity)->handle($order, $item, 1);

        $this->assertInstanceOf(OrderItem::class, $result);

        $this->assertDatabaseHas(Product::class, [
            'id' => $product->id,
            'inventory' => 10
        ]);
    }

    #[Test]
    public function it_decrements_inventory_when_decreasing_quantity_of_an_item_from_a_confirmed_order(): void
    {
        /** @var Order $order */
        $order = Order::factory()->create(['confirmed' => true, 'subtotal' => 100000]);

        /** @var Product $product */
        $product = Product::factory()->create(['track_inventory' => 'yes', 'inventory' => 10]);

        /** @var OrderItem $item */
        $item = OrderItem::factory()->create(['order_id' => $order->id, 'product_id' => $product->id, 'qty' => 3, 'fulfilled_qty' => 3]);

        $this->actingAsAdmin();

        $result = (new SetItemQuantity)->handle($order, $item, 1);

        $this->assertInstanceOf(OrderItem::class, $result);

        $this->assertDatabaseHas(Product::class, [
            'id' => $product->id,
            'inventory' => 12
        ]);
    }

    #[Test]
    public function it_does_not_record_event_when_decreasing_quantity_of_an_item_on_a_confirmed_order(): void
    {
        /** @var Order $order */
        $order = Order::factory()->create(['confirmed' => false]);

        /** @var OrderItem $item */
        $item = OrderItem::factory()->create(['order_id' => $order->id, 'qty' => 3, 'fulfilled_qty' => 3]);

        $result = (new SetItemQuantity)->handle($order, $item, 1);

        $this->assertInstanceOf(OrderItem::class, $result);

        $this->assertDatabaseMissing(\App\Models\Event::class, [
            'model_type' => \App\Models\Order::class,
            'model_id' => $order->id,
        ]);
    }

    #[Test]
    public function it_records_event_when_decreasing_quantity_of_an_item_from_a_confirmed_order(): void
    {
        Carbon::setTestNow(now());

        /** @var Order $order */
        $order = Order::factory()->create(['confirmed' => true]);

        /** @var OrderItem $item */
        $item = OrderItem::factory()->create(['order_id' => $order->id, 'qty' => 3, 'fulfilled_qty' => 3]);

        $this->actingAsAdmin();

        $result = (new SetItemQuantity)->handle($order, $item, 1);

        $this->assertInstanceOf(OrderItem::class, $result);

        $this->assertDatabaseHas(\App\Models\Event::class, [
            'model_type' => \App\Models\Order::class,
            'model_id' => $order->id,
            'description' => "{$item->title} quantity changed from 3 to 1",
            'event_id' => 'order_item_qty_updated',
            'user_id' => auth()->id(), // So we can know who did this...
            'created_at' => now()->format('Y-m-d H:i:s'),
            'metadata' => json_encode([
                'item_id' => $result->id,
                'old_qty' => 3,
                'qty' => 1,
            ])
        ]);

        Carbon::setTestNow();
    }

    #[Test]
    public function it_updates_gift_card_quantity_when_decreasing_a_gift_card_item(): void
    {
        /** @var Product $product */
        $product = Product::factory()->create(['type_id' => 2]);

        /** @var OrderItem $item */
        $item = OrderItem::factory()->create(['product_id' => $product->id, 'qty' => 3, 'fulfilled_qty' => 3]);
        $certificate = GiftCertificate::factory()->create(['order_item_id' => $item->id, 'qty' => 1]);

        $result = (new SetItemQuantity)->handle($item->order, $item, 1);

        $this->assertInstanceOf(OrderItem::class, $result);

        $this->assertDatabaseHas(GiftCertificate::class, [
            'id' => $certificate->id,
            'order_item_id' => $result->id,
            'qty' => 1,
        ]);
    }

    #[Test]
    public function it_removes_item_when_setting_quantity_to_zero(): void
    {
        /** @var OrderItem $item */
        $item = OrderItem::factory()->create(['qty' => 3, 'subtotal' => 1]);

        $result = (new SetItemQuantity)->handle($item->order, $item, 0);

        $this->assertNull($result);

        $this->assertDatabaseMissing(OrderItem::class, [
            'id' => $item->id,
        ]);
    }

    #[Test]
    public function it_only_setting_promo_item_quantities_to_zero(): void
    {
        /** @var Order $order */
        $order = Order::factory()->create();

        /** @var OrderItem $item */
        $item = OrderItem::factory()->create(['order_id' => $order->id, 'qty' => 2, 'type' => 'promo']);

        $result = (new SetItemQuantity)->handle($order, $item, 3);

        $this->assertEquals($item->id, $result->id);
        $this->assertEquals(2, $result->qty);

        $this->assertDatabaseHas(OrderItem::class, [
            'id' => $item->id,
            'qty' => 2
        ]);

        $result = (new SetItemQuantity)->handle($order, $item, 1);

        $this->assertEquals($item->id, $result->id);
        $this->assertEquals(2, $result->qty);

        $this->assertDatabaseHas(OrderItem::class, [
            'id' => $item->id,
            'qty' => 2
        ]);

        $result = (new SetItemQuantity)->handle($order, $item, 0);

        $this->assertNull($result);
        $this->assertDatabaseMissing(OrderItem::class, ['id' => $item->id]);
    }

    #[Test]
    public function it_throws_an_exception_when_attempting_to_decrease_quantity_under_delivery_method_on_a_confirmed_order(): void
    {
        $pickup = Pickup::factory()->create(['min_customer_orders' => '100.01']);
        $order = Order::factory()->create(['pickup_id' => $pickup->id, 'confirmed' => true, 'subtotal' => 12000]);
        /** @var OrderItem $item */

        /** @var Product $product */
        $product = Product::factory()->create();
        $item = OrderItem::factory()->create([
            'order_id' => $order->id,
            'product_id' => $product->id,
            'qty' => 6,
            'unit_price' => 2000,
            'subtotal' => 12000,
            'unit_of_issue' => 'package'
        ]);

        $this->expectException(Exception::class);
        $this->expectExceptionMessage("Decreasing the quantity this item would cause the subtotal to be below the $100.01 order minimum.");

        (new SetItemQuantity)->handle($item->order, $item, 5);
    }

    #[Test]
    public function it_updates_totals_decrease_quantity_of_a_one_time_order(): void
    {
        $pickup = Pickup::factory()->create(['min_customer_orders' => '100.00']);
        $order = Order::factory()->create(['pickup_id' => $pickup->id, 'confirmed' => true, 'subtotal' => 12000]);
        /** @var OrderItem $item */

        /** @var Product $product */
        $product = Product::factory()->create();
        $item = OrderItem::factory()->create([
            'order_id' => $order->id,
            'product_id' => $product->id,
            'qty' => 6,
            'fulfilled_qty' => 8,
            'unit_price' => 2000,
            'subtotal' => 12000,
            'unit_of_issue' => 'package'
        ]);

        (new SetItemQuantity)->handle($item->order, $item, 5);

        $this->assertDatabaseHas(OrderItem::class, [
            'id' => $item->id,
            'subtotal' => 10000,
            'qty' => 5,
            'fulfilled_qty' => 5,
        ]);
    }

    #[Test]
    public function it_updates_totals_decrease_quantity_of_a_subscription_order(): void
    {
        $subscription = RecurringOrder::factory()->create();
        $pickup = Pickup::factory()->create(['min_customer_orders' => '100.00']);
        $order = Order::factory()->create(['blueprint_id' => $subscription->id, 'pickup_id' => $pickup->id, 'confirmed' => true, 'subtotal' => 12000]);
        /** @var OrderItem $item */

        /** @var Product $product */
        $product = Product::factory()->create();
        $item = OrderItem::factory()->create([
            'order_id' => $order->id,
            'product_id' => $product->id,
            'qty' => 6,
            'fulfilled_qty' => 6,
            'unit_price' => 2000,
            'subtotal' => 12000,
            'unit_of_issue' => 'package'
        ]);

        (new SetItemQuantity)->handle($item->order, $item, 5);

        $this->assertDatabaseHas(OrderItem::class, [
            'id' => $item->id,
            'subtotal' => 10000,
            'qty' => 5,
            'fulfilled_qty' => 5,
        ]);
    }

    #[Test]
    public function it_throws_an_exception_when_attempting_to_remove_the_last_standard_item_in_a_subscription_order(): void
    {
        /** @var Order $order */
        $order = Order::factory()->create(['confirmed' => true, 'subtotal' => 100000]);

        /** @var OrderItem $item */
        $item = OrderItem::factory()->create(['order_id' => $order->id, 'type' => 'standard', 'qty' => 3]);

        $this->expectException(Exception::class);
        $this->expectExceptionMessage("The last item of a confirmed order cannot be removed.");

        (new SetItemQuantity)->handle($item->order, $item, 0);
    }

    #[Test]
    public function it_does_not_throw_an_exception_when_attempting_to_remove_the_last_addon_item_in_a_subscription_order(): void
    {
        $order = Order::factory()->create(['confirmed' => true, 'subtotal' => 100000]);

        OrderItem::factory()->create(['order_id' => $order->id, 'type' => 'standard', 'qty' => 1]);
        $item = OrderItem::factory()->create(['order_id' => $order->id, 'type' => 'addon', 'qty' => 1]);

        $this->actingAsAdmin();
        $this->expectNotToPerformAssertions(Exception::class);

        (new SetItemQuantity)->handle($item->order, $item, 0);
    }
}
