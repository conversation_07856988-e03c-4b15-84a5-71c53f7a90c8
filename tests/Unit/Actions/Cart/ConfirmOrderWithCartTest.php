<?php

namespace Tests\Unit\Actions\Cart;

use App\Actions\Cart\ConfirmOrderWithCart;
use App\Actions\CreateRecurringOrderBlueprint;
use App\Actions\Order\AddRecurringItem;
use App\Actions\Order\Confirm;
use App\Actions\Order\ConfirmRecurring;
use App\Cart\Cart;
use App\Events\User\UserSubscribedToSmsMarketing;
use App\Models\Card;
use App\Models\Coupon;
use App\Models\Date;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Payment;
use App\Models\Pickup;
use App\Models\Product;
use App\Models\RecurringOrder;
use App\Models\Schedule;
use App\Models\User;
use App\Services\SubscriptionSettingsService;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Event;
use Mockery\MockInterface;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class ConfirmOrderWithCartTest extends TenantTestCase
{
    #[Test]
    public function it_handles_customer_attributes_from_cart(): void
    {
        Carbon::setTestNow(now());

        Event::fake([UserSubscribedToSmsMarketing::class]);

        $customer = User::factory()->create([
            'first_name' => 'first',
            'last_name' => 'last',
            'phone' => '************',
            'email' => '<EMAIL>',
        ]);

        $order = Order::factory()->create([
            'customer_id' => $customer->id,
            'customer_first_name' => 'first',
            'customer_last_name' => 'last',
            'customer_phone' => '************',
            'customer_email' => '<EMAIL>',
        ]);

        $attributes = $this->cartAttributes();
        $cart = new Cart($attributes);

        $this->mock(Confirm::class, function (MockInterface $mock) use ($order, $attributes) {
            $mock->shouldReceive('handle')->once()
                ->with(\Mockery::on(fn (Order $arg) => $arg->id === $order->id), [])
                ->andReturn($order);
        });

        (new ConfirmOrderWithCart)->handle($order, $cart);

        $this->assertDatabaseHas(Order::class, [
            'id' => $order->id,
            'customer_first_name' => 'new first',
            'customer_last_name' => 'new last',
            'customer_phone' => '************',
            'customer_email' => '<EMAIL>',
        ]);

        $this->assertDatabaseHas(User::class, [
            'first_name' => 'first',
            'last_name' => 'last',
            'phone' => '************',
            'email' => '<EMAIL>',
            'subscribed_to_sms_marketing_at' => now()
        ]);

        Event::assertDispatched(
            UserSubscribedToSmsMarketing::class,
            function (UserSubscribedToSmsMarketing $event) use ($customer) {
                return $event->user->id === $customer->id
                    && $event->opt_in_location = 'during_checkout';
            }
        );

        Carbon::setTestNow();
    }

    private function cartAttributes(): array
    {
        $payment = Payment::factory()->create();
        $date = Date::factory()->create(['schedule_id' => Schedule::factory()]);

        return [
            'purchase_type' => 'one_time_purchase',
            'date_id' => $date->id,
            'notes' => 'some new notes',
            'subscription' => [
                'frequency' => null,
                'product_incentive_id' => null
            ],
            'customer' => [
                'first_name' => 'new first',
                'last_name' => 'new last',
                'phone' => '************',
                'email' => '<EMAIL>',
                'save_for_later' => false,
                'opt_in_to_sms' => true
            ],
            'shipping' => [
                'street' => '123 new st',
                'street_2' => 'apt new',
                'city' => 'new',
                'state' => 'NE',
                'zip' => '45678',
                'country' => 'US',
                'save_for_later' => false
            ],
            'billing' => [
                'method' => $payment->key,
                'source_id' => null,
                'save_for_later' => false
            ],
            'discounts' =>  [
                'coupons' =>  collect(),
                'gift_card' =>  [
                    'name' =>  '',
                    'code' =>  '',
                    'amount' =>  0,
                ],
                'store_credit' =>  [
                    'amount' =>  0,
                ]
            ],
        ];
    }

    #[Test]
    public function it_handles_saving_customer_attributes_for_later(): void
    {
        Carbon::setTestNow(now());

        Event::fake([UserSubscribedToSmsMarketing::class]);

        $customer = User::factory()->create([
            'first_name' => 'first',
            'last_name' => 'last',
            'phone' => '************',
            'email' => '<EMAIL>',
        ]);

        $order = Order::factory()->create([
            'customer_id' => $customer->id,
            'customer_first_name' => 'first',
            'customer_last_name' => 'last',
            'customer_phone' => '************',
            'customer_email' => '<EMAIL>',
        ]);

        $attributes = $this->cartAttributes();
        $attributes['customer']['save_for_later'] = true;

        $cart = new Cart($attributes);

        $this->mock(Confirm::class, function (MockInterface $mock) use ($order, $attributes) {
            $mock->shouldReceive('handle')->once()
                ->with(\Mockery::on(fn (Order $arg) => $arg->id === $order->id), [])
                ->andReturn($order);
        });

        (new ConfirmOrderWithCart)->handle($order, $cart);

        $this->assertDatabaseHas(Order::class, [
            'id' => $order->id,
            'customer_first_name' => 'new first',
            'customer_last_name' => 'new last',
            'customer_phone' => '************',
            'customer_email' => '<EMAIL>',
        ]);

        $this->assertDatabaseHas(User::class, [
            'first_name' => 'new first',
            'last_name' => 'new last',
            'phone' => '************',
            'email' => '<EMAIL>',
            'subscribed_to_sms_marketing_at' => now()
        ]);

        Event::assertDispatched(
            UserSubscribedToSmsMarketing::class,
            function (UserSubscribedToSmsMarketing $event) use ($customer) {
                return $event->user->id === $customer->id
                    && $event->opt_in_location = 'during_checkout';
            }
        );

        Carbon::setTestNow();
    }

    #[Test]
    public function it_doesnt_fire_user_subscribed_event_when_not_subscribing(): void
    {
        Event::fake([UserSubscribedToSmsMarketing::class]);

        $customer = User::factory()->create(['subscribed_to_sms_marketing_at' => null]);
        $order = Order::factory()->create(['customer_id' => $customer->id]);

        $attributes = $this->cartAttributes();
        $attributes['customer']['opt_in_to_sms'] = false;

        $this->mock(Confirm::class, function (MockInterface $mock) use ($order, $attributes) {
            $mock->shouldReceive('handle')->once()
                ->with(\Mockery::on(fn (Order $arg) => $arg->id === $order->id), [])
                ->andReturn($order);
        });

        $cart = new Cart($attributes);

        (new ConfirmOrderWithCart)->handle($order, $cart);

        Event::assertNotDispatched(UserSubscribedToSmsMarketing::class);
    }

    #[Test]
    public function it_doesnt_fire_user_subscribed_event_when_already_subscribed(): void
    {
        Event::fake([UserSubscribedToSmsMarketing::class]);

        $customer = User::factory()->create(['subscribed_to_sms_marketing_at' => now()]);
        $order = Order::factory()->create(['customer_id' => $customer->id]);

        $attributes = $this->cartAttributes();
        $attributes['customer']['opt_in_to_sms'] = true;

        $this->mock(Confirm::class, function (MockInterface $mock) use ($order, $attributes) {
            $mock->shouldReceive('handle')->once()
                ->with(\Mockery::on(fn (Order $arg) => $arg->id === $order->id), [])
                ->andReturn($order);
        });

        $cart = new Cart($attributes);

        (new ConfirmOrderWithCart)->handle($order, $cart);

        Event::assertNotDispatched(UserSubscribedToSmsMarketing::class);
    }

    #[Test]
    public function it_handles_shipping_information_from_the_cart(): void
    {
        Event::fake([UserSubscribedToSmsMarketing::class]);

        $customer = User::factory()->create([
            'first_name' => 'first',
            'last_name' => 'last',
            'phone' => '************',
            'email' => '<EMAIL>',
            'street' => '123 old st',
            'street_2' => 'old apt',
            'city' => 'old',
            'state' => 'OL',
            'zip' => '12345',
            'country' => 'CA',
        ]);

        $order = Order::factory()->create([
            'customer_id' => $customer->id,
            'shipping_street' => '123 old st',
            'shipping_street_2' => 'old apt',
            'shipping_city' => 'old',
            'shipping_state' => 'OL',
            'shipping_zip' => '12345',
            'shipping_country' => 'CA',
        ]);

        $attributes = $this->cartAttributes();
        $cart = new Cart($attributes);

        $this->mock(Confirm::class, function (MockInterface $mock) use ($order, $attributes) {
            $mock->shouldReceive('handle')->once()
                ->with(\Mockery::on(fn (Order $arg) => $arg->id === $order->id), [])
                ->andReturn($order);
        });

        (new ConfirmOrderWithCart)->handle($order, $cart);

        $this->assertDatabaseHas(Order::class, [
            'id' => $order->id,
            'shipping_street' => '123 new st',
            'shipping_street_2' => 'apt new',
            'shipping_city' => 'new',
            'shipping_state' => 'NE',
            'shipping_zip' => '45678',
            'shipping_country' => 'US',
        ]);

        $this->assertDatabaseHas(User::class, [
            'street' => '123 old st',
            'street_2' => 'old apt',
            'city' => 'old',
            'state' => 'OL',
            'zip' => '12345',
            'country' => 'CA',
        ]);
    }

    #[Test]
    public function it_handles_saving_shipping_information_for_later(): void
    {
        Event::fake([UserSubscribedToSmsMarketing::class]);

        $customer = User::factory()->create([
            'first_name' => 'first',
            'last_name' => 'last',
            'phone' => '************',
            'email' => '<EMAIL>',
            'street' => '123 old st',
            'street_2' => 'old apt',
            'city' => 'old',
            'state' => 'OL',
            'zip' => '12345',
            'country' => 'CA',
        ]);

        $order = Order::factory()->create([
            'customer_id' => $customer->id,
            'shipping_street' => '123 old st',
            'shipping_street_2' => 'old apt',
            'shipping_city' => 'old',
            'shipping_state' => 'OL',
            'shipping_zip' => '12345',
            'shipping_country' => 'CA',
        ]);

        $attributes = $this->cartAttributes();
        $attributes['shipping']['save_for_later'] = true;

        $cart = new Cart($attributes);

        $this->mock(Confirm::class, function (MockInterface $mock) use ($order, $attributes) {
            $mock->shouldReceive('handle')->once()
                ->with(\Mockery::on(fn (Order $arg) => $arg->id === $order->id), [])
                ->andReturn($order);
        });

        (new ConfirmOrderWithCart)->handle($order, $cart);

        $this->assertDatabaseHas(Order::class, [
            'id' => $order->id,
            'shipping_street' => '123 new st',
            'shipping_street_2' => 'apt new',
            'shipping_city' => 'new',
            'shipping_state' => 'NE',
            'shipping_zip' => '45678',
            'shipping_country' => 'US',
        ]);

        $this->assertDatabaseHas(User::class, [
            'street' => '123 new st',
            'street_2' => 'apt new',
            'city' => 'new',
            'state' => 'NE',
            'zip' => '45678',
            'country' => 'US',
        ]);
    }

    #[Test]
    public function it_handles_billing_information_from_the_cart(): void
    {
        Event::fake([UserSubscribedToSmsMarketing::class]);

        $customer = User::factory()->create([
            'checkout_card_id' => 0,
        ]);

        $order = Order::factory()->create([
            'customer_id' => $customer->id,
            'payment_id' => 0,
            'payment_source_id' => null,
        ]);

        $attributes = $this->cartAttributes();

        $cart = new Cart($attributes);

        $this->mock(Confirm::class, function (MockInterface $mock) use ($order, $attributes) {
            $mock->shouldReceive('handle')->once()
                ->with(\Mockery::on(fn (Order $arg) => $arg->id === $order->id), [])
                ->andReturn($order);
        });

        (new ConfirmOrderWithCart)->handle($order, $cart);

        $expected_payment = Payment::where('key', $attributes['billing']['method'])->first();

        $this->assertDatabaseHas(Order::class, [
            'id' => $order->id,
            'payment_id' => $expected_payment->id,
            'payment_source_id' => null,
        ]);

        $this->assertDatabaseHas(User::class, [
            'checkout_card_id' => 0,
        ]);
    }

    #[Test]
    public function it_handles_saving_billing_information_for_later(): void
    {
        Event::fake([UserSubscribedToSmsMarketing::class]);

        $customer = User::factory()->create([
            'checkout_card_id' => 0,
            'settings' => []
        ]);
        $card = Card::factory()->create(['user_id' => $customer->id]);

        $order = Order::factory()->create([
            'customer_id' => $customer->id,
            'payment_id' => 0,
            'payment_source_id' => null,
        ]);

        $expected_payment = Payment::where(['key' => 'card'])->firstOrNew();
        $expected_payment->fillable(['key']);
        $expected_payment->fill(Payment::factory()->make(['key' => 'card'])->toArray());
        $expected_payment->save();

        $attributes = $this->cartAttributes();
        $attributes['billing']['save_for_later'] = true;
        $attributes['billing']['method'] = 'card';
        $attributes['billing']['source_id'] = $card->source_id;

        $cart = new Cart($attributes);

        $this->mock(Confirm::class, function (MockInterface $mock) use ($order, $attributes) {
            $mock->shouldReceive('handle')->once()
                ->with(\Mockery::on(fn (Order $arg) => $arg->id === $order->id), [])
                ->andReturn($order);
        });

        (new ConfirmOrderWithCart)->handle($order, $cart);

        $this->assertDatabaseHas(Order::class, [
            'id' => $order->id,
            'payment_id' => $expected_payment->id,
            'payment_source_id' => $card->id,
        ]);

        $this->assertDatabaseHas(User::class, [
            'checkout_card_id' => $card->id,
            'settings' => json_encode(['default_payment_method' => $expected_payment->id])
        ]);
    }

    #[Test]
    public function it_handles_applying_an_order_discount(): void
    {
        $customer = User::factory()->create();
        $coupon_one = Coupon::factory()->create(['discount_type' => 'fixed', 'discount_amount' => 1000, 'total_uses' => 2]);
        $coupon_two = Coupon::factory()->create(['discount_type' => 'fixed', 'discount_amount' => 500, 'total_uses' => 3]);

        $order = Order::factory()->create([
            'customer_id' => $customer->id,
            'coupon_subtotal' => 0,
        ]);

        $attributes = $this->cartAttributes();
        $coupons = collect()->push(
            ['name' => $coupon_one->description, 'code' => $coupon_one->code, 'amount' => 2500],
            ['name' => $coupon_two->description, 'code' => $coupon_two->code, 'amount' => 2000],
        );
        $attributes['discounts']['coupons'] = $coupons;

        $cart = new Cart($attributes);

        $this->mock(Confirm::class, function (MockInterface $mock) use ($order, $attributes) {
            $mock->shouldReceive('handle')->once()
                ->with(\Mockery::on(fn (Order $arg) => $arg->id === $order->id), [])
                ->andReturn($order);
        });

        $this->actingAsAdmin();

        (new ConfirmOrderWithCart)->handle($order, $cart);

        $this->assertDatabaseHas('coupon_order', [
            'coupon_id' => $coupon_one->id,
            'order_id' => $order->id,
            'savings' => 1000,
        ]);
        $this->assertDatabaseHas('coupon_order', [
            'coupon_id' => $coupon_two->id,
            'order_id' => $order->id,
            'savings' => 500,
        ]);

        $this->assertDatabaseHas(Order::class, [
            'id' => $order->id,
            'coupon_subtotal' => 1500,
        ]);

        $this->assertDatabaseHas(Coupon::class, [
            'id' => $coupon_one->id,
            'total_uses' => 3,
        ]);

         $this->assertDatabaseHas(Coupon::class, [
             'id' => $coupon_two->id,
             'total_uses' => 4,
         ]);
    }

    #[Test]
    public function it_does_not_apply_an_invalid_discount(): void
    {
        $customer = User::factory()->create();
        $coupon = Coupon::factory()->create(['discount_type' => 'fixed', 'discount_amount' => 1000]);

        $order = Order::factory()->create([
            'customer_id' => $customer->id,
            'coupon_subtotal' => 0,
        ]);

        $attributes = $this->cartAttributes();
        $coupons = collect()->push(
            ['name' => $coupon->description, 'code' => $coupon->code, 'amount' => 2500],
            ['name' => 'some', 'code' => 'someinvalidcodenotfound', 'amount' => 2000],
        );
        $attributes['discounts']['coupons'] = $coupons;

        $cart = new Cart($attributes);

        $this->mock(Confirm::class, function (MockInterface $mock) use ($order, $attributes) {
            $mock->shouldReceive('handle')->once()
                ->with(\Mockery::on(fn (Order $arg) => $arg->id === $order->id), [])
                ->andReturn($order);
        });

        $this->actingAsAdmin();

        (new ConfirmOrderWithCart)->handle($order, $cart);

        $this->assertDatabaseHas('coupon_order', [
            'coupon_id' => $coupon->id,
            'order_id' => $order->id,
            'savings' => 1000,
        ]);

        $this->assertDatabaseHas(Order::class, [
            'id' => $order->id,
            'coupon_subtotal' => 1000,
        ]);
    }

    #[Test]
    public function it_handles_saving_the_selected_date(): void
    {
        Event::fake([UserSubscribedToSmsMarketing::class]);

        $customer = User::factory()->create();

        $order = Order::factory()->create([
            'customer_id' => $customer->id,
            'date_id' => null,
            'pickup_date' => null,
            'deadline_date' => null,
            'original_pickup_date' => null,
            'schedule_id' => null
        ]);

        $attributes = $this->cartAttributes();

        $expected_date = Date::find($attributes['date_id']);

        $this->assertNotNull($expected_date);

        $cart = new Cart($attributes);

        $this->mock(Confirm::class, function (MockInterface $mock) use ($order, $attributes) {
            $mock->shouldReceive('handle')->once()
                ->with(\Mockery::on(fn (Order $arg) => $arg->id === $order->id), [])
                ->andReturn($order);
        });

        (new ConfirmOrderWithCart)->handle($order, $cart);

        $expected_order_window = $expected_date->toOrderWindows();

        $this->assertDatabaseHas(Order::class, [
            'id' => $order->id,
            'date_id' => $expected_order_window->dateId(),
            'deadline_date' => $expected_order_window->deadlineDatetime()->format('Y-m-d'),
            'pickup_date' => $expected_order_window->deliveryDatetime()->format('Y-m-d'),
            'original_pickup_date' => $expected_order_window->deliveryDatetime()->format('Y-m-d'),
            'schedule_id' => $expected_date->schedule_id
        ]);
    }

    #[Test]
    public function it_handles_saving_customer_notes(): void
    {
        Event::fake([UserSubscribedToSmsMarketing::class]);

        $customer = User::factory()->create();

        $order = Order::factory()->create([
            'customer_id' => $customer->id,
            'customer_notes' => '',
        ]);

        $attributes = $this->cartAttributes();

        $cart = new Cart($attributes);

        $this->mock(Confirm::class, function (MockInterface $mock) use ($order, $attributes) {
            $mock->shouldReceive('handle')->once()
                ->with(\Mockery::on(fn (Order $arg) => $arg->id === $order->id), [])
                ->andReturn($order);
        });

        (new ConfirmOrderWithCart)->handle($order, $cart);

        $this->assertDatabaseHas(Order::class, [
            'id' => $order->id,
            'customer_notes' => 'some new notes',
        ]);
    }

    #[Test]
    public function it_handles_subscription_attributes(): void
    {
        $schedule = Schedule::factory()->create(['type_id' => Schedule::TYPE_REPEATING, 'reorder_frequency' => [7,14]]);
        /** @var Date $date */
        $date = Date::factory()->create(['schedule_id' => $schedule->id, 'active' => true]);

        $location = Pickup::factory()->create(['status_id' => 1, 'schedule_id' => $schedule->id]);

        $order = Order::factory()->create(['pickup_id' => $location->id, 'is_recurring' => true, 'blueprint_id' => null]);

        $product = Product::factory()->create();
        OrderItem::factory()->create(['order_id' => $order->id, 'product_id' => $product->id]);

        $expected_product_incentive = Product::factory()->create();

        $existing_promo_item = OrderItem::factory()->create([
            'order_id' => $order->id,
            'type' => 'promo',
            'product_id' => $expected_product_incentive->id,
        ]);

        $attributes = $this->cartAttributes();
        $attributes['purchase_type'] = 'recurring';
        $attributes['subscription']['frequency'] = 7;
        $attributes['subscription']['product_incentive_id'] = $expected_product_incentive->id;

        $cart = new Cart($attributes);

        $this->mock(SubscriptionSettingsService::class, function (MockInterface $mock) use ($expected_product_incentive) {
            $mock->shouldReceive('discountIncentive')->andReturn(5);
            $mock->shouldReceive('defaultProductIncentiveId')->andReturn($expected_product_incentive->id);
            $mock->shouldReceive('excludedProductIds')->andReturn(collect());
        });

        $expected_blueprint = RecurringOrder::factory()->create();

        $this->mock(CreateRecurringOrderBlueprint::class, function (MockInterface $mock) use ($order, $expected_blueprint, $expected_product_incentive) {
            $mock->shouldReceive('execute')
                ->once()
                ->with(
                    \Mockery::on(fn (Order $arg) => $arg->id === $order->id),
                    7,
                    $expected_product_incentive->id
                )
                ->andReturn($expected_blueprint);
        });

        $this->mock(ConfirmRecurring::class, function (MockInterface $mock) use ($order, $attributes) {
            $mock->shouldReceive('handle')->once()
                ->with(\Mockery::on(fn (Order $arg) => $arg->id === $order->id), [])
                ->andReturn($order);
        });

        (new ConfirmOrderWithCart)->handle($order, $cart);

        $this->assertDatabaseHas(Order::class, [
            'id' => $order->id,
            'is_recurring' => true,
            'blueprint_id' => $expected_blueprint->id,
        ]);

        $this->assertDatabaseMissing(OrderItem::class, [
            'id' => $existing_promo_item->id,
        ]);
    }

    #[Test]
    public function it_handles_subscription_attributes_without_a_promo_product(): void
    {
        $schedule = Schedule::factory()->create(['type_id' => Schedule::TYPE_REPEATING, 'reorder_frequency' => [7,14]]);
        /** @var Date $date */
        $date = Date::factory()->create(['schedule_id' => $schedule->id, 'active' => true]);

        $location = Pickup::factory()->create(['status_id' => 1, 'schedule_id' => $schedule->id]);

        $order = Order::factory()->create(['pickup_id' => $location->id, 'is_recurring' => true, 'blueprint_id' => null]);

        $product = Product::factory()->create();
        OrderItem::factory()->create(['order_id' => $order->id, 'product_id' => $product->id]);

        $attributes = $this->cartAttributes();
        $attributes['purchase_type'] = 'recurring';
        $attributes['subscription']['frequency'] = 7;
        $attributes['subscription']['product_incentive_id'] = null;

        $cart = new Cart($attributes);

        $this->mock(SubscriptionSettingsService::class, function (MockInterface $mock) {
            $mock->shouldReceive('discountIncentive')->andReturn(5);
            $mock->shouldReceive('defaultProductIncentiveId')->andReturnNull();
            $mock->shouldReceive('excludedProductIds')->andReturn(collect());

        });

        $expected_blueprint = RecurringOrder::factory()->create();

        $this->mock(CreateRecurringOrderBlueprint::class, function (MockInterface $mock) use ($order, $expected_blueprint) {
            $mock->shouldReceive('execute')
                ->once()
                ->with(
                    \Mockery::on(fn (Order $arg) => $arg->id === $order->id),
                    7,
                    null
                )
                ->andReturn($expected_blueprint);
        });

        $this->mock(AddRecurringItem::class, function (MockInterface $mock) use ($order) {
            $mock->shouldNotReceive('handle');
        });

        $this->mock(ConfirmRecurring::class, function (MockInterface $mock) use ($order, $attributes) {
            $mock->shouldReceive('handle')->once()
                ->with(\Mockery::on(fn (Order $arg) => $arg->id === $order->id), [])
                ->andReturn($order);
        });

        (new ConfirmOrderWithCart)->handle($order, $cart);

        $this->assertDatabaseHas(Order::class, [
            'id' => $order->id,
            'is_recurring' => true,
            'blueprint_id' => $expected_blueprint->id,
        ]);

        $this->assertDatabaseMissing(OrderItem::class, [
            'id' => $order->id,
            'type' => 'promo',
        ]);
    }

    #[Test]
    public function it_handles_subscription_attributes_when_purchase_type_is_one_time_purchase(): void
    {
        $order = Order::factory()->create([
            'is_recurring' => false,
            'blueprint_id' => RecurringOrder::factory(),
        ]);

        $expected_product_incentive = Product::factory()->create();
        $attributes = $this->cartAttributes();
        $attributes['purchase_type'] = 'one_time_purchase';
        $attributes['subscription']['frequency'] = 14;
        $attributes['subscription']['product_incentive_id'] = $expected_product_incentive->id;

        $cart = new Cart($attributes);

        $this->mock(CreateRecurringOrderBlueprint::class, function (MockInterface $mock) use ($order) {
            $mock->shouldNotReceive('execute');
        });

        $this->mock(AddRecurringItem::class, function (MockInterface $mock) {
            $mock->shouldNotReceive('handle');
        });

        $this->mock(Confirm::class, function (MockInterface $mock) use ($order, $attributes) {
            $mock->shouldReceive('handle')->once()
                ->with(\Mockery::on(fn (Order $arg) => $arg->id === $order->id), [])
                ->andReturn($order);
        });

        (new ConfirmOrderWithCart)->handle($order, $cart);

        $this->assertDatabaseHas(Order::class, [
            'id' => $order->id,
            'is_recurring' => null,
            'blueprint_id' => null,
        ]);
    }
}
