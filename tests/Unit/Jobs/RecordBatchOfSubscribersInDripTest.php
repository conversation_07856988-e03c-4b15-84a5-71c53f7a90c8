<?php

namespace Tests\Unit\Jobs;

use App\Integrations\Drip\Drip;
use App\Jobs\RecordBatchOfSubscribersInDrip;
use App\Models\Integration;
use Mo<PERSON>y\MockInterface;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class RecordBatchOfSubscribersInDripTest extends TenantTestCase
{
    #[Test]
    public function it_makes_the_expected_batch_subscriber_updates_request(): void
    {
        Integration::factory()->create([
            'name' => 'drip',
            'enabled' => true,
            'settings' => [
                'api_key' => 'abc123',
                'account_id' => 'def456'
            ]
        ]);

        $subscribers = [
            ['email' => '<EMAIL>', 'tags' => ['some' => 'props1']],
            ['email' => '<EMAIL>', 'tags' => ['some' => 'props2']]
        ];

        $this->mock(Drip::class, function (MockInterface $mock) use ($subscribers) {
            $mock->shouldReceive('configure')->once()->andReturnSelf();
            $mock->shouldReceive('recordBatchOfSubscriberUpdates')->once()->with($subscribers);
        });

        (new RecordBatchOfSubscribersInDrip($subscribers))->handle();
    }

    #[Test]
    public function it_doesnt_make_request_if_subscribers_is_empty(): void
    {
        Integration::factory()->create([
            'name' => 'drip',
            'enabled' => true,
            'settings' => [
                'api_key' => 'abc123',
                'account_id' => 'def456'
            ]
        ]);

        $subscribers = [];

        $this->mock(Drip::class, function (MockInterface $mock) use ($subscribers) {
            $mock->shouldNotReceive('configure');
            $mock->shouldNotReceive('recordBatchOfSubscriberUpdates');
        });

        (new RecordBatchOfSubscribersInDrip($subscribers))->handle();
    }

    #[Test]
    public function it_doesnt_make_request_if_drip_integration_is_not_enabled(): void
    {
        Integration::factory()->create([
            'name' => 'drip',
            'enabled' => false,
            'settings' => [
                'api_key' => 'abc123',
                'account_id' => 'def456'
            ]
        ]);

        $subscribers = [
            ['email' => '<EMAIL>', 'tags' => ['some' => 'props1']],
            ['email' => '<EMAIL>', 'tags' => ['some' => 'props2']]
        ];

        $this->mock(Drip::class, function (MockInterface $mock) use ($subscribers) {
            $mock->shouldNotReceive('configure');
            $mock->shouldNotReceive('recordBatchOfSubscriberUpdates');
        });

        (new RecordBatchOfSubscribersInDrip($subscribers))->handle();
    }
}
