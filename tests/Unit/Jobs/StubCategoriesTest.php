<?php

namespace Tests\Unit\Jobs;

use App\Jobs\StubCategories;
use App\Models\Category;
use App\Models\Product;
use App\Models\Subcollection;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class StubCategoriesTest extends TenantTestCase
{
    #[Test]
    public function it_stubs_categories_from_subcollections(): void
    {
        Subcollection::factory()->create([
            'title' => ' Beef | Roasts ',
        ]);

        Subcollection::factory()->create([
            'title' => ' Chicken | Roasts ',
        ]);

        (new StubCategories())->handle();

        $this->assertDatabaseHas(Category::class, [
            'category_id' => null,
            'name' => 'Beef',
        ]);

        $this->assertDatabaseHas(Category::class, [
            'category_id' => null,
            'name' => 'Chicken',
        ]);

        $this->assertDatabaseHas(Category::class, [
            'category_id' => Category::where('name', 'Beef')->whereNull('category_id')->first()->id,
            'name' => 'Roasts',
        ]);

        $this->assertDatabaseHas(Category::class, [
            'category_id' => Category::where('name', 'Chicken')->whereNull('category_id')->first()->id,
            'name' => 'Roasts',
        ]);
    }

    #[Test]
    public function it_does_not_stub_categories_that_already_exist_from_subcollections(): void
    {
        $expected_category_one = Category::factory()->create(['name' => 'Beef',]);

        Subcollection::factory()->create([
            'title' => ' Beef | Roasts ',
        ]);

        $expected_category_two = Category::factory()->create(['name' => 'Chicken',]);
        Category::factory()->create(['category_id' => $expected_category_two->id, 'name' => 'Roasts',]);

        Subcollection::factory()->create([
            'title' => ' Chicken | Roasts ',
        ]);

        (new StubCategories())->handle();

        $this->assertCount(1, Category::where([
            'category_id' => null,
            'name' => 'Beef',
        ])->get());


        $this->assertCount(1, Category::where([
            'category_id' => null,
            'name' => 'Chicken',
        ])->get());

        $this->assertCount(1, Category::where([
            'category_id' => $expected_category_two->id,
            'name' => 'Roasts',
        ])->get());


        $this->assertDatabaseHas(Category::class, [
            'category_id' => $expected_category_one->id,
            'name' => 'Roasts',
        ]);
    }

    #[Test]
    public function it_associates_tagged_and_uncategorized_products_to_subcategory(): void
    {
        $product_one = Product::factory()->create(['category_id' => null]);
        $product_two = Product::factory()->create(['category_id' => null]);
        $product_three = Product::factory()->create(['category_id' => Category::factory()]);

        $subcollection_one = Subcollection::factory()->create([
            'title' => ' Beef | Roasts ',
        ]);

        $product_one->tags()->attach($subcollection_one->tag_id);
        $product_two->tags()->attach($subcollection_one->tag_id);
        $product_three->tags()->attach($subcollection_one->tag_id);

        (new StubCategories())->handle();

        $category = Category::where('name', 'Beef')
            ->whereNull('category_id')
            ->first();

        $subcategory = Category::where('name', 'Roasts')
            ->where('category_id', $category->id)
            ->first();

        $this->assertDatabaseHas(Product::class, [
            'id' => $product_one->id,
            'category_id' => $subcategory->id,
        ]);

        $this->assertDatabaseHas(Product::class, [
            'id' => $product_two->id,
            'category_id' => $subcategory->id,
        ]);

        $this->assertDatabaseMissing(Product::class, [
            'id' => $product_three->id,
            'category_id' => $subcategory->id,
        ]);
    }

    #[Test]
    public function it_stubs_categories_from_accounting_classes(): void
    {
        $product_one = Product::factory()->create(['accounting_class' => 'Beef', 'category_id' => null]);
        $product_two = Product::factory()->create(['accounting_class' => 'Beef', 'category_id' => null]);
        $product_three = Product::factory()->create(['accounting_class' => 'Chicken', 'category_id' => null]);

        (new StubCategories())->handle();

        $this->assertDatabaseHas(Category::class, [
            'category_id' => null,
            'name' => 'Beef',
        ]);

        $this->assertDatabaseHas(Category::class, [
            'category_id' => null,
            'name' => 'Chicken',
        ]);

        $this->assertDatabaseHas(Product::class, [
            'id' => $product_one->id,
            'category_id' => Category::where('name', 'Beef')->whereNull('category_id')->first()->id,
        ]);

        $this->assertDatabaseHas(Product::class, [
            'id' => $product_two->id,
            'category_id' => Category::where('name', 'Beef')->whereNull('category_id')->first()->id,
        ]);

        $this->assertDatabaseHas(Product::class, [
            'id' => $product_three->id,
            'category_id' => Category::where('name', 'Chicken')->whereNull('category_id')->first()->id,
        ]);
    }

    #[Test]
    public function it_does_not_stub_categories_that_already_exist_from_accounting_classes(): void
    {
        $expected_category_one = Category::factory()->create(['name' => 'Beef',]);

        $product_one = Product::factory()->create(['accounting_class' => 'Beef', 'category_id' => null]);
        $product_two = Product::factory()->create(['accounting_class' => 'Beef', 'category_id' => null]);

        $expected_category_two = Category::factory()->create(['name' => 'Chicken',]);

        $product_three = Product::factory()->create(['accounting_class' => 'Chicken', 'category_id' => null]);

        (new StubCategories())->handle();

        $this->assertDatabaseHas(Product::class, [
            'id' => $product_one->id,
            'category_id' => $expected_category_one->id,
        ]);

        $this->assertDatabaseHas(Product::class, [
            'id' => $product_two->id,
            'category_id' => $expected_category_one->id,
        ]);

        $this->assertDatabaseHas(Product::class, [
            'id' => $product_three->id,
            'category_id' => $expected_category_two->id,
        ]);
    }

    #[Test]
    public function it_doesnt_override_subcollection_category_products_with_their_accounting_class_category(): void
    {
        $product_one = Product::factory()->create(['accounting_class' => 'Chicken', 'category_id' => null]);

        $subcollection_one = Subcollection::factory()->create([
            'title' => ' Beef | Roasts ',
        ]);

        $product_one->tags()->attach($subcollection_one->tag_id);

        (new StubCategories())->handle();

        $this->assertDatabaseHas(Product::class, [
            'id' => $product_one->id,
            'category_id' => Category::query()->where('name', 'Roasts')->first()->id,
        ]);
    }
}
