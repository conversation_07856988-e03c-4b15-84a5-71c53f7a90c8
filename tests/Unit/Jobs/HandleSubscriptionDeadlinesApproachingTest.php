<?php

namespace Tests\Unit\Jobs;

use App\Jobs\FireSubscriptionDeadlineApproachingEvent;
use App\Jobs\HandleSubscriptionDeadlinesApproaching;
use App\Models\Order;
use App\Models\Pickup;
use App\Models\RecurringOrder;
use App\Models\Schedule;
use App\Models\Setting;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Bus;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class HandleSubscriptionDeadlinesApproachingTest extends TenantTestCase
{
    #[Test]
    public function it_queues_expected_subscription_jobs_when_using_global_notification_settings(): void
    {
        $now = today()->setTime(7, 00, 00);

        Carbon::setTestNow($now);

        Bus::fake([FireSubscriptionDeadlineApproachingEvent::class]);

        Setting::updateOrCreate(['key' => 'timezone'], ['value' => 'America/Chicago']);
        Setting::updateOrCreate(['key' => 'order_deadline_hour'], ['value' => 8]);
        Setting::updateOrCreate(['key' => 'recurring_orders_deadline_email_enabled'], ['value' => 1]);
        Setting::updateOrCreate(['key' => 'recurring_orders_deadline_sms_enabled'], ['value' => 1]);
        Setting::updateOrCreate(['key' => 'recurring_orders_deadline_days_before'], ['value' => 1]);
        Setting::updateOrCreate(['key' => 'recurring_orders_deadline_hours_before'], ['value' => 1]);

        $schedule = Schedule::factory()->create(['subscription_reminder_enabled' => null, 'subscription_reminder_days' => 2, 'subscription_reminder_hour' => 2]);
        $pickup = Pickup::factory()->create(['schedule_id' => $schedule->id]);

        $recurring_order_one = RecurringOrder::factory()->create(['paused_at' => null, 'deleted_at' => null, 'reorder_frequency' => 7, 'fulfillment_id' => $pickup->id]);
        $order_one = Order::factory()->create([
            'blueprint_id' => $recurring_order_one->id,
            'deadline_date' => today()->addDays(1),
            'pickup_date' => today()->addDays(3),
            'confirmed' => false
        ]);

        $recurring_order_two = RecurringOrder::factory()->create(['paused_at' => null, 'deleted_at' => null, 'reorder_frequency' => 14, 'fulfillment_id' => $pickup->id]);
        $order_two = Order::factory()->create([
            'blueprint_id' => $recurring_order_two->id,
            'deadline_date' => today()->addDays(1),
            'pickup_date' => today()->addDays(2),
            'confirmed' => false
        ]);

        (new HandleSubscriptionDeadlinesApproaching)->handle();

        Bus::assertDispatchedTimes(FireSubscriptionDeadlineApproachingEvent::class, 2);

        Bus::assertDispatched(
            FireSubscriptionDeadlineApproachingEvent::class,
            fn(FireSubscriptionDeadlineApproachingEvent $job) => $job->order_id === $order_one->id && $job->schedule_id === $schedule->id
        );

        Bus::assertDispatched(
            FireSubscriptionDeadlineApproachingEvent::class,
            fn(FireSubscriptionDeadlineApproachingEvent $job) => $job->order_id === $order_two->id && $job->schedule_id === $schedule->id
        );

        Carbon::setTestNow();
    }

    #[Test]
    public function it_does_not_queue_subscription_jobs_for_one_time_orders(): void
    {
        $now = today()->setTime(7, 00, 00);

        Carbon::setTestNow($now);

        Bus::fake([FireSubscriptionDeadlineApproachingEvent::class]);

        Setting::updateOrCreate(['key' => 'timezone'], ['value' => 'America/Chicago']);
        Setting::updateOrCreate(['key' => 'order_deadline_hour'], ['value' => 8]);
        Setting::updateOrCreate(['key' => 'recurring_orders_deadline_email_enabled'], ['value' => 1]);
        Setting::updateOrCreate(['key' => 'recurring_orders_deadline_sms_enabled'], ['value' => 1]);
        Setting::updateOrCreate(['key' => 'recurring_orders_deadline_days_before'], ['value' => 1]);
        Setting::updateOrCreate(['key' => 'recurring_orders_deadline_hours_before'], ['value' => 1]);

        $schedule = Schedule::factory()->create(['subscription_reminder_enabled' => null, 'subscription_reminder_days' => 2, 'subscription_reminder_hour' => 2]);
        $pickup = Pickup::factory()->create(['schedule_id' => $schedule->id]);

        $recurring_order_one = RecurringOrder::factory()->create(['paused_at' => null, 'deleted_at' => null, 'reorder_frequency' => 7, 'fulfillment_id' => $pickup->id]);
        $order_one = Order::factory()->create([
            'blueprint_id' => null,
            'deadline_date' => today()->addDays(1),
            'pickup_date' => today()->addDays(3),
            'confirmed' => false
        ]);

        $recurring_order_two = RecurringOrder::factory()->create(['paused_at' => null, 'deleted_at' => null, 'reorder_frequency' => 14, 'fulfillment_id' => $pickup->id]);
        $order_two = Order::factory()->create([
            'blueprint_id' => null,
            'deadline_date' => today()->addDays(1),
            'pickup_date' => today()->addDays(2),
            'confirmed' => false
        ]);

        (new HandleSubscriptionDeadlinesApproaching)->handle();

        Bus::assertNotDispatched(FireSubscriptionDeadlineApproachingEvent::class);

        Carbon::setTestNow();
    }

    #[Test]
    public function it_does_not_queue_subscription_jobs_where_deadline_approaching_date_has_not_been_reached_yet_when_using_global_notification_settings(): void
    {
        $now = today()->setTime(6, 59, 59);

        Carbon::setTestNow($now);

        Bus::fake([FireSubscriptionDeadlineApproachingEvent::class]);

        Setting::updateOrCreate(['key' => 'timezone'], ['value' => 'America/Chicago']);
        Setting::updateOrCreate(['key' => 'order_deadline_hour'], ['value' => 8]);
        Setting::updateOrCreate(['key' => 'recurring_orders_deadline_email_enabled'], ['value' => 1]);
        Setting::updateOrCreate(['key' => 'recurring_orders_deadline_sms_enabled'], ['value' => 1]);
        Setting::updateOrCreate(['key' => 'recurring_orders_deadline_days_before'], ['value' => 1]);
        Setting::updateOrCreate(['key' => 'recurring_orders_deadline_hours_before'], ['value' => 1]);

        $schedule = Schedule::factory()->create(['subscription_reminder_enabled' => null, 'subscription_reminder_days' => 2, 'subscription_reminder_hour' => 2]);
        $pickup = Pickup::factory()->create(['schedule_id' => $schedule->id]);

        $recurring_order = RecurringOrder::factory()->create(['paused_at' => null, 'deleted_at' => null, 'reorder_frequency' => 7, 'fulfillment_id' => $pickup->id]);
        $order = Order::factory()->create([
            'blueprint_id' => $recurring_order->id,
            'deadline_date' => today()->addDays(1),
            'pickup_date' => today()->addDays(3),
            'confirmed' => false
        ]);

        (new HandleSubscriptionDeadlinesApproaching)->handle();

        Bus::assertNotDispatched(FireSubscriptionDeadlineApproachingEvent::class);
    }

    #[Test]
    public function it_does_not_queue_subscription_jobs_where_deadline_approaching_date_has_already_passed_yet_when_using_global_notification_settings(): void
    {
        $now = today()->setTime(7, 15, 00);

        Carbon::setTestNow($now);

        Bus::fake([FireSubscriptionDeadlineApproachingEvent::class]);

        Setting::updateOrCreate(['key' => 'timezone'], ['value' => 'America/Chicago']);
        Setting::updateOrCreate(['key' => 'order_deadline_hour'], ['value' => 8]);
        Setting::updateOrCreate(['key' => 'recurring_orders_deadline_email_enabled'], ['value' => 1]);
        Setting::updateOrCreate(['key' => 'recurring_orders_deadline_sms_enabled'], ['value' => 1]);
        Setting::updateOrCreate(['key' => 'recurring_orders_deadline_days_before'], ['value' => 1]);
        Setting::updateOrCreate(['key' => 'recurring_orders_deadline_hours_before'], ['value' => 1]);

        $schedule = Schedule::factory()->create(['subscription_reminder_enabled' => null, 'subscription_reminder_days' => 2, 'subscription_reminder_hour' => 2]);
        $pickup = Pickup::factory()->create(['schedule_id' => $schedule->id]);

        $recurring_order = RecurringOrder::factory()->create(['paused_at' => null, 'deleted_at' => null, 'reorder_frequency' => 7, 'fulfillment_id' => $pickup->id]);
        $order = Order::factory()->create([
            'blueprint_id' => $recurring_order->id,
            'deadline_date' => today()->addDays(1),
            'pickup_date' => today()->addDays(3),
            'confirmed' => false
        ]);

        (new HandleSubscriptionDeadlinesApproaching)->handle();

        Bus::assertNotDispatched(FireSubscriptionDeadlineApproachingEvent::class);
    }


    #[Test]
    public function it_does_not_queue_subscription_jobs_for_canceled_subscriptions_when_using_global_notification_settings(): void
    {
        $now = today()->setTime(7, 00, 00);

        Carbon::setTestNow($now);

        Bus::fake([FireSubscriptionDeadlineApproachingEvent::class]);

        Setting::updateOrCreate(['key' => 'timezone'], ['value' => 'America/Chicago']);
        Setting::updateOrCreate(['key' => 'order_deadline_hour'], ['value' => 8]);
        Setting::updateOrCreate(['key' => 'recurring_orders_deadline_email_enabled'], ['value' => 1]);
        Setting::updateOrCreate(['key' => 'recurring_orders_deadline_sms_enabled'], ['value' => 1]);
        Setting::updateOrCreate(['key' => 'recurring_orders_deadline_days_before'], ['value' => 1]);
        Setting::updateOrCreate(['key' => 'recurring_orders_deadline_hours_before'], ['value' => 1]);

        $schedule = Schedule::factory()->create(['subscription_reminder_enabled' => null, 'subscription_reminder_days' => 2, 'subscription_reminder_hour' => 2]);
        $pickup = Pickup::factory()->create(['schedule_id' => $schedule->id]);

        $recurring_order_one = RecurringOrder::factory()->create(['paused_at' => null, 'deleted_at' => now(), 'reorder_frequency' => 7, 'fulfillment_id' => $pickup->id]);
        $order_one = Order::factory()->create([
            'blueprint_id' => $recurring_order_one->id,
            'deadline_date' => today()->addDays(2),
            'pickup_date' => today()->addDays(3),
            'confirmed' => false
        ]);

        (new HandleSubscriptionDeadlinesApproaching)->handle();

        Bus::assertNotDispatched(FireSubscriptionDeadlineApproachingEvent::class);

        Carbon::setTestNow();
    }

    #[Test]
    public function it_does_not_queue_subscription_jobs_for_paused_subscriptions_when_using_global_notification_settings(): void
    {
        $now = today()->setTime(7, 00, 00);

        Carbon::setTestNow($now);

        Bus::fake([FireSubscriptionDeadlineApproachingEvent::class]);

        Setting::updateOrCreate(['key' => 'timezone'], ['value' => 'America/Chicago']);
        Setting::updateOrCreate(['key' => 'order_deadline_hour'], ['value' => 8]);
        Setting::updateOrCreate(['key' => 'recurring_orders_deadline_email_enabled'], ['value' => 1]);
        Setting::updateOrCreate(['key' => 'recurring_orders_deadline_sms_enabled'], ['value' => 1]);
        Setting::updateOrCreate(['key' => 'recurring_orders_deadline_days_before'], ['value' => 1]);
        Setting::updateOrCreate(['key' => 'recurring_orders_deadline_hours_before'], ['value' => 1]);

        $schedule = Schedule::factory()->create(['subscription_reminder_enabled' => null, 'subscription_reminder_days' => 2, 'subscription_reminder_hour' => 2]);
        $pickup = Pickup::factory()->create(['schedule_id' => $schedule->id]);

        $recurring_order_one = RecurringOrder::factory()->create(['paused_at' => now(), 'deleted_at' => null, 'reorder_frequency' => 7, 'fulfillment_id' => $pickup->id]);
        $order_one = Order::factory()->create([
            'blueprint_id' => $recurring_order_one->id,
            'deadline_date' => today()->addDays(2),
            'pickup_date' => today()->addDays(3),
            'confirmed' => false
        ]);

        (new HandleSubscriptionDeadlinesApproaching)->handle();

        Bus::assertNotDispatched(FireSubscriptionDeadlineApproachingEvent::class);

        Carbon::setTestNow();
    }

    #[Test]
    public function it_queues_expected_subscription_jobs_when_using_schedule_specific_notification_settings(): void
    {
        $now = today()->setTime(6, 00, 00);

        Carbon::setTestNow($now);

        Bus::fake([FireSubscriptionDeadlineApproachingEvent::class]);

        Setting::updateOrCreate(['key' => 'timezone'], ['value' => 'America/Chicago']);
        Setting::updateOrCreate(['key' => 'order_deadline_hour'], ['value' => 8]);
        Setting::updateOrCreate(['key' => 'recurring_orders_deadline_email_enabled'], ['value' => 0]);
        Setting::updateOrCreate(['key' => 'recurring_orders_deadline_sms_enabled'], ['value' => 0]);
        Setting::updateOrCreate(['key' => 'recurring_orders_deadline_days_before'], ['value' => 1]);
        Setting::updateOrCreate(['key' => 'recurring_orders_deadline_hours_before'], ['value' => 1]);

        $schedule = Schedule::factory()->create(['subscription_reminder_enabled' => true, 'subscription_reminder_days' => 2, 'subscription_reminder_hour' => 2]);
        $pickup = Pickup::factory()->create(['schedule_id' => $schedule->id]);

        $recurring_order_one = RecurringOrder::factory()->create(['paused_at' => null, 'deleted_at' => null, 'reorder_frequency' => 7, 'fulfillment_id' => $pickup->id]);
        $order_one = Order::factory()->create([
            'blueprint_id' => $recurring_order_one->id,
            'deadline_date' => today()->addDays(2),
            'pickup_date' => today()->addDays(3),
            'confirmed' => false
        ]);

        $recurring_order_two = RecurringOrder::factory()->create(['paused_at' => null, 'deleted_at' => null, 'reorder_frequency' => 14, 'fulfillment_id' => $pickup->id]);
        $order_two = Order::factory()->create([
            'blueprint_id' => $recurring_order_two->id,
            'deadline_date' => today()->addDays(2),
            'pickup_date' => today()->addDays(2),
            'confirmed' => false
        ]);

        (new HandleSubscriptionDeadlinesApproaching)->handle();

        Bus::assertDispatchedTimes(FireSubscriptionDeadlineApproachingEvent::class, 2);

        Bus::assertDispatched(
            FireSubscriptionDeadlineApproachingEvent::class,
            fn(FireSubscriptionDeadlineApproachingEvent $job) => $job->order_id === $order_one->id && $job->schedule_id === $schedule->id
        );

        Bus::assertDispatched(
            FireSubscriptionDeadlineApproachingEvent::class,
            fn(FireSubscriptionDeadlineApproachingEvent $job) => $job->order_id === $order_two->id && $job->schedule_id === $schedule->id
        );

        Carbon::setTestNow();
    }

    #[Test]
    public function it_does_not_queue_subscription_jobs_where_deadline_approaching_date_has_not_been_reached_yet_when_using_schedule_specific_notification_settings(): void
    {
        $now = today()->setTime(5, 59, 59);

        Carbon::setTestNow($now);

        Bus::fake([FireSubscriptionDeadlineApproachingEvent::class]);

        Setting::updateOrCreate(['key' => 'timezone'], ['value' => 'America/Chicago']);
        Setting::updateOrCreate(['key' => 'order_deadline_hour'], ['value' => 8]);
        Setting::updateOrCreate(['key' => 'recurring_orders_deadline_email_enabled'], ['value' => 0]);
        Setting::updateOrCreate(['key' => 'recurring_orders_deadline_sms_enabled'], ['value' => 0]);
        Setting::updateOrCreate(['key' => 'recurring_orders_deadline_days_before'], ['value' => 1]);
        Setting::updateOrCreate(['key' => 'recurring_orders_deadline_hours_before'], ['value' => 1]);

        $schedule = Schedule::factory()->create(['subscription_reminder_enabled' => true, 'subscription_reminder_days' => 2, 'subscription_reminder_hour' => 2]);
        $pickup = Pickup::factory()->create(['schedule_id' => $schedule->id]);

        $recurring_order = RecurringOrder::factory()->create(['paused_at' => null, 'deleted_at' => null, 'reorder_frequency' => 7, 'fulfillment_id' => $pickup->id]);
        $order = Order::factory()->create([
            'blueprint_id' => $recurring_order->id,
            'deadline_date' => today()->addDays(2),
            'pickup_date' => today()->addDays(3),
            'confirmed' => false
        ]);

        (new HandleSubscriptionDeadlinesApproaching)->handle();

        Bus::assertNotDispatched(FireSubscriptionDeadlineApproachingEvent::class);
    }

    #[Test]
    public function it_does_not_queue_subscription_jobs_where_deadline_approaching_date_has_already_passed_yet_when_using_schedule_specific_notification_settings(): void
    {
        $now = today()->setTime(7, 15, 00);

        Carbon::setTestNow($now);

        Bus::fake([FireSubscriptionDeadlineApproachingEvent::class]);

        Setting::updateOrCreate(['key' => 'timezone'], ['value' => 'America/Chicago']);
        Setting::updateOrCreate(['key' => 'order_deadline_hour'], ['value' => 8]);
        Setting::updateOrCreate(['key' => 'recurring_orders_deadline_email_enabled'], ['value' => 0]);
        Setting::updateOrCreate(['key' => 'recurring_orders_deadline_sms_enabled'], ['value' => 0]);
        Setting::updateOrCreate(['key' => 'recurring_orders_deadline_days_before'], ['value' => 1]);
        Setting::updateOrCreate(['key' => 'recurring_orders_deadline_hours_before'], ['value' => 1]);

        $schedule = Schedule::factory()->create(['subscription_reminder_enabled' => true, 'subscription_reminder_days' => 2, 'subscription_reminder_hour' => 2]);
        $pickup = Pickup::factory()->create(['schedule_id' => $schedule->id]);

        $recurring_order = RecurringOrder::factory()->create(['paused_at' => null, 'deleted_at' => null, 'reorder_frequency' => 7, 'fulfillment_id' => $pickup->id]);
        $order = Order::factory()->create([
            'blueprint_id' => $recurring_order->id,
            'deadline_date' => today()->addDays(2),
            'pickup_date' => today()->addDays(3),
            'confirmed' => false
        ]);

        (new HandleSubscriptionDeadlinesApproaching)->handle();

        Bus::assertNotDispatched(FireSubscriptionDeadlineApproachingEvent::class);
    }


    #[Test]
    public function it_does_not_queue_subscription_jobs_for_canceled_subscriptions_when_using_schedule_specific_notification_settings(): void
    {
        $now = today()->setTime(7, 00, 00);

        Carbon::setTestNow($now);

        Bus::fake([FireSubscriptionDeadlineApproachingEvent::class]);

        Setting::updateOrCreate(['key' => 'timezone'], ['value' => 'America/Chicago']);
        Setting::updateOrCreate(['key' => 'order_deadline_hour'], ['value' => 8]);
        Setting::updateOrCreate(['key' => 'recurring_orders_deadline_email_enabled'], ['value' => 0]);
        Setting::updateOrCreate(['key' => 'recurring_orders_deadline_sms_enabled'], ['value' => 0]);
        Setting::updateOrCreate(['key' => 'recurring_orders_deadline_days_before'], ['value' => 1]);
        Setting::updateOrCreate(['key' => 'recurring_orders_deadline_hours_before'], ['value' => 1]);

        $schedule = Schedule::factory()->create(['subscription_reminder_enabled' => true, 'subscription_reminder_days' => 2, 'subscription_reminder_hour' => 2]);
        $pickup = Pickup::factory()->create(['schedule_id' => $schedule->id]);

        $recurring_order_one = RecurringOrder::factory()->create(['paused_at' => null, 'deleted_at' => now(), 'reorder_frequency' => 7, 'fulfillment_id' => $pickup->id]);
        $order_one = Order::factory()->create([
            'blueprint_id' => $recurring_order_one->id,
            'deadline_date' => today()->addDays(2),
            'pickup_date' => today()->addDays(3),
            'confirmed' => false
        ]);

        (new HandleSubscriptionDeadlinesApproaching)->handle();

        Bus::assertNotDispatched(FireSubscriptionDeadlineApproachingEvent::class);

        Carbon::setTestNow();
    }

    #[Test]
    public function it_does_not_queue_subscription_jobs_for_paused_subscriptions_when_using_schedule_specific_notification_settings(): void
    {
        $now = today()->setTime(7, 00, 00);

        Carbon::setTestNow($now);

        Bus::fake([FireSubscriptionDeadlineApproachingEvent::class]);

        Setting::updateOrCreate(['key' => 'timezone'], ['value' => 'America/Chicago']);
        Setting::updateOrCreate(['key' => 'order_deadline_hour'], ['value' => 8]);
        Setting::updateOrCreate(['key' => 'recurring_orders_deadline_email_enabled'], ['value' => 0]);
        Setting::updateOrCreate(['key' => 'recurring_orders_deadline_sms_enabled'], ['value' => 0]);
        Setting::updateOrCreate(['key' => 'recurring_orders_deadline_days_before'], ['value' => 1]);
        Setting::updateOrCreate(['key' => 'recurring_orders_deadline_hours_before'], ['value' => 1]);

        $schedule = Schedule::factory()->create(['subscription_reminder_enabled' => true, 'subscription_reminder_days' => 2, 'subscription_reminder_hour' => 2]);
        $pickup = Pickup::factory()->create(['schedule_id' => $schedule->id]);

        $recurring_order_one = RecurringOrder::factory()->create(['paused_at' => now(), 'deleted_at' => null, 'reorder_frequency' => 7, 'fulfillment_id' => $pickup->id]);
        $order_one = Order::factory()->create([
            'blueprint_id' => $recurring_order_one->id,
            'deadline_date' => today()->addDays(2),
            'pickup_date' => today()->addDays(3),
            'confirmed' => false
        ]);

        (new HandleSubscriptionDeadlinesApproaching)->handle();

        Bus::assertNotDispatched(FireSubscriptionDeadlineApproachingEvent::class);

        Carbon::setTestNow();
    }

    #[Test]
    public function it_queues_expected_subscription_jobs_when_overriding_global_settings_with_schedule_specific_notification_settings(): void
    {
        $now = today()->setTime(6, 00, 00);

        Carbon::setTestNow($now);

        Bus::fake([FireSubscriptionDeadlineApproachingEvent::class]);

        Setting::updateOrCreate(['key' => 'timezone'], ['value' => 'America/Chicago']);
        Setting::updateOrCreate(['key' => 'order_deadline_hour'], ['value' => 8]);
        Setting::updateOrCreate(['key' => 'recurring_orders_deadline_email_enabled'], ['value' => 1]);
        Setting::updateOrCreate(['key' => 'recurring_orders_deadline_sms_enabled'], ['value' => 1]);
        Setting::updateOrCreate(['key' => 'recurring_orders_deadline_days_before'], ['value' => 1]);
        Setting::updateOrCreate(['key' => 'recurring_orders_deadline_hours_before'], ['value' => 1]);

        $schedule = Schedule::factory()->create(['subscription_reminder_enabled' => true, 'subscription_reminder_days' => 2, 'subscription_reminder_hour' => 2]);
        $pickup = Pickup::factory()->create(['schedule_id' => $schedule->id]);

        $recurring_order_one = RecurringOrder::factory()->create(['paused_at' => null, 'deleted_at' => null, 'reorder_frequency' => 7, 'fulfillment_id' => $pickup->id]);
        $order_one = Order::factory()->create([
            'blueprint_id' => $recurring_order_one->id,
            'deadline_date' => today()->addDays(2),
            'pickup_date' => today()->addDays(3),
            'confirmed' => false
        ]);

        $recurring_order_two = RecurringOrder::factory()->create(['paused_at' => null, 'deleted_at' => null, 'reorder_frequency' => 14, 'fulfillment_id' => $pickup->id]);
        $order_two = Order::factory()->create([
            'blueprint_id' => $recurring_order_two->id,
            'deadline_date' => today()->addDays(2),
            'pickup_date' => today()->addDays(2),
            'confirmed' => false
        ]);

        (new HandleSubscriptionDeadlinesApproaching)->handle();

        Bus::assertDispatchedTimes(FireSubscriptionDeadlineApproachingEvent::class, 2);

        Bus::assertDispatched(
            FireSubscriptionDeadlineApproachingEvent::class,
            fn(FireSubscriptionDeadlineApproachingEvent $job) => $job->order_id === $order_one->id && $job->schedule_id === $schedule->id
        );

        Bus::assertDispatched(
            FireSubscriptionDeadlineApproachingEvent::class,
            fn(FireSubscriptionDeadlineApproachingEvent $job) => $job->order_id === $order_two->id && $job->schedule_id === $schedule->id
        );

        Carbon::setTestNow();
    }

    #[Test]
    public function it_queues_expected_subscription_jobs_when_mixing_multiple_schedules_that_do_not_override_global_settings(): void
    {
        $now = today()->setTime(7, 00, 00);

        Carbon::setTestNow($now);

        Bus::fake([FireSubscriptionDeadlineApproachingEvent::class]);

        Setting::updateOrCreate(['key' => 'timezone'], ['value' => 'America/Chicago']);
        Setting::updateOrCreate(['key' => 'order_deadline_hour'], ['value' => 8]);
        Setting::updateOrCreate(['key' => 'recurring_orders_deadline_email_enabled'], ['value' => 1]);
        Setting::updateOrCreate(['key' => 'recurring_orders_deadline_sms_enabled'], ['value' => 1]);
        Setting::updateOrCreate(['key' => 'recurring_orders_deadline_days_before'], ['value' => 1]);
        Setting::updateOrCreate(['key' => 'recurring_orders_deadline_hours_before'], ['value' => 1]);

        $schedule_one = Schedule::factory()->create(['subscription_reminder_enabled' => null, 'subscription_reminder_days' => 2, 'subscription_reminder_hour' => 2]);
        $pickup_one = Pickup::factory()->create(['schedule_id' => $schedule_one->id]);

        $recurring_order_one = RecurringOrder::factory()->create(['paused_at' => null, 'deleted_at' => null, 'reorder_frequency' => 7, 'fulfillment_id' => $pickup_one->id]);
        $order_one = Order::factory()->create([
            'blueprint_id' => $recurring_order_one->id,
            'deadline_date' => today()->addDays(1),
            'pickup_date' => today()->addDays(3),
            'confirmed' => false
        ]);

        $schedule_two = Schedule::factory()->create(['subscription_reminder_enabled' => null, 'subscription_reminder_days' => 3, 'subscription_reminder_hour' => 3]);
        $pickup_two = Pickup::factory()->create(['schedule_id' => $schedule_two->id]);

        $recurring_order_two = RecurringOrder::factory()->create(['paused_at' => null, 'deleted_at' => null, 'reorder_frequency' => 14, 'fulfillment_id' => $pickup_two->id]);
        $order_two = Order::factory()->create([
            'blueprint_id' => $recurring_order_two->id,
            'deadline_date' => today()->addDays(2),
            'pickup_date' => today()->addDays(2),
            'confirmed' => false
        ]);

        (new HandleSubscriptionDeadlinesApproaching)->handle();

        Bus::assertDispatchedTimes(FireSubscriptionDeadlineApproachingEvent::class, 1);

        Bus::assertDispatched(
            FireSubscriptionDeadlineApproachingEvent::class,
            fn(FireSubscriptionDeadlineApproachingEvent $job) => $job->order_id === $order_one->id && $job->schedule_id === $schedule_one->id
        );

        Bus::assertNotDispatched(
            FireSubscriptionDeadlineApproachingEvent::class,
            fn(FireSubscriptionDeadlineApproachingEvent $job) => $job->order_id === $order_two->id && $job->schedule_id === $schedule_two->id
        );
    }

    #[Test]
    public function it_queues_expected_subscription_jobs_when_mixing_multiple_schedules_that_override_enabled_global_settings(): void
    {
        $now = today()->setTime(6, 00, 00);

        Carbon::setTestNow($now);

        Bus::fake([FireSubscriptionDeadlineApproachingEvent::class]);

        Setting::updateOrCreate(['key' => 'timezone'], ['value' => 'America/Chicago']);
        Setting::updateOrCreate(['key' => 'order_deadline_hour'], ['value' => 8]);
        Setting::updateOrCreate(['key' => 'recurring_orders_deadline_email_enabled'], ['value' => 1]);
        Setting::updateOrCreate(['key' => 'recurring_orders_deadline_sms_enabled'], ['value' => 1]);
        Setting::updateOrCreate(['key' => 'recurring_orders_deadline_days_before'], ['value' => 1]);
        Setting::updateOrCreate(['key' => 'recurring_orders_deadline_hours_before'], ['value' => 1]);

        $schedule_one = Schedule::factory()->create(['subscription_reminder_enabled' => true, 'subscription_reminder_days' => 2, 'subscription_reminder_hour' => 2]);
        $pickup_one = Pickup::factory()->create(['schedule_id' => $schedule_one->id]);

        $recurring_order_one = RecurringOrder::factory()->create(['paused_at' => null, 'deleted_at' => null, 'reorder_frequency' => 7, 'fulfillment_id' => $pickup_one->id]);
        $order_one = Order::factory()->create([
            'blueprint_id' => $recurring_order_one->id,
            'deadline_date' => today()->addDays(2),
            'pickup_date' => today()->addDays(3),
            'confirmed' => false
        ]);

        $schedule_two = Schedule::factory()->create(['subscription_reminder_enabled' => true, 'subscription_reminder_days' => 3, 'subscription_reminder_hour' => 3]);
        $pickup_two = Pickup::factory()->create(['schedule_id' => $schedule_two->id]);

        $recurring_order_two = RecurringOrder::factory()->create(['paused_at' => null, 'deleted_at' => null, 'reorder_frequency' => 14, 'fulfillment_id' => $pickup_two->id]);
        $order_two = Order::factory()->create([
            'blueprint_id' => $recurring_order_two->id,
            'deadline_date' => today()->addDays(3),
            'pickup_date' => today()->addDays(2),
            'confirmed' => false
        ]);

        (new HandleSubscriptionDeadlinesApproaching)->handle();

        Bus::assertDispatchedTimes(FireSubscriptionDeadlineApproachingEvent::class, 1);

        Bus::assertDispatched(
            FireSubscriptionDeadlineApproachingEvent::class,
            fn(FireSubscriptionDeadlineApproachingEvent $job) => $job->order_id === $order_one->id && $job->schedule_id === $schedule_one->id
        );

        Bus::assertNotDispatched(
            FireSubscriptionDeadlineApproachingEvent::class,
            fn(FireSubscriptionDeadlineApproachingEvent $job) => $job->order_id === $order_two->id && $job->schedule_id === $schedule_two->id
        );

        $now = today()->setTime(5, 00, 00);

        Carbon::setTestNow($now);
        Bus::fake([FireSubscriptionDeadlineApproachingEvent::class]);

        (new HandleSubscriptionDeadlinesApproaching)->handle();

        Bus::assertDispatchedTimes(FireSubscriptionDeadlineApproachingEvent::class, 1);

        Bus::assertNotDispatched(
            FireSubscriptionDeadlineApproachingEvent::class,
            fn(FireSubscriptionDeadlineApproachingEvent $job) => $job->order_id === $order_one->id && $job->schedule_id === $schedule_one->id
        );

        Bus::assertDispatched(
            FireSubscriptionDeadlineApproachingEvent::class,
            fn(FireSubscriptionDeadlineApproachingEvent $job) => $job->order_id === $order_two->id && $job->schedule_id === $schedule_two->id
        );

        Carbon::setTestNow();
    }

    #[Test]
    public function it_queues_expected_subscription_jobs_when_mixing_multiple_schedules_that_override_disabled_global_settings(): void
    {
        $now = today()->setTime(6, 00, 00);

        Carbon::setTestNow($now);

        Bus::fake([FireSubscriptionDeadlineApproachingEvent::class]);

        Setting::updateOrCreate(['key' => 'timezone'], ['value' => 'America/Chicago']);
        Setting::updateOrCreate(['key' => 'order_deadline_hour'], ['value' => 8]);
        Setting::updateOrCreate(['key' => 'recurring_orders_deadline_email_enabled'], ['value' => 0]);
        Setting::updateOrCreate(['key' => 'recurring_orders_deadline_sms_enabled'], ['value' => 0]);
        Setting::updateOrCreate(['key' => 'recurring_orders_deadline_days_before'], ['value' => 1]);
        Setting::updateOrCreate(['key' => 'recurring_orders_deadline_hours_before'], ['value' => 1]);

        $schedule_one = Schedule::factory()->create(['subscription_reminder_enabled' => true, 'subscription_reminder_days' => 2, 'subscription_reminder_hour' => 2]);
        $pickup_one = Pickup::factory()->create(['schedule_id' => $schedule_one->id]);

        $recurring_order_one = RecurringOrder::factory()->create(['paused_at' => null, 'deleted_at' => null, 'reorder_frequency' => 7, 'fulfillment_id' => $pickup_one->id]);
        $order_one = Order::factory()->create([
            'blueprint_id' => $recurring_order_one->id,
            'deadline_date' => today()->addDays(2),
            'pickup_date' => today()->addDays(3),
            'confirmed' => false
        ]);

        $schedule_two = Schedule::factory()->create(['subscription_reminder_enabled' => true, 'subscription_reminder_days' => 3, 'subscription_reminder_hour' => 3]);
        $pickup_two = Pickup::factory()->create(['schedule_id' => $schedule_two->id]);

        $recurring_order_two = RecurringOrder::factory()->create(['paused_at' => null, 'deleted_at' => null, 'reorder_frequency' => 14, 'fulfillment_id' => $pickup_two->id]);
        $order_two = Order::factory()->create([
            'blueprint_id' => $recurring_order_two->id,
            'deadline_date' => today()->addDays(3),
            'pickup_date' => today()->addDays(2),
            'confirmed' => false
        ]);

        (new HandleSubscriptionDeadlinesApproaching)->handle();

        Bus::assertDispatchedTimes(FireSubscriptionDeadlineApproachingEvent::class, 1);

        Bus::assertDispatched(
            FireSubscriptionDeadlineApproachingEvent::class,
            fn(FireSubscriptionDeadlineApproachingEvent $job) => $job->order_id === $order_one->id && $job->schedule_id === $schedule_one->id
        );

        Bus::assertNotDispatched(
            FireSubscriptionDeadlineApproachingEvent::class,
            fn(FireSubscriptionDeadlineApproachingEvent $job) => $job->order_id === $order_two->id && $job->schedule_id === $schedule_two->id
        );

        $now = today()->setTime(5, 00, 00);

        Carbon::setTestNow($now);
        Bus::fake([FireSubscriptionDeadlineApproachingEvent::class]);

        (new HandleSubscriptionDeadlinesApproaching)->handle();

        Bus::assertDispatchedTimes(FireSubscriptionDeadlineApproachingEvent::class, 1);

        Bus::assertNotDispatched(
            FireSubscriptionDeadlineApproachingEvent::class,
            fn(FireSubscriptionDeadlineApproachingEvent $job) => $job->order_id === $order_one->id && $job->schedule_id === $schedule_one->id
        );

        Bus::assertDispatched(
            FireSubscriptionDeadlineApproachingEvent::class,
            fn(FireSubscriptionDeadlineApproachingEvent $job) => $job->order_id === $order_two->id && $job->schedule_id === $schedule_two->id
        );

        Carbon::setTestNow();
    }

    #[Test]
    public function it_queues_jobs_when_schedule_uses_enabled_global_settings(): void
    {
        $now = today()->setTime(7, 00, 00);

        Carbon::setTestNow($now);

        Bus::fake([FireSubscriptionDeadlineApproachingEvent::class]);

        Setting::updateOrCreate(['key' => 'timezone'], ['value' => 'America/Chicago']);
        Setting::updateOrCreate(['key' => 'order_deadline_hour'], ['value' => 8]);
        Setting::updateOrCreate(['key' => 'recurring_orders_deadline_email_enabled'], ['value' => 1]);
        Setting::updateOrCreate(['key' => 'recurring_orders_deadline_sms_enabled'], ['value' => 1]);
        Setting::updateOrCreate(['key' => 'recurring_orders_deadline_days_before'], ['value' => 1]);
        Setting::updateOrCreate(['key' => 'recurring_orders_deadline_hours_before'], ['value' => 1]);

        $schedule = Schedule::factory()->create(['subscription_reminder_enabled' => null, 'subscription_reminder_days' => 2, 'subscription_reminder_hour' => 2]);
        $pickup = Pickup::factory()->create(['schedule_id' => $schedule->id]);

        $recurring_order = RecurringOrder::factory()->create(['paused_at' => null, 'deleted_at' => null, 'reorder_frequency' => 7, 'fulfillment_id' => $pickup->id]);
        $order = Order::factory()->create([
            'blueprint_id' => $recurring_order->id,
            'deadline_date' => today()->addDays(1),
            'pickup_date' => today()->addDays(3),
            'confirmed' => false
        ]);

        (new HandleSubscriptionDeadlinesApproaching)->handle();

        Bus::assertDispatchedTimes(FireSubscriptionDeadlineApproachingEvent::class, 1);

        Bus::assertDispatched(
            FireSubscriptionDeadlineApproachingEvent::class,
            fn(FireSubscriptionDeadlineApproachingEvent $job) => $job->order_id === $order->id && $job->schedule_id === $schedule->id
        );
    }

    #[Test]
    public function it_does_not_queue_jobs_when_schedule_uses_disabled_global_settings(): void
    {
        $now = today()->setTime(7, 00, 00);

        Carbon::setTestNow($now);

        Bus::fake([FireSubscriptionDeadlineApproachingEvent::class]);

        Setting::updateOrCreate(['key' => 'timezone'], ['value' => 'America/Chicago']);
        Setting::updateOrCreate(['key' => 'order_deadline_hour'], ['value' => 8]);
        Setting::updateOrCreate(['key' => 'recurring_orders_deadline_email_enabled'], ['value' => 0]);
        Setting::updateOrCreate(['key' => 'recurring_orders_deadline_sms_enabled'], ['value' => 0]);
        Setting::updateOrCreate(['key' => 'recurring_orders_deadline_days_before'], ['value' => 1]);
        Setting::updateOrCreate(['key' => 'recurring_orders_deadline_hours_before'], ['value' => 1]);

        $schedule = Schedule::factory()->create(['subscription_reminder_enabled' => null, 'subscription_reminder_days' => 2, 'subscription_reminder_hour' => 2]);
        $pickup = Pickup::factory()->create(['schedule_id' => $schedule->id]);

        $recurring_order = RecurringOrder::factory()->create(['paused_at' => null, 'deleted_at' => null, 'reorder_frequency' => 7, 'fulfillment_id' => $pickup->id]);
        $order = Order::factory()->create([
            'blueprint_id' => $recurring_order->id,
            'deadline_date' => today()->addDays(1),
            'pickup_date' => today()->addDays(3),
            'confirmed' => false
        ]);

        (new HandleSubscriptionDeadlinesApproaching)->handle();

        Bus::assertNotDispatched(FireSubscriptionDeadlineApproachingEvent::class);
    }

    #[Test]
    public function it_queues_the_expected_jobs_when_reminders_are_enabled(): void
    {
        $now = today()->setTime(6, 00, 00);

        Carbon::setTestNow($now);

        Bus::fake([FireSubscriptionDeadlineApproachingEvent::class]);

        Setting::updateOrCreate(['key' => 'timezone'], ['value' => 'America/Chicago']);
        Setting::updateOrCreate(['key' => 'order_deadline_hour'], ['value' => 8]);
        Setting::updateOrCreate(['key' => 'recurring_orders_deadline_email_enabled'], ['value' => 1]);
        Setting::updateOrCreate(['key' => 'recurring_orders_deadline_sms_enabled'], ['value' => 1]);
        Setting::updateOrCreate(['key' => 'recurring_orders_deadline_days_before'], ['value' => 1]);
        Setting::updateOrCreate(['key' => 'recurring_orders_deadline_hours_before'], ['value' => 1]);

        $schedule = Schedule::factory()->create(['subscription_reminder_enabled' => true, 'subscription_reminder_days' => 2, 'subscription_reminder_hour' => 2]);
        $pickup = Pickup::factory()->create(['schedule_id' => $schedule->id]);

        $recurring_order = RecurringOrder::factory()->create(['paused_at' => null, 'deleted_at' => null, 'reorder_frequency' => 7, 'fulfillment_id' => $pickup->id]);
        $order = Order::factory()->create([
            'blueprint_id' => $recurring_order->id,
            'deadline_date' => today()->addDays(2),
            'pickup_date' => today()->addDays(3),
            'confirmed' => false
        ]);

        (new HandleSubscriptionDeadlinesApproaching)->handle();

        Bus::assertDispatchedTimes(FireSubscriptionDeadlineApproachingEvent::class, 1);

        Bus::assertDispatched(
            FireSubscriptionDeadlineApproachingEvent::class,
            fn(FireSubscriptionDeadlineApproachingEvent $job) => $job->order_id === $order->id && $job->schedule_id === $schedule->id
        );
    }

    #[Test]
    public function it_does_not_queue_the_expected_jobs_when_reminders_are_disabled(): void
    {
        $now = today()->setTime(6, 00, 00);

        Carbon::setTestNow($now);

        Bus::fake([FireSubscriptionDeadlineApproachingEvent::class]);

        Setting::updateOrCreate(['key' => 'timezone'], ['value' => 'America/Chicago']);
        Setting::updateOrCreate(['key' => 'order_deadline_hour'], ['value' => 8]);
        Setting::updateOrCreate(['key' => 'recurring_orders_deadline_email_enabled'], ['value' => 1]);
        Setting::updateOrCreate(['key' => 'recurring_orders_deadline_sms_enabled'], ['value' => 1]);
        Setting::updateOrCreate(['key' => 'recurring_orders_deadline_days_before'], ['value' => 1]);
        Setting::updateOrCreate(['key' => 'recurring_orders_deadline_hours_before'], ['value' => 1]);

        $schedule = Schedule::factory()->create(['subscription_reminder_enabled' => false, 'subscription_reminder_days' => 2, 'subscription_reminder_hour' => 2]);
        $pickup = Pickup::factory()->create(['schedule_id' => $schedule->id]);

        $recurring_order = RecurringOrder::factory()->create(['paused_at' => null, 'deleted_at' => null, 'reorder_frequency' => 7, 'fulfillment_id' => $pickup->id]);
        $order = Order::factory()->create([
            'blueprint_id' => $recurring_order->id,
            'deadline_date' => today()->addDays(2),
            'pickup_date' => today()->addDays(3),
            'confirmed' => false
        ]);

        (new HandleSubscriptionDeadlinesApproaching)->handle();

        Bus::assertNotDispatched(FireSubscriptionDeadlineApproachingEvent::class);
    }

    #[Test]
    public function it_queues_expected_subscription_jobs_when_using_end_of_day_deadline_hour(): void
    {
        $now = today()->setTime(8, 00, 00);

        Carbon::setTestNow($now);

        Bus::fake([FireSubscriptionDeadlineApproachingEvent::class]);

        Setting::updateOrCreate(['key' => 'timezone'], ['value' => 'America/Chicago']);
        Setting::updateOrCreate(['key' => 'order_deadline_hour'], ['value' => 24]);
        Setting::updateOrCreate(['key' => 'recurring_orders_deadline_email_enabled'], ['value' => 1]);
        Setting::updateOrCreate(['key' => 'recurring_orders_deadline_sms_enabled'], ['value' => 1]);
        Setting::updateOrCreate(['key' => 'recurring_orders_deadline_days_before'], ['value' => 0]);
        Setting::updateOrCreate(['key' => 'recurring_orders_deadline_hours_before'], ['value' => 16]);

        $schedule = Schedule::factory()->create(['subscription_reminder_enabled' => null, 'subscription_reminder_days' => 2, 'subscription_reminder_hour' => 2]);
        $pickup = Pickup::factory()->create(['schedule_id' => $schedule->id]);

        $recurring_order_one = RecurringOrder::factory()->create(['paused_at' => null, 'deleted_at' => null, 'reorder_frequency' => 7, 'fulfillment_id' => $pickup->id]);
        $order_one = Order::factory()->create([
            'blueprint_id' => $recurring_order_one->id,
            'deadline_date' => today(),
            'pickup_date' => today()->addDays(3),
            'confirmed' => false
        ]);

        $recurring_order_two = RecurringOrder::factory()->create(['paused_at' => null, 'deleted_at' => null, 'reorder_frequency' => 14, 'fulfillment_id' => $pickup->id]);
        $order_two = Order::factory()->create([
            'blueprint_id' => $recurring_order_two->id,
            'deadline_date' => today(),
            'pickup_date' => today()->addDays(2),
            'confirmed' => false
        ]);

        (new HandleSubscriptionDeadlinesApproaching)->handle();

        Bus::assertDispatchedTimes(FireSubscriptionDeadlineApproachingEvent::class, 2);

        Bus::assertDispatched(
            FireSubscriptionDeadlineApproachingEvent::class,
            fn(FireSubscriptionDeadlineApproachingEvent $job) => $job->order_id === $order_one->id && $job->schedule_id === $schedule->id
        );

        Bus::assertDispatched(
            FireSubscriptionDeadlineApproachingEvent::class,
            fn(FireSubscriptionDeadlineApproachingEvent $job) => $job->order_id === $order_two->id && $job->schedule_id === $schedule->id
        );

        Carbon::setTestNow();
    }

    #[Test]
    public function it_does_not_queues_expected_subscription_jobs_when_using_end_of_day_deadline_hour_that_has_not_been_reached_yet(): void
    {
        $now = today()->setTime(7, 59, 58);

        Carbon::setTestNow($now);

        Bus::fake([FireSubscriptionDeadlineApproachingEvent::class]);

        Setting::updateOrCreate(['key' => 'timezone'], ['value' => 'America/Chicago']);
        Setting::updateOrCreate(['key' => 'order_deadline_hour'], ['value' => 24]);
        Setting::updateOrCreate(['key' => 'recurring_orders_deadline_email_enabled'], ['value' => 1]);
        Setting::updateOrCreate(['key' => 'recurring_orders_deadline_sms_enabled'], ['value' => 1]);
        Setting::updateOrCreate(['key' => 'recurring_orders_deadline_days_before'], ['value' => 0]);
        Setting::updateOrCreate(['key' => 'recurring_orders_deadline_hours_before'], ['value' => 16]);

        $schedule = Schedule::factory()->create(['subscription_reminder_enabled' => null, 'subscription_reminder_days' => 2, 'subscription_reminder_hour' => 2]);
        $pickup = Pickup::factory()->create(['schedule_id' => $schedule->id]);

        $recurring_order_one = RecurringOrder::factory()->create(['paused_at' => null, 'deleted_at' => null, 'reorder_frequency' => 7, 'fulfillment_id' => $pickup->id]);
        $order_one = Order::factory()->create([
            'blueprint_id' => $recurring_order_one->id,
            'deadline_date' => today(),
            'pickup_date' => today()->addDays(3),
            'confirmed' => false
        ]);

        $recurring_order_two = RecurringOrder::factory()->create(['paused_at' => null, 'deleted_at' => null, 'reorder_frequency' => 14, 'fulfillment_id' => $pickup->id]);
        $order_two = Order::factory()->create([
            'blueprint_id' => $recurring_order_two->id,
            'deadline_date' => today(),
            'pickup_date' => today()->addDays(2),
            'confirmed' => false
        ]);

        (new HandleSubscriptionDeadlinesApproaching)->handle();

        Bus::assertNotDispatched(FireSubscriptionDeadlineApproachingEvent::class);

        Carbon::setTestNow();
    }

    #[Test]
    public function it_does_not_queues_expected_subscription_jobs_when_using_end_of_day_deadline_hour_that_has_passed(): void
    {
        $now = today()->setTime(8, 14, 59);

        Carbon::setTestNow($now);

        Bus::fake([FireSubscriptionDeadlineApproachingEvent::class]);

        Setting::updateOrCreate(['key' => 'timezone'], ['value' => 'America/Chicago']);
        Setting::updateOrCreate(['key' => 'order_deadline_hour'], ['value' => 24]);
        Setting::updateOrCreate(['key' => 'recurring_orders_deadline_email_enabled'], ['value' => 1]);
        Setting::updateOrCreate(['key' => 'recurring_orders_deadline_sms_enabled'], ['value' => 1]);
        Setting::updateOrCreate(['key' => 'recurring_orders_deadline_days_before'], ['value' => 0]);
        Setting::updateOrCreate(['key' => 'recurring_orders_deadline_hours_before'], ['value' => 16]);

        $schedule = Schedule::factory()->create(['subscription_reminder_enabled' => null, 'subscription_reminder_days' => 2, 'subscription_reminder_hour' => 2]);
        $pickup = Pickup::factory()->create(['schedule_id' => $schedule->id]);

        $recurring_order_one = RecurringOrder::factory()->create(['paused_at' => null, 'deleted_at' => null, 'reorder_frequency' => 7, 'fulfillment_id' => $pickup->id]);
        $order_one = Order::factory()->create([
            'blueprint_id' => $recurring_order_one->id,
            'deadline_date' => today(),
            'pickup_date' => today()->addDays(3),
            'confirmed' => false
        ]);

        $recurring_order_two = RecurringOrder::factory()->create(['paused_at' => null, 'deleted_at' => null, 'reorder_frequency' => 14, 'fulfillment_id' => $pickup->id]);
        $order_two = Order::factory()->create([
            'blueprint_id' => $recurring_order_two->id,
            'deadline_date' => today(),
            'pickup_date' => today()->addDays(2),
            'confirmed' => false
        ]);

        (new HandleSubscriptionDeadlinesApproaching)->handle();

        Bus::assertNotDispatched(FireSubscriptionDeadlineApproachingEvent::class);

        Carbon::setTestNow();
    }
}