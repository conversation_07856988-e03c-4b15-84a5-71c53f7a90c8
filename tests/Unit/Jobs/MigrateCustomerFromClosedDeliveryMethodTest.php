<?php

namespace Tests\Unit\Jobs;

use App\Jobs\MigrateCustomerFromClosedDeliveryMethod;
use App\Models\Address;
use App\Models\Pickup;
use App\Models\User;
use App\Services\DeliveryMethodService;
use App\Services\Geocoding\GeocodedAddress;
use Illuminate\Database\Eloquent\Collection;
use Mockery\MockInterface;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class MigrateCustomerFromClosedDeliveryMethodTest extends TenantTestCase
{
    #[Test]
    public function it_migrates_a_user_to_another_delivery_method(): void
    {
        $old_delivery_method = Pickup::factory()->create();
        $new_delivery_method = Pickup::factory()->create(['fulfillment_type' => Pickup::FULFILLMENT_TYPE_PICKUP]);
        $user = User::factory()->create(['pickup_point' => $old_delivery_method->id]);

        /** @var Address $address */
        $address = Address::factory()->create();

        $user->addresses()->attach($address->id, ['is_default' => true]);

        $this->mock(DeliveryMethodService::class, function (MockInterface $mock) use ($address, $new_delivery_method) {
            $mock->shouldReceive('find')
                ->once()
                ->with(\Mockery::on(fn(GeocodedAddress $arg) =>
                    $arg->postalCode === $address->postal_code && $arg->state === $address->state
                ))
                ->andReturn(new Collection([$new_delivery_method]));
        });

        (new MigrateCustomerFromClosedDeliveryMethod($old_delivery_method->id, $user->id))->handle();

        $this->assertDatabaseHas('users', [
            'id' => $user->id,
            'pickup_point' => $new_delivery_method->id
        ]);
    }

    #[Test]
    public function it_prefers_delivery_zones_when_migrating_a_user_to_another_delivery_method(): void
    {
        $old_delivery_method = Pickup::factory()->create();
        $new_delivery_method_one = Pickup::factory()->create(['fulfillment_type' => Pickup::FULFILLMENT_TYPE_PICKUP]);
        $new_delivery_method_two = Pickup::factory()->create(['fulfillment_type' => Pickup::FULFILLMENT_TYPE_DELIVERY]);
        $user = User::factory()->create(['pickup_point' => $old_delivery_method->id]);

        /** @var Address $address */
        $address = Address::factory()->create();

        $user->addresses()->attach($address->id, ['is_default' => true]);

        $this->mock(DeliveryMethodService::class, function (MockInterface $mock) use ($address, $new_delivery_method_one, $new_delivery_method_two) {
            $mock->shouldReceive('find')
                ->once()
                ->with(\Mockery::on(fn(GeocodedAddress $arg) =>
                    $arg->postalCode === $address->postal_code && $arg->state === $address->state
                ))
                ->andReturn(new Collection([$new_delivery_method_one, $new_delivery_method_two]));
        });

        (new MigrateCustomerFromClosedDeliveryMethod($old_delivery_method->id, $user->id))->handle();

        $this->assertDatabaseHas('users', [
            'id' => $user->id,
            'pickup_point' => $new_delivery_method_two->id
        ]);
    }

    #[Test]
    public function it_unsets_old_delivery_method_when_customer_does_not_have_an_address(): void
    {
        $old_delivery_method = Pickup::factory()->create();
        $user = User::factory()->create([
            'pickup_point' => $old_delivery_method->id,
            'street' => '',
            'street_2' => '',
            'city' => '',
            'state' => '',
            'zip' => '',
        ]);

        $this->mock(DeliveryMethodService::class, function (MockInterface $mock) {
            $mock->shouldNotReceive('find');
        });

        (new MigrateCustomerFromClosedDeliveryMethod($old_delivery_method->id, $user->id))->handle();

        $this->assertDatabaseHas('users', [
            'id' => $user->id,
            'pickup_point' => null
        ]);
    }

    #[Test]
    public function it_unsets_old_delivery_method_when_no_delivery_methods_are_found(): void
    {
        $old_delivery_method = Pickup::factory()->create();
        $user = User::factory()->create(['pickup_point' => $old_delivery_method->id]);

        /** @var Address $address */
        $address = Address::factory()->create();

        $user->addresses()->attach($address->id, ['is_default' => true]);

        $this->mock(DeliveryMethodService::class, function (MockInterface $mock) use ($address) {
            $mock->shouldReceive('find')
                ->once()
                ->with(\Mockery::on(fn(GeocodedAddress $arg) =>
                    $arg->postalCode === $address->postal_code && $arg->state === $address->state
                ))
                ->andReturn(new Collection());
        });

        (new MigrateCustomerFromClosedDeliveryMethod($old_delivery_method->id, $user->id))->handle();

        $this->assertDatabaseHas('users', [
            'id' => $user->id,
            'pickup_point' => null
        ]);
    }
}
