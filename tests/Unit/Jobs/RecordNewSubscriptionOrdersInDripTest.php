<?php

namespace Tests\Unit\Jobs;

use App\Jobs\RecordBatchOfEventsInDrip;
use App\Jobs\RecordBatchOfOrdersInDrip;
use App\Jobs\RecordBatchOfSubscribersInDrip;
use App\Jobs\RecordNewSubscriptionOrdersInDrip;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\RecurringOrder;
use Carbon\Carbon;
use Illuminate\Support\Facades\Bus;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class RecordNewSubscriptionOrdersInDripTest extends TenantTestCase
{
    #[Test]
    public function it_dispacthes_the_expected_events(): void
    {
        Bus::fake([
            RecordBatchOfEventsInDrip::class,
            RecordBatchOfSubscribersInDrip::class,
            RecordBatchOfOrdersInDrip::class,
        ]);

        $subscription_one = RecurringOrder::factory()->create();
        $subscription_two = RecurringOrder::factory()->create();

        Carbon::setTestNow(now()->subSecond());

        $subscription_one_order_one = Order::factory()->create(['blueprint_id' => $subscription_one->id, 'confirmed' => true, 'canceled_at' => null]);
        $subscription_one_order_one_items = OrderItem::factory()->count(2)->create(['order_id' => $subscription_one_order_one->id]);

        $subscription_two_order_one = Order::factory()->create(['blueprint_id' => $subscription_two->id, 'confirmed' => true, 'canceled_at' => null]);
        $subscription_two_order_one_items = OrderItem::factory()->count(2)->create(['order_id' => $subscription_two_order_one->id]);

        Carbon::setTestNow();

        $subscription_one_order_two = Order::factory()->create(['blueprint_id' => $subscription_one->id, 'confirmed' => true, 'canceled_at' => null]);
        $subscription_one_order_two_items = OrderItem::factory()->count(2)->create(['order_id' => $subscription_one_order_two->id]);

        $subscription_two_order_two = Order::factory()->create(['blueprint_id' => $subscription_two->id, 'confirmed' => true, 'canceled_at' => null]);
        $subscription_two_order_two_items = OrderItem::factory()->count(2)->create(['order_id' => $subscription_two_order_two->id]);

        (new RecordNewSubscriptionOrdersInDrip())->handle();

        Bus::assertDispatched(RecordBatchOfEventsInDrip::class, function (RecordBatchOfEventsInDrip $job)
            use (
                $subscription_one_order_one,
                $subscription_one_order_one_items,
                $subscription_two_order_one,
                $subscription_two_order_one_items,
                $subscription_one_order_two,
                $subscription_one_order_two_items,
                $subscription_two_order_two,
                $subscription_two_order_two_items
            ) {
            $events = collect($job->events);

            return $events->doesntContain(fn ($event) => $event['action'] === 'Confirmed recurring order' && $event['properties']['order_id'] === $subscription_one_order_one->id)
                && $events->doesntContain(fn ($event) => $event['action'] === 'Confirmed recurring order' && $event['properties']['order_id'] === $subscription_two_order_one->id)
                && $events->doesntContain(fn ($event) => $event['action'] === 'Purchased product' && $event['properties']['order_id'] === $subscription_one_order_one->id && $event['properties']['product_id'] === $subscription_one_order_one_items->first()->product_id)
                && $events->doesntContain(fn ($event) => $event['action'] === 'Purchased product' && $event['properties']['order_id'] === $subscription_one_order_one->id && $event['properties']['product_id'] === $subscription_one_order_one_items->last()->product_id)
                && $events->doesntContain(fn ($event) => $event['action'] === 'Purchased product' && $event['properties']['order_id'] === $subscription_two_order_one->id && $event['properties']['product_id'] === $subscription_two_order_one_items->first()->product_id)
                && $events->doesntContain(fn ($event) => $event['action'] === 'Purchased product' && $event['properties']['order_id'] === $subscription_two_order_one->id && $event['properties']['product_id'] === $subscription_two_order_one_items->last()->product_id)
                && $events->contains(fn ($event) => $event['action'] === 'Confirmed recurring order' && $event['properties']['order_id'] === $subscription_one_order_two->id)
                && $events->contains(fn ($event) => $event['action'] === 'Confirmed recurring order' && $event['properties']['order_id'] === $subscription_two_order_two->id)
                && $events->contains(fn ($event) => $event['action'] === 'Purchased product' && $event['properties']['order_id'] === $subscription_one_order_two->id && $event['properties']['product_id'] === $subscription_one_order_two_items->first()->product_id)
                && $events->contains(fn ($event) => $event['action'] === 'Purchased product' && $event['properties']['order_id'] === $subscription_one_order_two->id && $event['properties']['product_id'] === $subscription_one_order_two_items->last()->product_id)
                && $events->contains(fn ($event) => $event['action'] === 'Purchased product' && $event['properties']['order_id'] === $subscription_two_order_two->id && $event['properties']['product_id'] === $subscription_two_order_two_items->first()->product_id)
                && $events->contains(fn ($event) => $event['action'] === 'Purchased product' && $event['properties']['order_id'] === $subscription_two_order_two->id && $event['properties']['product_id'] === $subscription_two_order_two_items->last()->product_id);
        });

        Bus::assertDispatched(RecordBatchOfSubscribersInDrip::class, function (RecordBatchOfSubscribersInDrip $job)
        use (
            $subscription_one_order_one,
            $subscription_one_order_one_items,
            $subscription_two_order_one,
            $subscription_two_order_one_items,
            $subscription_one_order_two,
            $subscription_one_order_two_items,
            $subscription_two_order_two,
            $subscription_two_order_two_items
        ) {
            $subscribers = collect($job->subscribers);

            return $subscribers->doesntContain(fn ($event) => $event['email'] === $subscription_one_order_one->customer->email && in_array('Purchased - ' . $subscription_one_order_one_items->first()->title . ' #' . $subscription_one_order_one_items->first()->product_id, $event['tags']))
                && $subscribers->doesntContain(fn ($event) => $event['email'] === $subscription_one_order_one->customer->email && in_array('Purchased - ' . $subscription_one_order_one_items->last()->title . ' #' . $subscription_one_order_one_items->last()->product_id, $event['tags']))
                && $subscribers->doesntContain(fn ($event) => $event['email'] === $subscription_two_order_one->customer->email && in_array('Purchased - ' . $subscription_two_order_one_items->first()->title . ' #' . $subscription_two_order_one_items->first()->product_id, $event['tags']))
                && $subscribers->doesntContain(fn ($event) => $event['email'] === $subscription_two_order_one->customer->email && in_array('Purchased - ' . $subscription_two_order_one_items->last()->title . ' #' . $subscription_two_order_one_items->last()->product_id, $event['tags']))
                && $subscribers->contains(fn ($event) => $event['email'] === $subscription_one_order_two->customer->email && in_array('Purchased - ' . $subscription_one_order_two_items->first()->title . ' #' . $subscription_one_order_two_items->first()->product_id, $event['tags']))
                && $subscribers->contains(fn ($event) => $event['email'] === $subscription_one_order_two->customer->email && in_array('Purchased - ' . $subscription_one_order_two_items->last()->title . ' #' . $subscription_one_order_two_items->last()->product_id, $event['tags']))
                && $subscribers->contains(fn ($event) => $event['email'] === $subscription_two_order_two->customer->email && in_array('Purchased - ' . $subscription_two_order_two_items->first()->title . ' #' . $subscription_two_order_two_items->first()->product_id, $event['tags']))
                && $subscribers->contains(fn ($event) => $event['email'] === $subscription_two_order_two->customer->email && in_array('Purchased - ' . $subscription_two_order_two_items->last()->title . ' #' . $subscription_two_order_two_items->last()->product_id, $event['tags']));
        });

        Bus::assertDispatched(RecordBatchOfOrdersInDrip::class, function (RecordBatchOfOrdersInDrip $job)
        use (
            $subscription_one_order_one,
            $subscription_one_order_one_items,
            $subscription_two_order_one,
            $subscription_two_order_one_items,
            $subscription_one_order_two,
            $subscription_one_order_two_items,
            $subscription_two_order_two,
            $subscription_two_order_two_items
        ) {
            $orders = collect($job->orders);

            return $orders->doesntContain(fn ($event) => $event['action'] === 'placed' && $event['order_id'] === (string) $subscription_one_order_one->id)
                && $orders->doesntContain(fn ($event) => $event['action'] === 'placed' && $event['order_id'] === (string) $subscription_two_order_one->id)
                && $orders->contains(fn ($event) => $event['action'] === 'placed' && $event['order_id'] === (string) $subscription_one_order_two->id)
                && $orders->contains(fn ($event) => $event['action'] === 'placed' && $event['order_id'] === (string) $subscription_two_order_two->id);
        });
    }

    #[Test]
    public function it_does_not_dispacth_for_unconfirmed_orders(): void
    {
        Bus::fake([
            RecordBatchOfEventsInDrip::class,
            RecordBatchOfSubscribersInDrip::class,
            RecordBatchOfOrdersInDrip::class,
        ]);

        $subscription_one = RecurringOrder::factory()->create();
        $order_one = Order::factory()->create(['blueprint_id' => $subscription_one->id, 'confirmed' => false, 'canceled_at' => null]);
        $order_one_items = OrderItem::factory()->count(2)->create(['order_id' => $order_one->id]);
        $order_two = Order::factory()->create(['blueprint_id' => $subscription_one->id, 'confirmed' => false, 'canceled_at' => null]);
        $order_two_items = OrderItem::factory()->count(2)->create(['order_id' => $order_two->id]);

        (new RecordNewSubscriptionOrdersInDrip())->handle();

        Bus::assertNotDispatched(RecordBatchOfEventsInDrip::class);
        Bus::assertNotDispatched(RecordBatchOfSubscribersInDrip::class);
        Bus::assertNotDispatched(RecordBatchOfOrdersInDrip::class);
    }

    #[Test]
    public function it_does_not_dispacth_for_canceled_orders(): void
    {
        Bus::fake([
            RecordBatchOfEventsInDrip::class,
            RecordBatchOfSubscribersInDrip::class,
            RecordBatchOfOrdersInDrip::class,
        ]);

        $subscription_one = RecurringOrder::factory()->create();
        $order_one = Order::factory()->create(['blueprint_id' => $subscription_one->id, 'confirmed' => true, 'canceled_at' => now()]);
        $order_one_items = OrderItem::factory()->count(2)->create(['order_id' => $order_one->id]);
        $order_two = Order::factory()->create(['blueprint_id' => $subscription_one->id, 'confirmed' => true, 'canceled_at' => now()]);
        $order_two_items = OrderItem::factory()->count(2)->create(['order_id' => $order_two->id]);

        (new RecordNewSubscriptionOrdersInDrip())->handle();

        Bus::assertNotDispatched(RecordBatchOfEventsInDrip::class);
        Bus::assertNotDispatched(RecordBatchOfSubscribersInDrip::class);
        Bus::assertNotDispatched(RecordBatchOfOrdersInDrip::class);
    }

    #[Test]
    public function it_does_not_dispacth_for_one_time_orders(): void
    {
        Bus::fake([
            RecordBatchOfEventsInDrip::class,
            RecordBatchOfSubscribersInDrip::class,
            RecordBatchOfOrdersInDrip::class,
        ]);

        $order_one = Order::factory()->create(['blueprint_id' => null, 'confirmed' => true, 'canceled_at' => null]);
        $order_one_items = OrderItem::factory()->count(2)->create(['order_id' => $order_one->id]);
        $order_two = Order::factory()->create(['blueprint_id' => null, 'confirmed' => true, 'canceled_at' => null]);
        $order_two_items = OrderItem::factory()->count(2)->create(['order_id' => $order_two->id]);

        (new RecordNewSubscriptionOrdersInDrip())->handle();

        Bus::assertNotDispatched(RecordBatchOfEventsInDrip::class);
        Bus::assertNotDispatched(RecordBatchOfSubscribersInDrip::class);
        Bus::assertNotDispatched(RecordBatchOfOrdersInDrip::class);
    }
}
