<?php

namespace Tests\Unit\Jobs;

use App\Billing\Gateway\Customer;
use App\Billing\Gateway\GatewayException;
use App\Contracts\Billing;
use App\Jobs\UpdateCustomerInPaymentGateway;
use App\Models\Card;
use App\Models\User;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class UpdateCustomerInPaymentGatewayTest extends TenantTestCase
{
    #[Test]
    public function it_handles_customer_not_found_exception(): void
    {
        $user = User::factory()->create(['customer_id' => 'cus_12345xyz']);

        $mockedBilling = \Mockery::mock(Billing::class);

        $mockedBilling->shouldReceive('updateCustomer')->once()
            ->with($user->customer_id, \Mockery::on(fn(Customer $args) => true))
            ->andThrow(new GatewayException("No such customer: {$user->customer_id}"));

        (new UpdateCustomerInPaymentGateway($user))->handle($mockedBilling);

        $this->assertDatabaseHas(User::class, [
            'id' => $user->id,
            'customer_id' => null,
        ]);
    }

    #[Test]
    public function it_throws_unhandled_exceptions(): void
    {
        $user = User::factory()->create(['customer_id' => 'cus_12345xyz']);

        $mockedBilling = \Mockery::mock(Billing::class);

        $mockedBilling->shouldReceive('updateCustomer')->once()
            ->with($user->customer_id, \Mockery::on(fn(Customer $args) => true))
            ->andThrow(new GatewayException('Other Exception'));

        $this->expectException(GatewayException::class);
        $this->expectExceptionMessage('Other Exception');

        (new UpdateCustomerInPaymentGateway($user))->handle($mockedBilling);

        $this->assertDatabaseHas(User::class, [
            'id' => $user->id,
            'customer_id' => 'cus_12345xyz',
        ]);
    }
}
