<?php

namespace Tests\Unit\Repositories\Reports;

use App\Models\Order;
use App\Models\Pickup;
use App\Models\Schedule;
use App\Repositories\Reports\DeliverySalesRevenueFromFees;
use App\Support\Enums\OrderStatus;
use Illuminate\Support\Carbon;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class DeliverySalesRevenueFromFeesTest extends TenantTestCase
{
    #[Test]
    public function it_calculates_fee_totals(): void
    {
        $pickup_one = Pickup::factory()->create();
        Order::factory()->create(['pickup_id' => $pickup_one->id, 'confirmed' => true, 'canceled' => false, 'delivery_fee' => 123, 'fees_subtotal' => 222]);
        Order::factory()->create(['pickup_id' => $pickup_one->id, 'confirmed' => true, 'canceled' => false, 'delivery_fee' => 123, 'fees_subtotal' => 222]);

        $pickup_two = Pickup::factory()->create();
        Order::factory()->create(['pickup_id' => $pickup_two->id, 'confirmed' => true, 'canceled' => false, 'delivery_fee' => 223, 'fees_subtotal' => 333]);
        Order::factory()->create(['pickup_id' => $pickup_two->id, 'confirmed' => true, 'canceled' => false, 'delivery_fee' => 223, 'fees_subtotal' => 333]);

        $results = (new DeliverySalesRevenueFromFees())->handle([]);

        $this->assertTrue($results->contains(function ($result) use ($pickup_one) {
            return $result->pickup_id === $pickup_one->id
                && $result->pickup_title === $pickup_one->title
                && $result->fee_total === '444';
        }));

        $this->assertTrue($results->contains(function ($result) use ($pickup_two) {
            return $result->pickup_id === $pickup_two->id
                && $result->pickup_title === $pickup_two->title
                && $result->fee_total === '666';
        }));
    }

    #[Test]
    public function it_filters_out_non_confirmed_orders_by_default(): void
    {
        $pickup = Pickup::factory()->create();
        Order::factory()->create(['pickup_id' => $pickup->id, 'confirmed' => false, 'canceled' => false, 'delivery_fee' => 123, 'fees_subtotal' => 222]);
        Order::factory()->create(['pickup_id' => $pickup->id, 'confirmed' => false, 'canceled' => false, 'delivery_fee' => 123, 'fees_subtotal' => 222]);

        $results = (new DeliverySalesRevenueFromFees())->handle([]);

        $this->assertEmpty($results);
    }

    #[Test]
    public function it_filters_out_canceled_orders_by_default(): void
    {
        $pickup = Pickup::factory()->create();
        Order::factory()->create(['pickup_id' => $pickup->id, 'confirmed' => true, 'canceled' => true, 'delivery_fee' => 123, 'fees_subtotal' => 222]);
        Order::factory()->create(['pickup_id' => $pickup->id, 'confirmed' => true, 'canceled' => true, 'delivery_fee' => 123, 'fees_subtotal' => 222]);

        $results = (new DeliverySalesRevenueFromFees())->handle([]);

        $this->assertEmpty($results);
    }

    #[Test]
    public function it_can_filter_by_order_status(): void
    {
        $pickup = Pickup::factory()->create();
        Order::factory()->create(['pickup_id' => $pickup->id, 'confirmed' => true, 'canceled' => false, 'delivery_fee' => 123, 'fees_subtotal' => 222, 'status_id' => OrderStatus::confirmed()]);
        Order::factory()->create(['pickup_id' => $pickup->id, 'confirmed' => true, 'canceled' => false, 'delivery_fee' => 123, 'fees_subtotal' => 222, 'status_id' => OrderStatus::confirmed()]);

        $results = (new DeliverySalesRevenueFromFees())->handle([]);

        $this->assertTrue($results->contains(function ($result) use ($pickup) {
            return $result->pickup_id === $pickup->id
                && $result->pickup_title === $pickup->title
                && $result->fee_total === '444';
        }));

        $results = (new DeliverySalesRevenueFromFees())->handle(['order_status' => [OrderStatus::confirmed()]]);

        $this->assertTrue($results->contains(function ($result) use ($pickup) {
            return $result->pickup_id === $pickup->id
                && $result->pickup_title === $pickup->title
                && $result->fee_total === '444';
        }));

        $results = (new DeliverySalesRevenueFromFees())->handle(['order_status' => [OrderStatus::canceled()]]);

        $this->assertEmpty($results);
    }

    #[Test]
    public function it_can_filter_by_confirmed_date(): void
    {
        Carbon::setTestNow(now());

        $pickup = Pickup::factory()->create();

        Order::factory()->create(['pickup_id' => $pickup->id, 'confirmed' => true, 'canceled' => false, 'delivery_fee' => 123, 'fees_subtotal' => 222, 'confirmed_date' => today()]);
        Order::factory()->create(['pickup_id' => $pickup->id, 'confirmed' => true, 'canceled' => false, 'delivery_fee' => 123, 'fees_subtotal' => 222, 'confirmed_date' => today()]);

        $results = (new DeliverySalesRevenueFromFees())->handle([]);

        $this->assertTrue($results->contains(function ($result) use ($pickup) {
            return $result->pickup_id === $pickup->id
                && $result->pickup_title === $pickup->title
                && $result->fee_total === '444';
        }));

        $results = (new DeliverySalesRevenueFromFees())->handle(['confirmed_date' => ['start' => today()]]);

        $this->assertTrue($results->contains(function ($result) use ($pickup) {
            return $result->pickup_id === $pickup->id
                && $result->pickup_title === $pickup->title
                && $result->fee_total === '444';
        }));

        $results = (new DeliverySalesRevenueFromFees())->handle(['confirmed_date' => ['end' => today()]]);

        $this->assertTrue($results->contains(function ($result) use ($pickup) {
            return $result->pickup_id === $pickup->id
                && $result->pickup_title === $pickup->title
                && $result->fee_total === '444';
        }));

        $results = (new DeliverySalesRevenueFromFees())->handle(['confirmed_date' => ['start' => today()->addDay()]]);

        $this->assertEmpty($results);

        $results = (new DeliverySalesRevenueFromFees())->handle(['confirmed_date' => ['end' => today()->subDay()]]);

        $this->assertEmpty($results);

        Carbon::setTestNow();
    }

    #[Test]
    public function it_can_filter_by_pickup_date(): void
    {
        Carbon::setTestNow(now());

        $pickup = Pickup::factory()->create();

        Order::factory()->create(['pickup_id' => $pickup->id, 'confirmed' => true, 'canceled' => false, 'delivery_fee' => 123, 'fees_subtotal' => 222, 'pickup_date' => today()]);
        Order::factory()->create(['pickup_id' => $pickup->id, 'confirmed' => true, 'canceled' => false, 'delivery_fee' => 123, 'fees_subtotal' => 222, 'pickup_date' => today()]);

        $results = (new DeliverySalesRevenueFromFees())->handle([]);

        $this->assertTrue($results->contains(function ($result) use ($pickup) {
            return $result->pickup_id === $pickup->id
                && $result->pickup_title === $pickup->title
                && $result->fee_total === '444';
        }));

        $results = (new DeliverySalesRevenueFromFees())->handle(['pickup_date' => ['start' => today()]]);

        $this->assertTrue($results->contains(function ($result) use ($pickup) {
            return $result->pickup_id === $pickup->id
                && $result->pickup_title === $pickup->title
                && $result->fee_total === '444';
        }));

        $results = (new DeliverySalesRevenueFromFees())->handle(['pickup_date' => ['end' => today()]]);

        $this->assertTrue($results->contains(function ($result) use ($pickup) {
            return $result->pickup_id === $pickup->id
                && $result->pickup_title === $pickup->title
                && $result->fee_total === '444';
        }));

        $results = (new DeliverySalesRevenueFromFees())->handle(['pickup_date' => ['start' => today()->addDay()]]);

        $this->assertEmpty($results);

        $results = (new DeliverySalesRevenueFromFees())->handle(['pickup_date' => ['end' => today()->subDay()]]);

        $this->assertEmpty($results);

        Carbon::setTestNow();
    }

    #[Test]
    public function it_can_filter_by_first_time_orders_status(): void
    {
        $pickup = Pickup::factory()->create();

        Order::factory()->create(['pickup_id' => $pickup->id, 'confirmed' => true, 'canceled' => false, 'delivery_fee' => 123, 'fees_subtotal' => 222,  'first_time_order' => true]);
        Order::factory()->create(['pickup_id' => $pickup->id, 'confirmed' => true, 'canceled' => false, 'delivery_fee' => 123, 'fees_subtotal' => 222,  'first_time_order' => true]);

        $results = (new DeliverySalesRevenueFromFees())->handle([]);

        $this->assertTrue($results->contains(function ($result) use ($pickup) {
            return $result->pickup_id === $pickup->id
                && $result->pickup_title === $pickup->title
                && $result->fee_total === '444';
        }));

        $results = (new DeliverySalesRevenueFromFees())->handle(['first_time_order' => true]);

        $this->assertTrue($results->contains(function ($result) use ($pickup) {
            return $result->pickup_id === $pickup->id
                && $result->pickup_title === $pickup->title
                && $result->fee_total === '444';
        }));

        $results = (new DeliverySalesRevenueFromFees())->handle(['first_time_order' => false]);

        $this->assertEmpty($results);
    }

    #[Test]
    public function it_can_filter_by_fulfillment_error_status(): void
    {
        $pickup = Pickup::factory()->create();

        Order::factory()->create(['pickup_id' => $pickup->id, 'confirmed' => true, 'canceled' => false, 'delivery_fee' => 123, 'fees_subtotal' => 222,  'fulfillment_error' => true]);
        Order::factory()->create(['pickup_id' => $pickup->id, 'confirmed' => true, 'canceled' => false, 'delivery_fee' => 123, 'fees_subtotal' => 222,  'fulfillment_error' => true]);

        $results = (new DeliverySalesRevenueFromFees())->handle([]);

        $this->assertTrue($results->contains(function ($result) use ($pickup) {
            return $result->pickup_id === $pickup->id
                && $result->pickup_title === $pickup->title
                && $result->fee_total === '444';
        }));

        $results = (new DeliverySalesRevenueFromFees())->handle(['fulfillment_error' => true]);

        $this->assertTrue($results->contains(function ($result) use ($pickup) {
            return $result->pickup_id === $pickup->id
                && $result->pickup_title === $pickup->title
                && $result->fee_total === '444';
        }));

        $results = (new DeliverySalesRevenueFromFees())->handle(['fulfillment_error' => false]);

        $this->assertEmpty($results);
    }

    #[Test]
    public function it_can_filter_by_sales_channel(): void
    {
        $pickup = Pickup::factory()->create();

        Order::factory()->create(['pickup_id' => $pickup->id, 'confirmed' => true, 'canceled' => false, 'delivery_fee' => 123, 'fees_subtotal' => 222, 'type_id' => 1]);
        Order::factory()->create(['pickup_id' => $pickup->id, 'confirmed' => true, 'canceled' => false, 'delivery_fee' => 123, 'fees_subtotal' => 222, 'type_id' => 1]);

        $results = (new DeliverySalesRevenueFromFees())->handle([]);

        $this->assertTrue($results->contains(function ($result) use ($pickup) {
            return $result->pickup_id === $pickup->id
                && $result->pickup_title === $pickup->title
                && $result->fee_total === '444';
        }));

        $results = (new DeliverySalesRevenueFromFees())->handle(['order_type_id' => [1]]);

        $this->assertTrue($results->contains(function ($result) use ($pickup) {
            return $result->pickup_id === $pickup->id
                && $result->pickup_title === $pickup->title
                && $result->fee_total === '444';
        }));

        $results = (new DeliverySalesRevenueFromFees())->handle(['order_type_id' => [2]]);

        $this->assertEmpty($results);
    }

    #[Test]
    public function it_can_filter_by_schedule(): void
    {
        $schedule = Schedule::factory()->create();
        $other_schedule = Schedule::factory()->create();
        $pickup = Pickup::factory()->create(['schedule_id' => $schedule->id]);

        Order::factory()->create(['pickup_id' => $pickup->id, 'confirmed' => true, 'canceled' => false, 'delivery_fee' => 123, 'fees_subtotal' => 222, 'type_id' => 1]);
        Order::factory()->create(['pickup_id' => $pickup->id, 'confirmed' => true, 'canceled' => false, 'delivery_fee' => 123, 'fees_subtotal' => 222, 'type_id' => 1]);

        $results = (new DeliverySalesRevenueFromFees())->handle([]);

        $this->assertTrue($results->contains(function ($result) use ($pickup) {
            return $result->pickup_id === $pickup->id
                && $result->pickup_title === $pickup->title
                && $result->fee_total === '444';
        }));

        $results = (new DeliverySalesRevenueFromFees())->handle(['schedule_id' => [$schedule->id]]);

        $this->assertTrue($results->contains(function ($result) use ($pickup) {
            return $result->pickup_id === $pickup->id
                && $result->pickup_title === $pickup->title
                && $result->fee_total === '444';
        }));

        $results = (new DeliverySalesRevenueFromFees())->handle(['schedule_id' => [$other_schedule->id]]);

        $this->assertEmpty($results);
    }
}
