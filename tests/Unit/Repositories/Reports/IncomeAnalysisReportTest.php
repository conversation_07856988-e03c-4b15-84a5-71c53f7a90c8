<?php

namespace Tests\Unit\Repositories\Reports;

use App\Repositories\Reports\IncomeAnalysisDeductions;
use App\Repositories\Reports\IncomeAnalysisReport;
use App\Repositories\Reports\IncomeAnalysisRevenueByAccountingClass;
use App\Repositories\Reports\IncomeAnalysisRevenueFromFees;
use Mockery\MockInterface;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class IncomeAnalysisReportTest extends TestCase
{
    #[Test]
    public function it_returns_an_array_of_the_expected_collections(): void
    {
        $line_items = collect([(object) ['accounting_class' => 'Test', 'total' => 123]]);
        $this->mock(IncomeAnalysisRevenueByAccountingClass::class, function (MockInterface $mock) use ($line_items) {
            $mock->shouldReceive('handle')->once()->with(['foo' => 'bar'], true)->andReturn($line_items);
        });

        $fees = collect([(object) ['accounting_class' => 'Fee Income', 'total' => 123]]);
        $this->mock(IncomeAnalysisRevenueFromFees::class, function (MockInterface $mock) use ($fees) {
            $mock->shouldReceive('handle')->once()->with(['foo' => 'bar'])->andReturn($fees);
        });

        $deductions = collect([(object) ['accounting_class' => 'Credits', 'total' => 123]]);
        $this->mock(IncomeAnalysisDeductions::class, function (MockInterface $mock) use ($deductions) {
            $mock->shouldReceive('handle')->once()->with(['foo' => 'bar'])->andReturn($deductions);
        });

        [$result_line_item_revenue, $result_fee_revenue, $result_deductions] = (new IncomeAnalysisReport)->handle(['foo' => 'bar'], true);

        $this->assertSame($line_items, $result_line_item_revenue);
        $this->assertSame($fees, $result_fee_revenue);
        $this->assertSame($deductions, $result_deductions);
    }

    #[Test]
    public function it_does_not_calculate_fees_or_discounts_when_line_item_collection_is_empty(): void
    {
        $line_items = collect();
        $this->mock(IncomeAnalysisRevenueByAccountingClass::class, function (MockInterface $mock) use ($line_items) {
            $mock->shouldReceive('handle')->once()->with(['foo' => 'bar'], true)->andReturn($line_items);
        });

        $this->mock(IncomeAnalysisRevenueFromFees::class, function (MockInterface $mock) {
            $mock->shouldNotReceive('handle');
        });

        $this->mock(IncomeAnalysisDeductions::class, function (MockInterface $mock) {
            $mock->shouldNotReceive('handle');
        });

        [$result_line_item_revenue, $result_fee_revenue, $result_deductions] = (new IncomeAnalysisReport)->handle(['foo' => 'bar'], true);

        $this->assertSame($line_items, $result_line_item_revenue);
        $this->assertEmpty($result_fee_revenue);
        $this->assertEmpty($result_deductions);
    }
}