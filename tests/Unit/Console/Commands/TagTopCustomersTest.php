<?php

namespace Tests\Unit\Console\Commands;

use App\Jobs\TagTopCustomers;
use Illuminate\Support\Facades\Bus;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class TagTopCustomersTest extends TestCase
{
    #[Test]
    public function it_dispatches_the_job_successfully()
    {
        Bus::fake();

        $this->artisan('sevensons:tag-top-customers ' . $limit = 20)
            ->assertExitCode(0);

        Bus::assertDispatched(
            TagTopCustomers::class,
            fn(TagTopCustomers $job) => $job->limit === $limit
        );
    }
}
