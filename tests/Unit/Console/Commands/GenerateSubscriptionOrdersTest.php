<?php

namespace Tests\Unit\Console\Commands;

use App\Jobs\GenerateSubscriptionOrders;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Bus;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class GenerateSubscriptionOrdersTest extends TestCase
{
    #[Test]
    public function it_dispatches_expected_job(): void
    {
        Bus::fake([GenerateSubscriptionOrders::class]);

        Artisan::call('subscriptions:generate-orders');

        Bus::assertDispatched(GenerateSubscriptionOrders::class);
    }
}
