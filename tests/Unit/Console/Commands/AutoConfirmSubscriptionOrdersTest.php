<?php

namespace Tests\Unit\Console\Commands;

use App\Events\Order\OrderWasConfirmed;
use App\Models\Order;
use App\Models\RecurringOrder;
use App\Services\SubscriptionSettingsService;
use Carbon\Carbon;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\Mail;
use Mockery\MockInterface;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class AutoConfirmSubscriptionOrdersTest extends TestCase
{
    #[Test]
    public function it_confirms_expected_orders(): void
    {
        Mail::fake();
        Event::fake([OrderWasConfirmed::class]);

        $today = today();

        Carbon::setTestNow($today);

        $this->mock(SubscriptionSettingsService::class, function (MockInterface $mock) use ($today) {
            $mock->shouldReceive('autoConfirmationDeadlineDate')->withNoArgs()->andReturn($today);
        });

        $order_one = Order::factory()->create([
            'blueprint_id' => RecurringOrder::factory(),
            'confirmed' => false,
            'canceled' => false,
            'deadline_date' => $today,
            'pickup_date' => $today->copy()->addDay()
        ]);

        $order_two = Order::factory()->create([
            'blueprint_id' => RecurringOrder::factory(),
            'confirmed' => false,
            'canceled' => false,
            'deadline_date' => $today,
            'pickup_date' => $today->copy()->addDay()
        ]);

        Artisan::call('grazecart:auto-confirm-orders');

        $this->assertDatabaseHas(Order::class, [
            'id' => $order_one->id,
            'confirmed' => true,
        ]);

        $this->assertDatabaseHas(Order::class, [
            'id' => $order_two->id,
            'confirmed' => true,
        ]);
    }
}
