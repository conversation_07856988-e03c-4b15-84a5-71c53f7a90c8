<?php

namespace Tests\Unit\Console\Commands;

use App\Actions\CaptureMetrics;
use App\Jobs\SendMarketingReview;
use App\Repositories\Reports\MarketingReview\MarketingReview;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Bus;
use Mockery\MockInterface;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class ProcessMarketingReviewsTest extends TestCase
{
    #[Test]
    public function it_processes_the_marketing_review(): void
    {
        Carbon::setTestNow($now = now());

        Bus::fake([SendMarketingReview::class]);

        $this->mock(MarketingReview::class, function (MockInterface $mock) use ($now) {
            $mock->shouldReceive('handle')->once()
                ->with(
                    \Mockery::on(fn(\Carbon\Carbon $date) => $date->year === $now->year && $date->month === $now->month)
                )
                ->andReturn(['abc' => '123']);
        });

        $this->mock(CaptureMetrics::class, function (MockInterface $mock) {
            $mock->shouldReceive('handle')->once()
                ->with(
                    'marketing_review',
                    ['abc' => '123']
                );
        });

        $this->artisan("grazecart:marketing-review")
            ->assertExitCode(0);

        Bus::assertDispatchedTimes(SendMarketingReview::class, 1);

        Bus::assertDispatched(
            SendMarketingReview::class,
            fn(SendMarketingReview $job) => $job->marketing_review === ['abc' => '123']
        );

        Carbon::setTestNow();
    }
}
