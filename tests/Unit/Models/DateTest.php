<?php

namespace Tests\Unit\Models;

use App\Models\Date;
use App\Models\Schedule;
use App\Models\Setting;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class DateTest extends TenantTestCase
{
    #[Test]
    public function it_can_be_converted_to_an_order_windows(): void
    {
        Setting::updateOrCreate(['key' => 'order_deadline_hour'], ['value' => 10]);

        $date = Date::factory()->create(['schedule_id' => Schedule::factory()]);

        $window = $date->toOrderWindows();

        $this->assertEquals($date->id, $window->dateId());
        $this->assertEquals($date->schedule_id, $window->scheduleId());
        $this->assertEquals($date->order_end_date->copy()->setTime(10,0), $window->deadlineDatetime());
        $this->assertEquals($date->pickup_date->copy()->endOfDay(), $window->deliveryDatetime());
    }
}