<?php

namespace Tests\Unit\Models;

use App\Models\Page;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class PageTest extends TenantTestCase
{
    #[Test]
    public function it_can_get_its_own_public_cache_key(): void
    {
        $page = Page::factory()->create();

        $this->assertEquals("pages.{$page->slug}.public", $page->cacheKey(is_public: true));
    }

    #[Test]
    public function it_can_get_its_own_private_cache_key(): void
    {
        $page = Page::factory()->create();

        $this->assertEquals("pages.{$page->slug}.private", $page->cacheKey(is_public: false));
    }

    #[Test]
    public function it_can_get_a_public_cache_key_for_a_slug(): void
    {
        $this->assertEquals("pages./abc-123/abc.public", Page::cacheKeyForSlug(slug: '/abc-123/abc', is_public: true));
    }

    #[Test]
    public function it_can_get_a_private_cache_key_for_a_slug(): void
    {
        $this->assertEquals("pages./abc-123/abc.private", Page::cacheKeyForSlug(slug: '/abc-123/abc', is_public: false));
    }

    #[Test]
    public function it_can_get_its_own_public_html_cache_key(): void
    {
        $page = Page::factory()->create();

        $this->assertEquals("pages.{$page->slug}.html.public", $page->htmlCacheKey(is_public: true));
    }

    #[Test]
    public function it_can_get_its_own_private_html_cache_key(): void
    {
        $page = Page::factory()->create();

        $this->assertEquals("pages.{$page->slug}.html.private", $page->htmlCacheKey(is_public: false));
    }

    #[Test]
    public function it_can_get_a_public_html_cache_key_for_a_slug(): void
    {
        $this->assertEquals("pages./abc-123/abc.html.public", Page::htmlCacheKeyForSlug(slug: '/abc-123/abc', is_public: true));
    }

    #[Test]
    public function it_can_get_a_private_html_cache_key_for_a_slug(): void
    {
        $this->assertEquals("pages./abc-123/abc.html.private", Page::htmlCacheKeyForSlug(slug: '/abc-123/abc', is_public: false));
    }
}
