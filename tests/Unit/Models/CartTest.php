<?php

namespace Tests\Unit\Models;

use App\Cart\Item;
use App\Cart\Subscription;
use App\Contracts\Cartable;
use App\Events\Cart\ExcludedProductsRemovedFromCart;
use App\Models\Address;
use App\Models\Card;
use App\Models\Cart;
use App\Models\Coupon;
use App\Models\Date;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Payment;
use App\Models\Pickup;
use App\Models\PickupFee;
use App\Models\Product;
use App\Models\ProductPriceGroup;
use App\Models\RecurringOrder;
use App\Models\Schedule;
use App\Models\User;
use App\Services\SettingsService;
use App\Services\SubscriptionSettingsService;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Event;
use Mockery\MockInterface;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class CartTest extends TenantTestCase
{
    #[Test]
    public function it_can_instantiate_a_cart_from_an_order(): void
    {
        $pickup = Pickup::factory()->create();

        $card = Card::factory()->create();
        $user = User::factory()->create(['credit' => 1234, 'subscribed_to_sms_marketing_at' => now(), 'checkout_card_id' => $card]);
        $order = Order::factory()->create(['customer_id' => $user->id, 'is_recurring' => true, 'pickup_id' => $pickup]);

        $coupon = Coupon::factory()->create();

        $order->discounts()->attach($coupon->id, ['user_id' => $user->id, 'savings' => 234]);
        $item_one = OrderItem::factory()->create(['order_id' => $order]);
        $item_two = OrderItem::factory()->create(['order_id' => $order]);
        $item_promo = OrderItem::factory()->create(['order_id' => $order, 'type' => 'promo']);

        session(['subscription_frequency' => 14]);

        $cart = Cart::newFromOrder($order);

        $this->assertNotNull($cart->shopper_id);
        $this->assertNotNull($cart->shopper_type);

        $this->assertEquals(User::class, $cart->shopper_type);
        $this->assertEquals($order->customer_id, $cart->shopper_id);

        $this->assertEquals([
            'date_id' =>  null,
            'delivery_method_id' =>  $pickup->id,
            'items' => [
                [
                    'id' => $item_two->id,
                    'product' => [
                        'id' => $item_two->product_id
                    ],
                    'quantity' => $item_two->qty,
                    'weight' => null,
                    'price' => null,
                ],
                [
                    'id' => $item_one->id,
                    'product' => [
                        'id' => $item_one->product_id
                    ],
                    'quantity' => $item_one->qty,
                    'weight' => null,
                    'price' => null,
                ]
            ],
            'subscription' => [
                'frequency' => 14,
                'product_incentive_id' => $item_promo->product_id
            ],
            'discounts' => [
                'coupons' => [
                    [
                        'name' => $coupon->description,
                        'code' => $coupon->code,
                        'amount' => 234
                    ]
                ],
                'gift_card' =>  [
                    'name' =>  '',
                    'code' =>  '',
                    'amount' =>  0,
                ],
                'store_credit' =>  [
                    'amount' =>  1234,
                ]
            ],
            'contact' => [
                'first_name' => $user->first_name,
                'last_name' => $user->last_name,
                'email' => $user->email,
                'phone' => $user->phone,
                'save_for_later' => true,
                'opt_in_to_sms' => true,
                'subscribed_to_sms' => true,
            ],
            'shipping' => [
                'address_id' => null,
                'street' => $user->street,
                'street_2' => $user->street_2,
                'city' => $user->city,
                'state' => $user->state,
                'zip' => $user->zip,
                'country' => 'USA',
                'save_for_later' => true
            ],
            'billing' => [
                'method' => null,
                'source_id' => $card->id,
                'save_for_later' => true
            ]
        ], $cart->extra_attributes->all());
    }

    #[Test]
    public function it_can_get_its_initial_attributes_for_a_user_shopper(): void
    {
        Carbon::setTestNow($now = now());

        $pickup = Pickup::factory()->create();

        $user = User::factory()->create([
            'first_name' => 'Test First',
            'last_name' => 'Test Last',
            'email' => '<EMAIL>',
            'phone' => '************',
            'credit' => 1234,
            'subscribed_to_sms_marketing_at' => $now->format('Y-m-d H:i:s'),
            'pickup_point' => $pickup->id,
            'street' => '123 Test st',
            'street_2' => 'Apt 1',
            'city' => 'Test',
            'state' => 'TE',
            'zip' => '12345',
            'country' => 'US',
        ]);

        $card = Card::factory()->create(['user_id' => $user->id]);

        $user->checkout_card_id = $card->id;

        $expected_attributes = [
            'purchase_type_id' => Cartable::ONE_TIME_PURCHASE,
            'date_id' =>  null,
            'delivery_method_id' => $pickup->id,
            'items' => [],
            'subscription' => null,
            'discounts' => [
                'coupons' => [],
                'gift_card' =>  [
                    'name' =>  '',
                    'code' =>  '',
                    'amount' =>  0,
                ],
                'store_credit' =>  [
                    'amount' => 1234,
                ]
            ],
            'contact' => [
                'first_name' => 'Test First',
                'last_name' => 'Test Last',
                'email' => '<EMAIL>',
                'phone' => '************',
                'save_for_later' => true,
                'opt_in_to_sms' => true,
                'subscribed_to_sms' => true,
            ],
            'shipping' => [
                'address_id' => null,
                'street' => '123 Test st',
                'street_2' => 'Apt 1',
                'city' => 'Test',
                'state' => 'TE',
                'zip' => '12345',
                'country' => app(SettingsService::class)->farmCountry(),
                'save_for_later' => true
            ],
            'billing' => [
                'method' => null,
                'source_id' => $card->id,
                'save_for_later' => true
            ]
        ];

        $initial_attributes = Cart::initialAttributesForShopper($user);

        $expected_attributes['shipping']['address_id'] = Address::query()->latest('id')->select('id')->first()?->id;;

        $this->assertEquals($expected_attributes, $initial_attributes);

        Carbon::setTestNow();
    }

    #[Test]
    public function it_can_get_its_id(): void
    {
        /** @var Cart $cart */
        $cart = Cart::factory()->create();

        $this->assertEquals($cart->id, $cart->cartId());
    }

    #[Test]
    public function it_can_get_its_purchase_type(): void
    {
        /** @var Cart $cart */
        $cart = Cart::factory()->make();

        $this->assertEquals(Cartable::ONE_TIME_PURCHASE, $cart->purchaseType());
    }

    #[Test]
    public function it_can_determine_if_the_cart_is_empty(): void
    {
        /** @var Cart $cart */
        $cart = Cart::factory()->make();

        $this->assertTrue($cart->cartIsEmpty());

        $product = Product::factory()->create();

        $cart = $cart->addItemToCart(new Item(
            id: 'abc-123',
            product: $product,
            quantity: 1,
        ));

        $this->assertFalse($cart->cartIsEmpty());
    }

    #[Test]
    public function it_can_get_the_cart_customer(): void
    {
        /** @var Cart $cart */
        $cart = Cart::factory()->create();

        $this->assertNotNull($cart->cartCustomer());
        $this->assertEquals($cart->shopper_id, $cart->cartCustomer()->id);
    }

    #[Test]
    public function it_can_get_the_cart_location(): void
    {
        $location = Pickup::factory()->create();

        /** @var Cart $cart */
        $cart = Cart::factory()->create(['extra_attributes' => ['delivery_method_id' => $location->id]]);

        $this->assertNotNull($cart->cartLocation());
        $this->assertEquals($location->id, $cart->cartLocation()->id);
    }

    #[Test]
    public function it_can_get_the_cart_pricing_group(): void
    {
        /** @var Cart $cart_one */
        $cart_one = Cart::factory()->create();
        $this->assertNull($cart_one->cartPricingGroup());

        $group_one = ProductPriceGroup::factory()->create();
        $location_one = Pickup::factory()->create(['pricing_group_id' => $group_one->id]);
        $user_one = User::factory()->create(['pricing_group_id' => null]);

        /** @var Cart $cart_two */
        $cart_two = Cart::factory()->create(['shopper_id' => $user_one->id, 'extra_attributes' => ['delivery_method_id' => $location_one->id]]);
        $this->assertEquals($group_one->id, $cart_two->cartPricingGroup()?->id);

        $group_two = ProductPriceGroup::factory()->create();
        $location_one = Pickup::factory()->create(['pricing_group_id' => $group_one->id]);
        $user_one = User::factory()->create(['pricing_group_id' => $group_two->id]);

        $cart_three = Cart::factory()->create(['shopper_id' => $user_one->id, 'extra_attributes' => ['delivery_method_id' => $location_one->id]]);

        $this->assertEquals($group_two->id, $cart_three->cartPricingGroup()?->id);
    }

    #[Test]
    public function it_can_get_its_date(): void
    {
        $date = Date::factory()->create(['schedule_id' => Schedule::factory()]);

        /** @var Cart $cart */
        $cart = Cart::factory()->create(['extra_attributes' => ['date_id' => $date->id]]);
        $this->assertEquals($date->id, $cart->cartDate()?->id);
    }

    #[Test]
    public function it_can_get_its_items(): void
    {
        /** @var Cart $cart */
        $cart = Cart::factory()->make();

        $this->assertEmpty($cart->itemsInCart());

        $product = Product::factory()->create();

        $item = new Item(
            id: 'abc-123',
            product: $product,
            quantity: 1,
        );

        $cart = $cart->addItemToCart($item);

        $this->assertNotEmpty($cart->itemsInCart());
        $this->assertNotNull($cart->itemsInCart()->first(fn(Item $item) => $item->id === 'abc-123'));
    }

    #[Test]
    public function it_can_get_its_subscription(): void
    {
        /** @var Cart $one_time_cart */
        $one_time_cart = Cart::factory()->create();
        $this->assertNull($one_time_cart->cartSubscription());

        /** @var Cart $subscription_cart */
        $subscription_cart = Cart::factory()->create(['extra_attributes' => [
            'purchase_type' => Cartable::SUBSCRIPTION_PURCHASE,
            'subscription' => [
                'frequency' => 14,
                'product_incentive_id' => 123
            ]
        ]]);

        $this->assertNotNull($subscription_cart->cartSubscription());
        $this->assertEquals(14, $subscription_cart->cartSubscription()?->frequency);
        $this->assertEquals(123, $subscription_cart->cartSubscription()?->product_incentive_id);
    }

    #[Test]
    public function it_can_add_item_to_the_cart(): void
    {
        /** @var Cart $cart */
        $cart = Cart::factory()->make();

        $this->assertEmpty($cart->itemsInCart());

        $product = Product::factory()->create();

        $item = new Item(
            id: 'abc-123',
            product: $product,
            quantity: 1,
        );

        $cart = $cart->addItemToCart($item);

        $this->assertNotEmpty($cart->itemsInCart());
        $this->assertNotNull($cart->itemsInCart()->first(fn(Item $item) => $item->id === 'abc-123'));
    }

    #[Test]
    public function it_can_update_the_cart_item_quantity(): void
    {
        /** @var Cart $cart */
        $cart = Cart::factory()->make();

        $product_one = Product::factory()->create();
        $item_one = new Item(
            id: 'abc-123',
            product: $product_one,
            quantity: 1,
        );

        $cart = $cart->addItemToCart($item_one);

        $product_two = Product::factory()->create();
        $item_two = new Item(
            id: 'def-123',
            product: $product_two,
            quantity: 1,
        );

        $cart->addItemToCart($item_two);

        $cart->updateCartItemQuantity('abc-123' , 2);


        $this->assertEquals(2, collect($cart->extra_attributes->get('items', []))->first(fn(array $item) => $item['id'] === 'abc-123')['quantity']);
        $this->assertEquals(1, collect($cart->extra_attributes->get('items', []))->first(fn(array $item) => $item['id'] === 'def-123')['quantity']);

        $cart = $cart->updateCartItemQuantity('def-123' , 3);


        $this->assertEquals(2, collect($cart->extra_attributes->get('items', []))->first(fn(array $item) => $item['id'] === 'abc-123')['quantity']);
        $this->assertEquals(3, collect($cart->extra_attributes->get('items', []))->first(fn(array $item) => $item['id'] === 'def-123')['quantity']);
    }

    #[Test]
    public function it_can_remove_a_cart_item(): void
    {
        /** @var Cart $cart */
        $cart = Cart::factory()->make();

        $product_one = Product::factory()->create();
        $item_one = new Item(
            id: 'abc-123',
            product: $product_one,
            quantity: 1,
        );

        $cart = $cart->addItemToCart($item_one);

        $product_two = Product::factory()->create();
        $item_two = new Item(
            id: 'def-123',
            product: $product_two,
            quantity: 1,
        );

        $cart->addItemToCart($item_two);

        $cart->removeCartItem('abc-123');

        // ensure array is a list and keys are reset upon removal
        $this->assertTrue(array_is_list($cart->extra_attributes->get('items')));

        $this->assertNull(collect($cart->extra_attributes->get('items', []))->first(fn(array $item) => $item['id'] === 'abc-123'));
        $this->assertNotNull(collect($cart->extra_attributes->get('items', []))->first(fn(array $item) => $item['id'] === 'def-123'));

        $cart = $cart->removeCartItem('abc-123');

        // ensure array is a list and keys are reset upon removal
        $this->assertTrue(array_is_list($cart->extra_attributes->get('items')));

        // assert it can handle items not found
        $this->assertNull(collect($cart->extra_attributes->get('items', []))->first(fn(array $item) => $item['id'] === 'abc-123'));
        $this->assertNotNull(collect($cart->extra_attributes->get('items', []))->first(fn(array $item) => $item['id'] === 'def-123'));
    }

    #[Test]
    public function it_can_update_the_cart_location(): void
    {
        /** @var Cart $cart */
        $cart = Cart::factory()->make();

        $location = Pickup::factory()->create();

        $this->assertNotEquals($location->id, $cart->cartLocation()?->id);

        $cart->updateCartLocation($location);


        $this->assertEquals($location->id, $cart->cartLocation()?->id);
        $this->assertNotNull($cart->cartId());

    }

    #[Test]
    public function it_can_stub_the_cart_location(): void
    {
        /** @var Cart $cart */
        $cart = Cart::factory()->make();

        $location = Pickup::factory()->create();

        $this->assertNotEquals($location->id, $cart->cartLocation()?->id);

        $cart->stubCartLocation($location);


        $this->assertEquals($location->id, $cart->cartLocation()?->id);
    }

    #[Test]
    public function it_can_update_the_cart_date_id_with_a_valid_date(): void
    {
        /** @var Cart $cart */
        $cart = Cart::factory()->make();

        $date = Date::factory()->create(['schedule_id' => Schedule::factory()]);

        $this->assertNotEquals($date->id, $cart->cartDate()?->id);

        $cart->updateCartDate($date);


        $this->assertEquals($date->id, $cart->cartDate()?->id);
    }

    #[Test]
    public function it_can_update_the_cart_date_id_with_null(): void
    {
        /** @var Cart $cart */
        $cart = Cart::factory()->make(['extra_attributes' => ['date_id' => 123]]);

        $cart->updateCartDate(null);

        $this->assertNull($cart->cartDate());
    }

    #[Test]
    public function it_can_set_the_cart_as_a_one_time_purchase(): void
    {
        /** @var Cart $cart */
        $cart = Cart::factory()->make(['extra_attributes' => ['purchase_type' => Cartable::SUBSCRIPTION_PURCHASE, 'subscription' => ['frequency' => 14]]]);

        $this->assertTrue($cart->isRecurring());
        $this->assertInstanceOf(Subscription::class, $cart->cartSubscription());

        $cart->setCartAsOneTimePurchase();


        $this->assertFalse($cart->isRecurring());
        $this->assertNull($cart->cartSubscription());
    }

    #[Test]
    public function it_can_set_the_cart_as_a_subscription_purchase(): void
    {
        /** @var Cart $cart */
        $cart = Cart::factory()->make(['extra_attributes' => ['purchase_type' => Cartable::ONE_TIME_PURCHASE, 'subscription' => null]]);

        $product = Product::factory()->create();

        $this->assertFalse($cart->isRecurring());
        $this->assertNull($cart->cartSubscription());


        $cart->setCartAsSubscriptionPurchase(frequency: 14, product_incentive_id: $product->id);


        $this->assertTrue($cart->isRecurring());
        $this->assertInstanceOf(Subscription::class, $cart->cartSubscription());
        $this->assertEquals(14, $cart->cartSubscription()?->frequency);
        $this->assertEquals($product->id, $cart->cartSubscription()?->product_incentive_id);
    }

    #[Test]
    public function it_can_set_determine_if_its_recurring(): void
    {
        /** @var Cart $cart */
        $cart = Cart::factory()->make(['extra_attributes' => ['subscription' => []]]);
        $this->assertTrue($cart->isRecurring());

        /** @var Cart $cart */
        $cart = Cart::factory()->make(['extra_attributes' => ['subscription' => null]]);
        $this->assertFalse($cart->isRecurring());
    }

    #[Test]
    public function it_can_apply_a_coupon_to_a_cart(): void
    {
        /** @var Cart $cart */
        $cart = Cart::factory()->make();

        $this->assertEmpty($cart->cartCoupons());

        $coupon = Coupon::factory()->create();

        $cart->applyCouponToCart($coupon);

        $this->assertCount(1, $cart->cartCoupons());
        /** @var \App\Cart\Coupon $applied */
        $applied = $cart->cartCoupons()->first();
        $this->assertEquals($coupon->description, $applied->name);
        $this->assertEquals($coupon->code, $applied->code);
    }

    #[Test]
    public function it_can_get_its_subtotal_without_price_or_weight_overrides(): void
    {
        /** @var Cart $cart */
        $cart = Cart::factory()->make();

        $weighted_product = Product::factory()->create(['unit_of_issue' => 'weight', 'unit_price' => '12.34', 'weight' => 1.5]);

        $cart->addItemToCart(new Item(
            id: 'abc-123',
            product: $weighted_product,
            quantity: 2
        ));

        $package_product = Product::factory()->create(['unit_of_issue' => 'package', 'unit_price' => '12.34', 'weight' => 2.5]);

        $cart->addItemToCart(new Item(
            id: 'def-123',
            product: $package_product,
            quantity: 2,
        ));



        $this->assertEquals((1234 * (1.5 * 2)) + (1234 * 2), $cart->cartSubtotal());
    }

    #[Test]
    public function it_can_get_its_weight_without_weight_overrides(): void
    {
        /** @var Cart $cart */
        $cart = Cart::factory()->make();

        $weighted_product = Product::factory()->create(['unit_of_issue' => 'weight', 'unit_price' => '12.34', 'weight' => 1.5]);

        $cart->addItemToCart(new Item(
            id: 'abc-123',
            product: $weighted_product,
            quantity: 2
        ));

        $package_product = Product::factory()->create(['unit_of_issue' => 'package', 'unit_price' => '12.34', 'weight' => 2.5]);

        $cart->addItemToCart(new Item(
            id: 'def-123',
            product: $package_product,
            quantity: 2,
        ));

        $this->assertEquals(3.0 + 5.0, $cart->cartWeight());
    }

    #[Test]
    public function it_can_get_its_cart_coupon_total(): void
    {
        /** @var Cart $cart */
        $cart = Cart::factory()->make();

        $coupon_one = Coupon::factory()->create(['discount_type' => 'fixed', 'discount_amount' => 500]);

        $cart->applyCouponToCart($coupon_one);

        $coupon_two = Coupon::factory()->create(['discount_type' => 'fixed', 'discount_amount' => 200]);

        $cart->applyCouponToCart($coupon_two);

        $this->assertEquals(700, $cart->cartCouponTotal());
    }

    #[Test]
    public function it_can_get_its_cart_store_credit_total(): void
    {
        $user = User::factory()->create(['credit' => 1234]);

        /** @var Cart $cart */
        $cart = Cart::factory()->make(['shopper_type' => User::class, 'shopper_id' => $user->id]);

        $this->assertEquals(1234, $cart->cartStoreCreditTotal());
    }

    #[Test]
    public function it_can_get_its_location_fee_total(): void
    {
        $user = User::factory()->create(['exempt_from_fees' => false]);
        $location = Pickup::factory()->create();
        PickupFee::factory()->create(['pickup_id' => $location->id, 'amount' => '12.34']);
        PickupFee::factory()->create(['pickup_id' => $location->id, 'amount' => '22.34']);

        /** @var Cart $cart */
        $cart = Cart::factory()->make(['shopper_type' => User::class, 'shopper_id' => $user->id, 'extra_attributes' => ['delivery_method_id' => $location->id]]);

        $this->assertEquals(3468, $cart->cartLocationFeeTotal());

        $user = User::factory()->create(['exempt_from_fees' => true]);

        /** @var Cart $cart */
        $cart = Cart::factory()->make(['shopper_type' => User::class, 'shopper_id' => $user->id, 'extra_attributes' => ['delivery_method_id' => $location->id]]);

        $this->assertEquals(0, $cart->cartLocationFeeTotal());
    }

    #[Test]
    public function it_can_get_its_subscription_savings_total(): void
    {
        /** @var Cart $cart */
        $cart = Cart::factory()->make([
            'extra_attributes' => [
                'subscription' => [
                    'frequency' => null,
                    'product_incentive_id' => null
                ]
            ]
        ]);

        $product = Product::factory()->create(['unit_of_issue' => 'package', 'unit_price' => '12.34']);

        $cart->addItemToCart(new Item(
            id: 'def-123',
            product: $product,
            quantity: 2,
        ));

        $this->mock(SubscriptionSettingsService::class, function (MockInterface $mock) {
            $mock->shouldReceive('discountIncentive')->andReturn(5);
        });

        $this->assertEquals(round(2468 * 0.05), $cart->cartSubscriptionSavingsTotal());

        $cart->setCartAsOneTimePurchase();

        $this->assertEquals(0, $cart->cartSubscriptionSavingsTotal());
    }

    #[Test]
    public function it_can_get_its_tax_total(): void
    {
        $location = Pickup::factory()->create([
            'tax_delivery_fee' => true,
            'delivery_rate' => '5.00',
            'tax_rate' => 0.10,
            'settings' => [
                'delivery_fee_type' => 2 // fixed
            ]
        ]);

        PickupFee::factory()->create(['pickup_id' => $location->id, 'amount' => '10.00', 'taxable' => false]);
        PickupFee::factory()->create(['pickup_id' => $location->id, 'amount' => '20.00', 'taxable' => true]);

        /** @var Cart $cart */
        $cart = Cart::factory()->make([
            'extra_attributes' => [
                'delivery_method_id' => $location->id
            ]
        ]);

        $non_tax_product = Product::factory()->create(['unit_of_issue' => 'package', 'unit_price' => '10.00', 'taxable' => false]);

        $cart->addItemToCart(new Item(
            id: 'abc-123',
            product: $non_tax_product,
            quantity: 2,
        ));

        $tax_product = Product::factory()->create(['unit_of_issue' => 'package', 'unit_price' => '10.00', 'taxable' => true]);

        $cart->addItemToCart(new Item(
            id: 'def-123',
            product: $tax_product,
            quantity: 3,
        ));

        // taxable subtotal + taxable delivery method fees + taxable delivery fee
        $this->assertEquals(round((3000 * 0.1) + (2000 * 0.1) + (500 * 0.1)), $cart->cartTaxTotal());
    }

    #[Test]
    public function it_can_get_its_tax_total_when_customer_is_exempt_from_fees(): void
    {
        $customer = User::factory()->create(['exempt_from_fees' => true]);

        $location = Pickup::factory()->create([
            'tax_delivery_fee' => true,
            'delivery_rate' => '5.00',
            'tax_rate' => 0.10,
            'settings' => [
                'delivery_fee_type' => 2 // fixed
            ]
        ]);

        PickupFee::factory()->create(['pickup_id' => $location->id, 'amount' => '10.00', 'taxable' => false]);
        PickupFee::factory()->create(['pickup_id' => $location->id, 'amount' => '20.00', 'taxable' => true]);

        /** @var Cart $cart */
        $cart = Cart::factory()->make([
            'shopper_type' => User::class,
            'shopper_id' => $customer->id,
            'extra_attributes' => [
                'delivery_method_id' => $location->id
            ]
        ]);

        $non_tax_product = Product::factory()->create(['unit_of_issue' => 'package', 'unit_price' => '10.00', 'taxable' => false]);

        $cart->addItemToCart(new Item(
            id: 'abc-123',
            product: $non_tax_product,
            quantity: 2,
        ));

        $tax_product = Product::factory()->create(['unit_of_issue' => 'package', 'unit_price' => '10.00', 'taxable' => true]);

        $cart->addItemToCart(new Item(
            id: 'def-123',
            product: $tax_product,
            quantity: 3,
        ));

        // taxable subtotal + taxable delivery method fees + taxable delivery fee
        $this->assertEquals(round((3000 * 0.1) + 0 + 0), $cart->cartTaxTotal());
    }

    #[Test]
    public function it_can_get_its_tax_total_when_customer_is_exempt_from_tax(): void
    {
        $customer = User::factory()->create(['settings' => ['exempt_from_tax' => true]]);

        $location = Pickup::factory()->create([
            'tax_delivery_fee' => true,
            'delivery_rate' => '5.00',
            'tax_rate' => 0.10,
            'settings' => [
                'delivery_fee_type' => 2 // fixed
            ]
        ]);

        PickupFee::factory()->create(['pickup_id' => $location->id, 'amount' => '10.00', 'taxable' => false]);
        PickupFee::factory()->create(['pickup_id' => $location->id, 'amount' => '20.00', 'taxable' => true]);

        /** @var Cart $cart */
        $cart = Cart::factory()->make([
            'shopper_type' => User::class,
            'shopper_id' => $customer->id,
            'extra_attributes' => [
                'delivery_method_id' => $location->id
            ]
        ]);

        $non_tax_product = Product::factory()->create(['unit_of_issue' => 'package', 'unit_price' => '10.00', 'taxable' => false]);

        $cart->addItemToCart(new Item(
            id: 'abc-123',
            product: $non_tax_product,
            quantity: 2,
        ));

        $tax_product = Product::factory()->create(['unit_of_issue' => 'package', 'unit_price' => '10.00', 'taxable' => true]);

        $cart->addItemToCart(new Item(
            id: 'def-123',
            product: $tax_product,
            quantity: 3,
        ));

        $this->assertEquals(0, $cart->cartTaxTotal());
    }

    #[Test]
    public function it_can_get_its_delivery_total_when_threshold_is_not_met(): void
    {
        $customer = User::factory()->create(['settings' => ['exempt_from_fees' => false]]);

        $location = Pickup::factory()->create([
            'tax_delivery_fee' => true,
            'delivery_rate' => '5.00',
            'tax_rate' => 0.10,
            'apply_limit' => true,
            'delivery_total_threshold' => 2001,
            'delivery_fee_cap' => 200,
            'settings' => [
                'delivery_fee_type' => 2 // fixed
            ]
        ]);

        /** @var Cart $cart */
        $cart = Cart::factory()->make([
            'shopper_type' => User::class,
            'shopper_id' => $customer->id,
            'extra_attributes' => [
                'delivery_method_id' => $location->id
            ]
        ]);

        $product = Product::factory()->create(['unit_of_issue' => 'package', 'unit_price' => '10.00']);

        $cart->addItemToCart(new Item(
            id: 'abc-123',
            product: $product,
            quantity: 2,
        ));

        $this->assertEquals(500, $cart->cartDeliveryTotal());
    }

    #[Test]
    public function it_can_get_its_delivery_total_when_threshold_is_met(): void
    {
        $customer = User::factory()->create(['settings' => ['exempt_from_fees' => false]]);

        $location = Pickup::factory()->create([
            'tax_delivery_fee' => true,
            'delivery_rate' => '5.00',
            'tax_rate' => 0.10,
            'apply_limit' => true,
            'delivery_total_threshold' => '20.00',
            'delivery_fee_cap' => '2.00',
            'settings' => [
                'delivery_fee_type' => 2 // fixed
            ]
        ]);

        /** @var Cart $cart */
        $cart = Cart::factory()->make([
            'shopper_type' => User::class,
            'shopper_id' => $customer->id,
            'extra_attributes' => [
                'delivery_method_id' => $location->id
            ]
        ]);

        $product = Product::factory()->create(['unit_of_issue' => 'package', 'unit_price' => '10.00']);

        $cart->addItemToCart(new Item(
            id: 'abc-123',
            product: $product,
            quantity: 2,
        ));

        $this->assertEquals(200, $cart->cartDeliveryTotal());
    }

    #[Test]
    public function it_can_get_its_delivery_total_when_customer_is_exempt_from_fees(): void
    {
        $customer = User::factory()->create(['exempt_from_fees' => true]);

        $location = Pickup::factory()->create([
            'tax_delivery_fee' => true,
            'delivery_rate' => '5.00',
            'tax_rate' => 0.10,
            'apply_limit' => true,
            'delivery_total_threshold' => '20.00',
            'delivery_fee_cap' => '2.00',
            'settings' => [
                'delivery_fee_type' => 2 // fixed
            ]
        ]);

        /** @var Cart $cart */
        $cart = Cart::factory()->make([
            'shopper_type' => User::class,
            'shopper_id' => $customer->id,
            'extra_attributes' => [
                'delivery_method_id' => $location->id
            ]
        ]);

        $product = Product::factory()->create(['unit_of_issue' => 'package', 'unit_price' => '10.00']);

        $cart->addItemToCart(new Item(
            id: 'abc-123',
            product: $product,
            quantity: 2,
        ));

        $this->assertEquals(0, $cart->cartDeliveryTotal());
    }

    #[Test]
    public function it_can_get_its_cart_total_before_store_credit_for_a_one_time_purchase(): void
    {
        $customer = User::factory()->create(['credit' => 1234]);

        $location = Pickup::factory()->create([
            'tax_delivery_fee' => true,
            'delivery_rate' => '5.00',
            'tax_rate' => 0.10,
            'settings' => [
                'delivery_fee_type' => 2 // fixed
            ]
        ]);

        PickupFee::factory()->create(['pickup_id' => $location->id, 'amount' => '12.34', 'taxable' => true]);

        /** @var Cart $cart */
        $cart = Cart::factory()->make([
            'shopper_type' => User::class,
            'shopper_id' => $customer->id,
            'extra_attributes' => [
                'delivery_method_id' => $location->id
            ]
        ]);

        $product = Product::factory()->create(['unit_of_issue' => 'package', 'unit_price' => '10.00', 'taxable' => true]);

        $cart->addItemToCart(new Item(
            id: 'abc-123',
            product: $product,
            quantity: 2,
        ));

        $coupon = Coupon::factory()->create(['discount_amount' => 100, 'discount_type' => 'fixed']);

        $cart->applyCouponToCart($coupon);

        // $this->cartSubtotal + $this->cartLocationFeeTotal + $this->cartDeliveryTotal + $this->cartTaxTotal() - $this->cartSubscriptionSavingsTotal() - $this->cartCouponTotal();
        $this->assertEquals(2000 + 1234 + 500 + (200 + 123 + 50) - 0 - 100, $cart->cartTotalBeforeStoreCredit());
    }

    #[Test]
    public function it_can_get_its_cart_total_before_store_credit_for_a_subscription_purchase(): void
    {
        $customer = User::factory()->create(['credit' => 1234]);

        $location = Pickup::factory()->create([
            'tax_delivery_fee' => true,
            'delivery_rate' => '5.00',
            'tax_rate' => 0.10,
            'settings' => [
                'delivery_fee_type' => 2 // fixed
            ]
        ]);

        PickupFee::factory()->create(['pickup_id' => $location->id, 'amount' => '12.34', 'taxable' => true]);

        /** @var Cart $cart */
        $cart = Cart::factory()->make([
            'shopper_type' => User::class,
            'shopper_id' => $customer->id,
            'extra_attributes' => [
                'delivery_method_id' => $location->id,
                'subscription' => []
            ]
        ]);

        $product = Product::factory()->create(['unit_of_issue' => 'package', 'unit_price' => '10.00', 'taxable' => true]);

        $cart->addItemToCart(new Item(
            id: 'abc-123',
            product: $product,
            quantity: 2,
        ));

        $coupon = Coupon::factory()->create(['discount_amount' => 100, 'discount_type' => 'fixed']);

        $cart->applyCouponToCart($coupon);

        $this->mock(SubscriptionSettingsService::class, function (MockInterface $mock) {
            $mock->shouldReceive('discountIncentive')->andReturn(10);
        });

        // $this->cartSubtotal + $this->cartLocationFeeTotal + $this->cartDeliveryTotal + $this->cartTaxTotal() - $this->cartSubscriptionSavingsTotal() - $this->cartCouponTotal();
        $this->assertEquals(2000 + 1234 + 500 + (200 + 123 + 50) - 200 - 100, $cart->cartTotalBeforeStoreCredit());
    }

    #[Test]
    public function it_can_get_its_cart_total_when_customer_credit_does_not_exceed_cart_total(): void
    {
        $customer = User::factory()->create(['credit' => 1234]);

        $location = Pickup::factory()->create([
            'tax_delivery_fee' => true,
            'delivery_rate' => '5.00',
            'tax_rate' => 0.10,
            'settings' => [
                'delivery_fee_type' => 2 // fixed
            ]
        ]);

        PickupFee::factory()->create(['pickup_id' => $location->id, 'amount' => '12.34', 'taxable' => true]);

        /** @var Cart $cart */
        $cart = Cart::factory()->make([
            'shopper_type' => User::class,
            'shopper_id' => $customer->id,
            'extra_attributes' => [
                'delivery_method_id' => $location->id,
                'subscription' => []
            ]
        ]);

        $product = Product::factory()->create(['unit_of_issue' => 'package', 'unit_price' => '10.00', 'taxable' => true]);

        $cart->addItemToCart(new Item(
            id: 'abc-123',
            product: $product,
            quantity: 2,
        ));

        $coupon = Coupon::factory()->create(['discount_amount' => 100, 'discount_type' => 'fixed']);

        $cart->applyCouponToCart($coupon);

        $this->mock(SubscriptionSettingsService::class, function (MockInterface $mock) {
            $mock->shouldReceive('discountIncentive')->andReturn(10);
        });

        // $this->cartTotalBeforeStoreCredit - $this->cartStoreCreditTotal;
        $this->assertEquals(2000 + 1234 + 500 + (200 + 123 + 50) - 200 - 100 - 1234, $cart->cartTotal());
    }

    #[Test]
    public function it_can_get_its_cart_total_when_customer_credit_exceeds_cart_total(): void
    {
        $customer = User::factory()->create(['credit' => 123400]);

        $location = Pickup::factory()->create([
            'tax_delivery_fee' => true,
            'delivery_rate' => '5.00',
            'tax_rate' => 0.10,
            'settings' => [
                'delivery_fee_type' => 2 // fixed
            ]
        ]);

        PickupFee::factory()->create(['pickup_id' => $location->id, 'amount' => '12.34', 'taxable' => true]);

        /** @var Cart $cart */
        $cart = Cart::factory()->make([
            'shopper_type' => User::class,
            'shopper_id' => $customer->id,
            'extra_attributes' => [
                'delivery_method_id' => $location->id,
                'subscription' => []
            ]
        ]);

        $product = Product::factory()->create(['unit_of_issue' => 'package', 'unit_price' => '10.00', 'taxable' => true]);

        $cart->addItemToCart(new Item(
            id: 'abc-123',
            product: $product,
            quantity: 2,
        ));

        $coupon = Coupon::factory()->create(['discount_amount' => 100, 'discount_type' => 'fixed']);

        $cart->applyCouponToCart($coupon);

        $this->mock(SubscriptionSettingsService::class, function (MockInterface $mock) {
            $mock->shouldReceive('discountIncentive')->andReturn(10);
        });

        // $this->cartTotalBeforeStoreCredit - $this->cartStoreCreditTotal;
        $this->assertEquals(0, $cart->cartTotal());
    }

    #[Test]
    public function it_is_not_eligible_for_subscription_when_cart_only_has_excluded_items(): void
    {
        $one_time_product = Product::factory()->create();

        $schedule = Schedule::factory()->create(['type_id' => Schedule::TYPE_REPEATING, 'reorder_frequency' => [7]]);
        $pickup = Pickup::factory()->create(['schedule_id' => $schedule->id]);
        $user = User::factory()->create();

        /** @var Cart $cart */
        $cart = Cart::factory()->make([
            'shopper_type' => User::class,
            'shopper_id' => $user->id,
            'extra_attributes' => [
                'delivery_method_id' => $pickup->id,
                'subscription' => []
            ]
        ]);

        $cart->addItemToCart(new Item(id: 'abc-123', product: $one_time_product));

        $this->mock(SubscriptionSettingsService::class, function (MockInterface $mock) use ($one_time_product) {
            $mock->shouldReceive('excludedProductIds')->andReturn(collect([$one_time_product->id]));
        });

        $this->assertFalse($cart->cartIsEligibleForSubscription());
    }

    #[Test]
    public function it_is_not_eligible_for_subscription_when_customer_is_already_on_subscription(): void
    {
        $schedule = Schedule::factory()->create(['type_id' => Schedule::TYPE_REPEATING, 'reorder_frequency' => [7]]);
        $pickup = Pickup::factory()->create(['schedule_id' => $schedule->id]);
        $user = User::factory()->create();

        /** @var Cart $cart */
        $cart = Cart::factory()->make([
            'shopper_type' => User::class,
            'shopper_id' => $user->id,
            'extra_attributes' => [
                'delivery_method_id' => $pickup->id,
                'subscription' => []
            ]
        ]);

        RecurringOrder::factory()->create(['customer_id' => $user->id]);

        $this->mock(SubscriptionSettingsService::class, function (MockInterface $mock) {
            $mock->shouldReceive('oneTimeOrdesAreEnabled')->andReturnTrue();
            $mock->shouldReceive('excludedProductIds')->andReturn(collect());
        });

        $this->assertFalse($cart->cartIsEligibleForSubscription());
    }

    #[Test]
    public function it_is_not_eligible_for_subscription_when_schedule_is_not_repeating(): void
    {
        $schedule = Schedule::factory()->create(['type_id' => Schedule::TYPE_CUSTOM, 'reorder_frequency' => [7]]);
        $pickup = Pickup::factory()->create(['schedule_id' => $schedule->id]);
        $user = User::factory()->create();

        /** @var Cart $cart */
        $cart = Cart::factory()->make([
            'shopper_type' => User::class,
            'shopper_id' => $user->id,
            'extra_attributes' => [
                'delivery_method_id' => $pickup->id,
                'subscription' => []
            ]
        ]);

        $this->mock(SubscriptionSettingsService::class, function (MockInterface $mock) {
            $mock->shouldReceive('excludedProductIds')->andReturn(collect());
        });

        $this->assertFalse($cart->cartIsEligibleForSubscription());
    }


    #[Test]
    public function it_is_not_eligible_for_subscription_when_schedule_has_no_frequency_options(): void
    {
        $product = Product::factory()->create();
        $one_time_product = Product::factory()->create();

        $schedule = Schedule::factory()->create(['type_id' => Schedule::TYPE_REPEATING, 'reorder_frequency' => []]);
        $pickup = Pickup::factory()->create(['schedule_id' => $schedule->id]);
        $user = User::factory()->create();

        /** @var Cart $cart */
        $cart = Cart::factory()->make([
            'shopper_type' => User::class,
            'shopper_id' => $user->id,
            'extra_attributes' => [
                'delivery_method_id' => $pickup->id,
                'subscription' => []
            ]
        ]);

        $cart->addItemToCart(new Item(id: 'abc-123', product: $product));
        $cart->addItemToCart(new Item(id: 'def-123', product: $one_time_product));


        $this->mock(SubscriptionSettingsService::class, function (MockInterface $mock) use ($one_time_product) {
            $mock->shouldReceive('excludedProductIds')->andReturn(collect([$one_time_product->id]));
        });

        $this->assertFalse($cart->cartIsEligibleForSubscription());
    }

    #[Test]
    public function it_determines_when_order_is_eligible_for_subscription(): void
    {
        $product = Product::factory()->create();
        $one_time_product = Product::factory()->create();

        $schedule = Schedule::factory()->create(['type_id' => Schedule::TYPE_REPEATING, 'reorder_frequency' => [7]]);
        $pickup = Pickup::factory()->create(['schedule_id' => $schedule->id]);
        $user = User::factory()->create();

        /** @var Cart $cart */
        $cart = Cart::factory()->make([
            'shopper_type' => User::class,
            'shopper_id' => $user->id,
            'extra_attributes' => [
                'delivery_method_id' => $pickup->id,
                'subscription' => []
            ]
        ]);

        $cart->addItemToCart(new Item(id: 'abc-123', product: $product));
        $cart->addItemToCart(new Item(id: 'def-123', product: $one_time_product));


        $this->mock(SubscriptionSettingsService::class, function (MockInterface $mock) use ($one_time_product) {
            $mock->shouldReceive('excludedProductIds')->andReturn(collect([$one_time_product->id]));
        });

        $this->assertTrue($cart->cartIsEligibleForSubscription());
    }

    #[Test]
    public function it_can_determine_if_it_has_subscription_eligible_items(): void
    {
        $product = Product::factory()->create();
        $one_time_product = Product::factory()->create();

        $schedule = Schedule::factory()->create(['type_id' => Schedule::TYPE_REPEATING, 'reorder_frequency' => [7]]);
        $pickup = Pickup::factory()->create(['schedule_id' => $schedule->id]);
        $user = User::factory()->create();

        /** @var Cart $cart */
        $cart = Cart::factory()->make([
            'shopper_type' => User::class,
            'shopper_id' => $user->id,
            'extra_attributes' => [
                'delivery_method_id' => $pickup->id,
                'subscription' => []
            ]
        ]);

        $cart->addItemToCart(new Item(id: 'abc-123', product: $product));
        $cart->addItemToCart(new Item(id: 'def-123', product: $one_time_product));


        $this->mock(SubscriptionSettingsService::class, function (MockInterface $mock) use ($one_time_product) {
            $mock->shouldReceive('recurringOrdersAreEnabled')->andReturnTrue();
            $mock->shouldReceive('enrollmentIsOpen')->andReturnTrue();
            $mock->shouldReceive('oneTimeOrdersAreEnabled')->andReturnTrue();
            $mock->shouldReceive('excludedProductIds')->andReturn(collect([$one_time_product->id]));
        });

        $this->assertTrue($cart->hasSubscriptionEligibleItems());

        /** @var Cart $cart */
        $cart2 = Cart::factory()->make([
            'shopper_type' => User::class,
            'shopper_id' => $user->id,
            'extra_attributes' => [
                'delivery_method_id' => $pickup->id,
                'subscription' => []
            ]
        ]);

        $cart2->addItemToCart(new Item(id: 'def-123', product: $one_time_product));

        $this->assertFalse($cart2->hasSubscriptionEligibleItems());
    }

    #[Test]
    public function it_can_set_contact_info(): void
    {
        /** @var Cart $cart */
        $cart = Cart::factory()->create([
            'extra_attributes' => [
                'contact' => [
                    'first_name' => 'existing first',
                    'last_name' => 'existing last',
                    'email' => 'existing email',
                    'phone' => 'existing phone',
                    'save_for_later' => false,
                    'opt_in_to_sms' => false,
                    'subscribed_to_sms' => false
                ],
            ]
        ]);

        $cart->setContactInfo([
            'first_name' => 'new first',
            'last_name' => 'new last',
            'email' => 'new email',
            'phone' => 'new phone',
            'save_for_later' => true,
            'opt_in_to_sms' => true,
            'subscribed_to_sms' => true
        ]);

        $this->assertEquals([
            'first_name' => 'new first',
            'last_name' => 'new last',
            'email' => 'existing email',
            'phone' => 'new phone',
            'save_for_later' => true,
            'opt_in_to_sms' => true,
            'subscribed_to_sms' => true
        ], $cart->extra_attributes->get('contact'));

        $this->assertTrue(Cart::query()->withExtraAttributes([
            'contact->first_name' => 'new first',
            'contact->last_name' => 'new last',
            'contact->email' => 'existing email',
            'contact->phone' => 'new phone',
            'contact->save_for_later' => true,
            'contact->opt_in_to_sms' => true,
            'contact->subscribed_to_sms' => true
        ])->exists());

        $cart->setContactInfo([
            'first_name' => 'new again first',
        ]);

        $this->assertEquals([
            'first_name' => 'new again first',
            'last_name' => 'new last',
            'email' => 'existing email',
            'phone' => 'new phone',
            'save_for_later' => true,
            'opt_in_to_sms' => true,
            'subscribed_to_sms' => true
        ], $cart->extra_attributes->get('contact'));

        $this->assertTrue(Cart::query()->withExtraAttributes([
            'contact->first_name' => 'new again first',
            'contact->last_name' => 'new last',
            'contact->email' => 'existing email',
            'contact->phone' => 'new phone',
            'contact->save_for_later' => true,
            'contact->opt_in_to_sms' => true,
            'contact->subscribed_to_sms' => true
        ])->exists());
    }

    #[Test]
    public function it_can_set_shipping_info(): void
    {
        /** @var Cart $cart */
        $cart = Cart::factory()->create([
            'extra_attributes' => [
                'shipping' => [
                    'street' => 'existing street',
                    'street_2' => 'existing street 2',
                    'city' => 'existing city',
                    'state' => 'existing state',
                    'zip' => 'existing zip',
                    'country' => false,
                    'save_for_later' => false,
                ],
            ]
        ]);

        $cart->setShippingInfo([
            'street' => 'new street',
            'street_2' => 'new street 2',
            'city' => 'new city',
            'state' => 'new state',
            'zip' => 'new zip',
            'country' => true,
            'save_for_later' => true,
        ]);

        $this->assertEquals([
            'address_id' => null,
            'street' => 'new street',
            'street_2' => 'new street 2',
            'city' => 'new city',
            'state' => 'new state',
            'zip' => 'new zip',
            'country' => true,
            'save_for_later' => true,
        ], $cart->extra_attributes->get('shipping'));

        $this->assertTrue(Cart::query()->withExtraAttributes([
            'shipping->address_id' => null,
            'shipping->street' => 'new street',
            'shipping->street_2' => 'new street 2',
            'shipping->city' => 'new city',
            'shipping->state' => 'new state',
            'shipping->zip' => 'new zip',
            'shipping->country' => true,
            'shipping->save_for_later' => true,
        ])->exists());

        $cart->setShippingInfo([
            'street' => 'new again street',
        ]);

        $this->assertEquals([
            'address_id' => null,
            'street' => 'new again street',
            'street_2' => 'new street 2',
            'city' => 'new city',
            'state' => 'new state',
            'zip' => 'new zip',
            'country' => true,
            'save_for_later' => true,
        ], $cart->extra_attributes->get('shipping'));

        $this->assertTrue(Cart::query()->withExtraAttributes([
            'shipping->street' => 'new again street',
            'shipping->street_2' => 'new street 2',
            'shipping->city' => 'new city',
            'shipping->state' => 'new state',
            'shipping->zip' => 'new zip',
            'shipping->country' => true,
            'shipping->save_for_later' => true,
        ])->exists());
    }

    #[Test]
    public function it_can_set_billing_info(): void
    {
        /** @var Cart $cart */
        $cart = Cart::factory()->create([
            'extra_attributes' => [
                'billing' => [
                    'method' => 'existing method',
                    'source_id' => 'existing source id',
                    'save_for_later' => false,
                ],
            ]
        ]);

        $cart->setBillingInfo([
            'method' => 'new method',
            'source_id' => 'new source id',
            'save_for_later' => true,
        ]);

        $this->assertEquals([
            'method' => 'new method',
            'source_id' => 'new source id',
            'save_for_later' => true,
        ], $cart->extra_attributes->get('billing'));

        $this->assertTrue(Cart::query()->withExtraAttributes([
            'billing->method' => 'new method',
            'billing->source_id' => 'new source id',
            'billing->save_for_later' => true,
        ])->exists());

        $cart->setBillingInfo([
            'method' => 'new again method',
        ]);

        $this->assertEquals([
            'method' => 'new again method',
            'source_id' => 'new source id',
            'save_for_later' => true,
        ], $cart->extra_attributes->get('billing'));

        $this->assertTrue(Cart::query()->withExtraAttributes([
            'billing->method' => 'new again method',
            'billing->source_id' => 'new source id',
            'billing->save_for_later' => true,
        ])->exists());
    }

    #[Test]
    public function it_can_update_short_stock_items(): void
    {
        /** @var Cart $cart */
        $cart = Cart::factory()->make();

        $this->assertEmpty($cart->itemsInCart());

        $available_product = Product::factory()->create(['inventory' => 10]);
        $short_product = Product::factory()->create(['inventory' => 3]);
        $out_product = Product::factory()->create(['inventory' => 1]);

        $available_item = new Item(
            id: 'abc-123',
            product: $available_product,
            quantity: 1,
        );

        $cart = $cart->addItemToCart($available_item);

        $short_item = new Item(
            id: 'def-123',
            product: $short_product,
            quantity: 2,
        );

        $cart = $cart->addItemToCart($short_item);

        $out_item = new Item(
            id: 'ghi-123',
            product: $out_product,
            quantity: 1,
        );

        $cart = $cart->addItemToCart($out_item);

        $short_product->inventory = 1;
        $short_product->save();

        $out_product->inventory = 0;
        $out_product->save();

        $this->assertNotEmpty($cart->itemsInCart());

        $this->assertNotNull($cart->itemsInCart()->first(fn(Item $item) => $item->id === 'abc-123' && $item->quantity === 1));
        $this->assertNotNull($cart->itemsInCart()->first(fn(Item $item) => $item->id === 'def-123' && $item->quantity === 2));
        $this->assertNotNull($cart->itemsInCart()->first(fn(Item $item) => $item->id === 'ghi-123' && $item->quantity === 1));

        $cart = $cart->removeOutOfStockItems(true);

        $this->assertNotEmpty($cart->itemsInCart());

        $this->assertNotNull($cart->itemsInCart()->first(fn(Item $item) => $item->id === 'abc-123' && $item->quantity === 1));
        $this->assertNotNull($cart->itemsInCart()->first(fn(Item $item) => $item->id === 'def-123' && $item->quantity === 1));
        $this->assertNull($cart->itemsInCart()->first(fn(Item $item) => $item->id === 'ghi-123'));
    }

    #[Test]
    public function it_can_remove_short_stock_items(): void
    {
        /** @var Cart $cart */
        $cart = Cart::factory()->make();

        $this->assertEmpty($cart->itemsInCart());

        $available_product = Product::factory()->create(['inventory' => 10]);
        $short_product = Product::factory()->create(['inventory' => 3]);
        $out_product = Product::factory()->create(['inventory' => 1]);

        $available_item = new Item(
            id: 'abc-123',
            product: $available_product,
            quantity: 1,
        );

        $cart = $cart->addItemToCart($available_item);

        $short_item = new Item(
            id: 'def-123',
            product: $short_product,
            quantity: 2,
        );

        $cart = $cart->addItemToCart($short_item);

        $out_item = new Item(
            id: 'ghi-123',
            product: $out_product,
            quantity: 1,
        );

        $cart = $cart->addItemToCart($out_item);

        $short_product->inventory = 1;
        $short_product->save();

        $out_product->inventory = 0;
        $out_product->save();

        $this->assertNotEmpty($cart->itemsInCart());

        $this->assertNotNull($cart->itemsInCart()->first(fn(Item $item) => $item->id === 'abc-123' && $item->quantity === 1));
        $this->assertNotNull($cart->itemsInCart()->first(fn(Item $item) => $item->id === 'def-123' && $item->quantity === 2));
        $this->assertNotNull($cart->itemsInCart()->first(fn(Item $item) => $item->id === 'ghi-123' && $item->quantity === 1));

        $cart = $cart->removeOutOfStockItems();

        $this->assertNotEmpty($cart->itemsInCart());

        $this->assertNotNull($cart->itemsInCart()->first(fn(Item $item) => $item->id === 'abc-123' && $item->quantity === 1));
        $this->assertNull($cart->itemsInCart()->first(fn(Item $item) => $item->id === 'def-123'));
        $this->assertNull($cart->itemsInCart()->first(fn(Item $item) => $item->id === 'ghi-123'));
    }

    #[Test]
    public function it_knows_if_a_billing_method_has_been_selected_when_delivery_method_has_limited_options(): void
    {
        $enabled_payments = Payment::factory(2)->create(['enabled' => true]);
        $disabled_payments = Payment::factory(2)->create(['enabled' => false]);

        $delivery_method = Pickup::factory()->create([
            'payment_methods' => [$enabled_payments[0]->id, $disabled_payments[0]->id]
        ]);

        $cart = Cart::factory()->make();
        $cart->updateCartLocation($delivery_method);

        // not set
        $this->assertFalse($cart->hasSelectedBillingMethod());

        // set, enabled, and allowed by location
        $cart->setBillingInfo(['method' => $enabled_payments[0]->key]);
        $this->assertTrue($cart->hasSelectedBillingMethod());

        // set, enabled, but not allowed by location
        $cart->setBillingInfo(['method' => $enabled_payments[1]->key]);
        $this->assertFalse($cart->hasSelectedBillingMethod());

        // set, allowed by location, but globally disabled
        $cart->setBillingInfo(['method' => $disabled_payments[0]->key]);
        $this->assertFalse($cart->hasSelectedBillingMethod());

        // set, disabled, and not allowed by location
        $cart->setBillingInfo(['method' => $disabled_payments[1]->key]);
        $this->assertFalse($cart->hasSelectedBillingMethod());
    }

    #[Test]
    public function it_knows_if_a_billing_method_has_been_selected_when_delivery_method_uses_global_options(): void
    {
        $enabled_payments = Payment::factory(2)->create(['enabled' => true]);
        $disabled_payments = Payment::factory(2)->create(['enabled' => false]);

        $delivery_method = Pickup::factory()->create(['payment_methods' => []]);

        $cart = Cart::factory()->make();
        $cart->updateCartLocation($delivery_method);

        // not set
        $this->assertFalse($cart->hasSelectedBillingMethod());

        // set and enabled globally
        $cart->setBillingInfo(['method' => $enabled_payments[0]->key]);
        $this->assertTrue($cart->hasSelectedBillingMethod());

        // set and enabled globally
        $cart->setBillingInfo(['method' => $enabled_payments[1]->key]);
        $this->assertTrue($cart->hasSelectedBillingMethod());

        // set and disabled globally
        $cart->setBillingInfo(['method' => $disabled_payments[0]->key]);
        $this->assertFalse($cart->hasSelectedBillingMethod());

        // set and disabled globally
        $cart->setBillingInfo(['method' => $disabled_payments[1]->key]);
        $this->assertFalse($cart->hasSelectedBillingMethod());
    }

    #[Test]
    public function it_can_remove_excluded_items_from_cart_when_the_new_location_excludes_them(): void
    {
        Event::fake([ExcludedProductsRemovedFromCart::class]);

        $delivery_method = Pickup::factory()->create();

        $product = Product::factory()->create();
        $excluded_product = Product::factory()->create();

        $delivery_method->products()->attach($excluded_product->id);

        $cart = Cart::factory()->make();
        $cart->addProduct($product);
        $cart->addProduct($excluded_product);

        $cart->updateCartLocation($delivery_method);

        $this->assertTrue(
            $cart->itemsInCart()->contains(fn(Item $item) => $item->product->id === $product->id)
        );

        $this->assertTrue(
            $cart->itemsInCart()->doesntContain(fn(Item $item) => $item->product->id === $excluded_product->id)
        );

        Event::assertDispatched(
            ExcludedProductsRemovedFromCart::class,
            function (ExcludedProductsRemovedFromCart $event) use ($excluded_product) {
                return $event->removed_products->contains(fn(Product $product) => $product->id === $excluded_product->id);
            }
        );
    }

    #[Test]
    public function it_can_get_its_user_shipping_info(): void
    {
        $user = User::factory()->create([
            'street' => '123 Main St',
            'street_2' => 'Apt 1',
            'city' => 'Test',
            'state' => 'TE',
            'zip' => '12345',
            'country' => 'USA',
        ]);

        $cart = new Cart(['shopper_type' => User::class, 'shopper_id' => $user->id]);

        $shipping_info = $cart->getShippingInfo();

        $expected_country = app(SettingsService::class)->farmCountry();

        $this->assertEquals('123 Main St', $shipping_info[ 'street']);
        $this->assertEquals('Apt 1', $shipping_info['street_2']);
        $this->assertEquals('Test', $shipping_info['city']);
        $this->assertEquals('TE', $shipping_info['state']);
        $this->assertEquals('12345', $shipping_info['zip']);
        $this->assertEquals($expected_country, $shipping_info['country']);

        $address = Address::factory()->create([
            'street' => '223 Main St',
            'city' => 'Test',
            'state' => 'TE',
            'postal_code' => '22345',
            'country' => 'USA',
        ]);

        $user->addresses()->attach($address->id, ['name' => 'Non default', 'street_2' => 'Apt 2', 'is_default' => false]);

        $shipping_info = $cart->getShippingInfo();

        $this->assertEquals('123 Main St', $shipping_info[ 'street']);
        $this->assertEquals('Apt 1', $shipping_info['street_2']);
        $this->assertEquals('Test', $shipping_info['city']);
        $this->assertEquals('TE', $shipping_info['state']);
        $this->assertEquals('12345', $shipping_info['zip']);
        $this->assertEquals($expected_country, $shipping_info['country']);

        $user->addresses()->detach($address->id);

        $user->addresses()->attach($address->id, ['name' => 'Default', 'street_2' => 'Apt 3', 'is_default' => true]);

        $shipping_info = $cart->getShippingInfo();

        $this->assertEquals('123 Main St', $shipping_info[ 'street']);
        $this->assertEquals('Apt 1', $shipping_info['street_2']);
        $this->assertEquals('Test', $shipping_info['city']);
        $this->assertEquals('TE', $shipping_info['state']);
        $this->assertEquals('12345', $shipping_info['zip']);
        $this->assertEquals($expected_country, $shipping_info['country']);

        $shipping_info = $cart->setShippingInfo([
            'street' => '423 Main St',
            'street_2' => 'Apt 4',
            'city' => 'Test',
            'state' => 'TE',
            'zip' => '42345',
            'country' => 'USA',
        ]);

        $shipping_info = $cart->getShippingInfo();

        $this->assertEquals('423 Main St', $shipping_info[ 'street']);
        $this->assertEquals('Apt 4', $shipping_info['street_2']);
        $this->assertEquals('Test', $shipping_info['city']);
        $this->assertEquals('TE', $shipping_info['state']);
        $this->assertEquals('42345', $shipping_info['zip']);
        $this->assertEquals('USA', $shipping_info['country']);
    }

    #[Test]
    public function it_can_get_its_current_quantities_by_product_id(): void
    {
        /** @var Cart $cart */
        $cart = Cart::factory()->make();

        $this->assertEmpty($cart->itemsInCart());

        $product_one = Product::factory()->create();

        $cart = $cart->addItemToCart(new Item(
            id: 'abc-123',
            product: $product_one,
            quantity: 1,
        ));

        $product_two = Product::factory()->create();

        $cart = $cart->addItemToCart(new Item(
            id: 'abc-456',
            product: $product_two,
            quantity: 3,
        ));

        $result = $cart->quantitiesByProductId();

        $this->assertInstanceOf(Collection::class, $result);

        $this->assertEquals(1, $result->get($product_one->id));
        $this->assertEquals(3, $result->get($product_two->id));
    }

    #[Test]
    public function it_can_get_its_current_quantities_with_bundles_by_product_id(): void
    {
        /** @var Cart $cart */
        $cart = Cart::factory()->make();

        $this->assertEmpty($cart->itemsInCart());

        $product_one = Product::factory()->create();

        // item 1
        $cart = $cart->addItemToCart(new Item(
            id: 'abc-123',
            product: $product_one,
            quantity: 2,
        ));

        $product_two = Product::factory()->create();
        $product_three = Product::factory()->create();

        $bundle_one = Product::factory()->create(['is_bundle' => true, 'track_inventory' => 'bundle']);
        $bundle_one->bundle()->attach($product_one->id, ['qty' => 2]);
        $bundle_one->bundle()->attach($product_two->id, ['qty' => 1]);
        $bundle_one->bundle()->attach($product_three->id, ['qty' => 4]);

        // item 2
        $cart = $cart->addItemToCart(new Item(
            id: 'abc-456',
            product: $bundle_one,
            quantity: 3,
        ));

        $bundle_two = Product::factory()->create(['is_bundle' => true, 'track_inventory' => 'yes']);
        $bundle_two->bundle()->attach($product_three->id, ['qty' => 2]);

        // item 3
        $cart = $cart->addItemToCart(new Item(
            id: 'abc-789',
            product: $bundle_two,
            quantity: 2,
        ));

        // item 4
        $cart = $cart->addItemToCart(new Item(
            id: 'abc-012',
            product: $product_three,
            quantity: 5,
        ));

        $result = $cart->quantitiesByProductId();

        $this->assertInstanceOf(Collection::class, $result);

        // 2 + 6 = 8 (item 1: 2, item 2: 6)
        $this->assertEquals(8, $result->get($product_one->id));

        // 3 (item 2: 3)
        $this->assertEquals(3, $result->get($product_two->id));

        // 0 + 12 + 0 + 5 = 17 (item 1: 0, item 2: 12, item 3: 0, item 4: 5)
        $this->assertEquals(17, $result->get($product_three->id));
    }
}
