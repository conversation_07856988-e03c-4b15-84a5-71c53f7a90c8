<?php

namespace Tests\Unit\Models;

use App\Models\Setting;
use App\Models\Template;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class TemplateTest extends TenantTestCase
{
    #[Test]
    public function it_can_its_from_email_address(): void
    {
        config(['mail.from.address' => '<EMAIL>']);

        $template = Template::factory()->make(['from_email' => '<EMAIL>']);

        $this->assertEquals('<EMAIL>', $template->getFromEmail());
    }

    #[Test]
    public function it_can_its_from_name(): void
    {
        Setting::updateOrCreate(['key' => 'farm_name'], ['value' => 'Test name']);

        $template = Template::factory()->make(['from_name' => null]);

        $this->assertEquals('Test name', $template->getFromName());

        $template = Template::factory()->make(['from_name' => 'New name']);

        $this->assertEquals('New name', $template->getFromName());
    }

    #[Test]
    public function it_can_its_reply_to_email(): void
    {
        Setting::updateOrCreate(['key' => 'email_general'], ['value' => '<EMAIL>']);

        $template = Template::factory()->make(['reply_to' => null]);

        $this->assertEquals('<EMAIL>', $template->getReplyToEmail());

        $template = Template::factory()->make(['reply_to' => '<EMAIL>']);

        $this->assertEquals('<EMAIL>', $template->getReplyToEmail());
    }
}
