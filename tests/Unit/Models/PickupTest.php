<?php

namespace Tests\Unit\Models;

use App\Jobs\MigrateCustomersFromClosedDeliveryMethod;
use App\Models\Date;
use App\Models\Payment;
use App\Models\Pickup;
use App\Models\PickupState;
use App\Models\PickupZip;
use App\Models\Product;
use App\Models\Schedule;
use App\OrderWindow;
use App\Support\Enums\PickupStatus;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Bus;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class PickupTest extends TenantTestCase
{
    #[Test]
    public function it_can_determine_if_it_excludes_a_product(): void
    {
        /** @var Pickup $pickup */
        $pickup = Pickup::factory()->create();

        /** @var Product $product */
        $product = Product::factory()->create();

        $this->assertFalse($pickup->excludesProduct($product));

        $pickup->products()->attach($product->id);

        $this->assertTrue($pickup->excludesProduct($product));
    }

    #[Test]
    public function it_can_fetch_active_order_window_collection_when_there_are_no_dates(): void
    {
        /** @var Pickup $pickup */
        $pickup = Pickup::factory()->create();

        $this->assertInstanceOf(Collection::class, $pickup->activeOrderWindowCollection());
        $this->assertEmpty($pickup->activeOrderWindowCollection());
    }

    #[Test]
    public function it_can_fetch_active_active_order_window_collection_when_there_are_no_dates(): void
    {
        /** @var Pickup $pickup */
        $pickup = Pickup::factory()->create();

        $this->assertInstanceOf(Collection::class, $pickup->activeOrderWindowCollection());
        $this->assertEmpty($pickup->activeOrderWindowCollection());
    }

    #[Test]
    public function it_can_fetch_active_order_window_collection_when_there_are_active_open_dates(): void
    {
        Carbon::setTestNow(now());

        $schedule = Schedule::factory()->create();
        $date = Date::factory()->create([
            'schedule_id' => $schedule->id,
            'order_start_date' => today()->subDay(),
            'order_end_date' => today()->addDay(),
            'pickup_date' => today()->addDays(2),
        ]);

        $pickup = Pickup::factory()->create(['schedule_id' => $schedule->id]);

        $this->assertInstanceOf(Collection::class, $pickup->activeOrderWindowCollection());
        $this->assertCount(1, $pickup->activeOrderWindowCollection());

        /** @var OrderWindow $order_window */
        $order_window = $pickup->activeOrderWindowCollection()->first();

        $this->assertEquals($date->id, $order_window->dateId());
        $this->assertEquals(today()->addDay()->endOfDay(), $order_window->deadlineDatetime());
        $this->assertEquals(today()->subDay(), $order_window->startDatetime());
        $this->assertEquals(today()->addDays(2)->endOfDay(), $order_window->deliveryDatetime());
        $this->assertEquals($schedule->id, $order_window->scheduleId());

        Carbon::setTestNow();
    }

    #[Test]
    public function it_can_fetch_order_window_collection_when_there_are_dates_that_have_already_ended(): void
    {
        Carbon::setTestNow(now());

        $schedule = Schedule::factory()->create();
        Date::factory()->create([
            'schedule_id' => $schedule->id,
            'order_start_date' => today()->subDays(2),
            'order_end_date' => today()->subDay(),
            'pickup_date' => today()->addDays(2),
        ]);

        $pickup = Pickup::factory()->create(['schedule_id' => $schedule->id]);

        $this->assertInstanceOf(Collection::class, $pickup->activeOrderWindowCollection());
        $this->assertEmpty($pickup->activeOrderWindowCollection());

        Carbon::setTestNow();
    }

    #[Test]
    public function it_can_fetch_active_order_window_collection_when_there_are_dates_that_have_already_ended(): void
    {
        Carbon::setTestNow(now());

        $schedule = Schedule::factory()->create();
        Date::factory()->create([
            'schedule_id' => $schedule->id,
            'order_start_date' => today()->subDays(2),
            'order_end_date' => today()->subDay(),
            'pickup_date' => today()->addDays(2),
        ]);

        $pickup = Pickup::factory()->create(['schedule_id' => $schedule->id]);

        $this->assertInstanceOf(Collection::class, $pickup->activeOrderWindowCollection());
        $this->assertEmpty($pickup->activeOrderWindowCollection());

        Carbon::setTestNow();
    }

    #[Test]
    public function it_can_fetch_order_window_collection_when_there_are_dates_that_have_not_started(): void
    {
        Carbon::setTestNow(now());

        $schedule = Schedule::factory()->create();
        Date::factory()->create([
            'schedule_id' => $schedule->id,
            'order_start_date' => today()->addDays(2),
            'order_end_date' => today()->addDays(4),
            'pickup_date' => today()->addDays(5),
        ]);

        $pickup = Pickup::factory()->create(['schedule_id' => $schedule->id]);

        $this->assertInstanceOf(Collection::class, $pickup->activeOrderWindowCollection());

        Carbon::setTestNow();
    }

    #[Test]
    public function it_can_fetch_active_order_window_collection_when_there_are_dates_that_have_not_started(): void
    {
        Carbon::setTestNow(now());

        $schedule = Schedule::factory()->create();
        Date::factory()->create([
            'schedule_id' => $schedule->id,
            'order_start_date' => today()->addDays(2),
            'order_end_date' => today()->addDays(4),
            'pickup_date' => today()->addDays(5),
        ]);

        $pickup = Pickup::factory()->create(['schedule_id' => $schedule->id]);

        $this->assertInstanceOf(Collection::class, $pickup->activeOrderWindowCollection());
        $this->assertEmpty($pickup->activeOrderWindowCollection());

        Carbon::setTestNow();
    }

    #[Test]
    public function it_can_determine_if_it_contains_an_address(): void
    {
        /** @var Pickup $location */
        $location = Pickup::factory()->create(['fulfillment_type' => 1]);
        $this->assertTrue($location->containsAddress([
            'state' => 'AB',
            'zip' => '12345',
        ]));

        $location->fulfillment_type = 2;
        $this->assertFalse($location->containsAddress([
            'state' => 'AB',
            'zip' => '12345',
        ]));

        PickupZip::factory()->create(['pickup_id' => $location->id, 'zip' => '12345']);
        $this->assertTrue($location->containsAddress([
            'state' => 'BC',
            'zip' => '12345',
        ]));

        PickupState::factory()->create(['pickup_id' => $location->id, 'state' => 'AB']);
        $this->assertTrue($location->containsAddress([
            'state' => 'AB',
            'zip' => '99999',
        ]));
    }

    #[Test]
    public function it_globally_scopes_to_non_virtual_by_default(): void
    {
        $pickup = Pickup::factory()->create(['fulfillment_type' => Pickup::FULFILLMENT_TYPE_PICKUP]);
        $delivery = Pickup::factory()->create(['fulfillment_type' => Pickup::FULFILLMENT_TYPE_DELIVERY]);
        $virtual = Pickup::factory()->create(['fulfillment_type' => Pickup::FULFILLMENT_TYPE_VIRTUAL]);

        $default_ids = Pickup::pluck('id');

        $this->assertTrue($default_ids->contains($pickup->id));
        $this->assertTrue($default_ids->contains($delivery->id));
        $this->assertFalse($default_ids->contains($virtual->id));
    }

    #[Test]
    public function it_can_scope_to_virtual(): void
    {
        $pickup = Pickup::factory()->create(['fulfillment_type' => Pickup::FULFILLMENT_TYPE_PICKUP]);
        $delivery = Pickup::factory()->create(['fulfillment_type' => Pickup::FULFILLMENT_TYPE_DELIVERY]);
        $virtual = Pickup::factory()->create(['fulfillment_type' => Pickup::FULFILLMENT_TYPE_VIRTUAL]);

        $virtual_ids = Pickup::virtual()->pluck('id');

        $this->assertFalse($virtual_ids->contains($pickup->id));
        $this->assertFalse($virtual_ids->contains($delivery->id));
        $this->assertTrue($virtual_ids->contains($virtual->id));
    }

    #[Test]
    public function it_can_fetch_a_virtual_pickup_for_gift_cards_when_one_does_not_exist(): void
    {
        Pickup::virtual()->delete();

        $card_payment = Payment::firstOrNew(['key' => 'card'], ['title' => 'Card']);
        $card_payment->key = 'card';
        $card_payment->save();

        $pickup = Pickup::forGiftCards();

        $this->assertEquals('gift-card-fulfillment', $pickup->slug);
        $this->assertEquals(Pickup::FULFILLMENT_TYPE_VIRTUAL, $pickup->fulfillment_type);
        $this->assertEquals('Gift Card Fulfillment', $pickup->title);
        $this->assertEquals([$card_payment->id], $pickup->payment_methods);
    }

    #[Test]
    public function it_can_fetch_a_virtual_pickup_for_gift_cards_when_already_exists(): void
    {
        Pickup::virtual()->delete();

        $card_payment = Payment::firstOrNew(['key' => 'card'], ['title' => 'Card']);
        $card_payment->key = 'card';
        $card_payment->save();

        $pickup_one = Pickup::forGiftCards();

        $this->assertEquals('gift-card-fulfillment', $pickup_one->slug);
        $this->assertEquals(Pickup::FULFILLMENT_TYPE_VIRTUAL, $pickup_one->fulfillment_type);
        $this->assertEquals('Gift Card Fulfillment', $pickup_one->title);
        $this->assertEquals([$card_payment->id], $pickup_one->payment_methods);

        $pickup_two = Pickup::forGiftCards();

        $this->assertEquals($pickup_one->id, $pickup_two->id);
    }

    #[Test]
    public function it_can_determine_if_it_is_virtual(): void
    {
        $pickup = Pickup::factory()->make(['fulfillment_type' => Pickup::FULFILLMENT_TYPE_PICKUP]);
        $this->assertFalse($pickup->isVirtual());

        $pickup = Pickup::factory()->make(['fulfillment_type' => Pickup::FULFILLMENT_TYPE_DELIVERY]);
        $this->assertFalse($pickup->isVirtual());

        $pickup = Pickup::factory()->make(['fulfillment_type' => Pickup::FULFILLMENT_TYPE_VIRTUAL]);
        $this->assertTrue($pickup->isVirtual());
    }

    #[Test]
    public function it_queues_a_job_to_migrate_users_when_a_pickup_location_status_changes_to_closed(): void
    {
        Bus::fake([MigrateCustomersFromClosedDeliveryMethod::class]);

        $pickup_one = Pickup::factory()->create(['status_id' => PickupStatus::OPEN->value]);

        $pickup_one->status_id = PickupStatus::CLOSED->value;
        $pickup_one->save();

        Bus::assertDispatched(
            MigrateCustomersFromClosedDeliveryMethod::class,
            fn(MigrateCustomersFromClosedDeliveryMethod $job) => $job->delivery_method_id === $pickup_one->id
        );

        $pickup_two = Pickup::factory()->create(['status_id' => PickupStatus::CLOSED->value]);

        $pickup_two->status_id = PickupStatus::CLOSED->value;
        $pickup_two->save();

        Bus::assertNotDispatched(
            MigrateCustomersFromClosedDeliveryMethod::class,
            fn(MigrateCustomersFromClosedDeliveryMethod $job) => $job->delivery_method_id === $pickup_two->id
        );
    }
}
