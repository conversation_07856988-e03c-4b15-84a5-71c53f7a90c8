<?php

namespace Tests\Unit\Services;

use App\Services\GoogleSheetsService;
use Illuminate\Support\Facades\Http;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class GoogleSheetsServiceTest extends TenantTestCase
{
    #[Test]
    public function it_successfully_gets_getWorksheetContents()
    {
        config(['services.google.sheets_api_key' => 'lowkey']);

        $expected_response = [
            [
                ['sku', 'off_site_inventory'],
                ['AS0017', '612'],
                ['AS0037', '402'],
                ['AS0027', '0'],
            ],
        ];

        Http::fake([
            'https://sheets.googleapis.com/v4/spreadsheets/qwerty1/values/Sheet1*' => Http::response(
                [
                    'values' => $expected_response,
                ],
                200
            ),
        ]);

        $service = (new GoogleSheetsService());

        $this->assertEquals($expected_response, $service->getWorksheetContents('qwerty1', 'Sheet1'));
    }

    #[Test]
    public function testGetWorksheetContentsThrowsExceptionOnFailure()
    {
        config(['services.google.sheets_api_key' => 'lowkey']);
        
        $spreadsheetId = 'test_spreadsheet_id';
        $worksheetName = 'test_worksheet';

        Http::fake([
            'sheets.googleapis.com/*' => Http::response('Error', 400),
        ]);

        $service = (new GoogleSheetsService());

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Failed to fetch Google Sheet data: Error');

        $service->getWorksheetContents($spreadsheetId, $worksheetName);
    }
}
