<?php

namespace Tests\Unit\Services;

use App\Models\Pickup;
use App\Models\Product;
use App\Services\DeliveryMethodService;
use App\Services\Geocoding\GeocodedAddress;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class DeliveryMethodServiceTest extends TenantTestCase
{
    #[Test]
    public function it_can_find_pickup_locations_and_delivery_zones_by_postal_code(): void
    {
        $pickup_one = Pickup::factory()->create(['fulfillment_type' => Pickup::FULFILLMENT_TYPE_PICKUP]);
        $pickup_two = Pickup::factory()->create(['fulfillment_type' => Pickup::FULFILLMENT_TYPE_PICKUP]);
        $pickup_two->zips()->create(['zip' => '12345']);

        $delivery_one = Pickup::factory()->create(['fulfillment_type' => Pickup::FULFILLMENT_TYPE_DELIVERY]);
        $delivery_two = Pickup::factory()->create(['fulfillment_type' => Pickup::FULFILLMENT_TYPE_DELIVERY]);
        $delivery_two->zips()->create(['zip' => '12345']);

        $address = new GeocodedAddress(
            lat: 1.234,
            lng: 2.345,
            city: 'Anytown',
            state: 'NY',
            postalCode: '12345',
            country: 'US',
            accuracy: 1
        );

        $results = (new DeliveryMethodService)->find($address);

        $this->assertFalse($results->contains(fn(Pickup $result) => $result->id === $pickup_one->id));
        $this->assertTrue($results->contains(fn(Pickup $result) => $result->id === $pickup_two->id));
        $this->assertFalse($results->contains(fn(Pickup $result) => $result->id === $delivery_one->id));
        $this->assertTrue($results->contains(fn(Pickup $result) => $result->id === $delivery_two->id));
    }

    #[Test]
    public function it_can_find_pickup_locations_and_delivery_zones_by_state(): void
    {
        $pickup_one = Pickup::factory()->create(['fulfillment_type' => Pickup::FULFILLMENT_TYPE_PICKUP]);
        $pickup_two = Pickup::factory()->create(['fulfillment_type' => Pickup::FULFILLMENT_TYPE_PICKUP]);
        $pickup_two->states()->create(['state' => 'NY']);

        $delivery_one = Pickup::factory()->create(['fulfillment_type' => Pickup::FULFILLMENT_TYPE_DELIVERY]);
        $delivery_two = Pickup::factory()->create(['fulfillment_type' => Pickup::FULFILLMENT_TYPE_DELIVERY]);
        $delivery_two->states()->create(['state' => 'NY']);

        $address = new GeocodedAddress(
            lat: 1.234,
            lng: 2.345,
            city: 'Anytown',
            state: 'NY',
            postalCode: '12345',
            country: 'US',
            accuracy: 1
        );

        $results = (new DeliveryMethodService)->find($address);

        $this->assertFalse($results->contains(fn(Pickup $result) => $result->id === $pickup_one->id));
        $this->assertTrue($results->contains(fn(Pickup $result) => $result->id === $pickup_two->id));
        $this->assertFalse($results->contains(fn(Pickup $result) => $result->id === $delivery_one->id));
        $this->assertTrue($results->contains(fn(Pickup $result) => $result->id === $delivery_two->id));
    }

    #[Test]
    public function it_can_find_pickup_locations_by_coordinates(): void
    {
        $pickup_one = Pickup::factory()->create(['lat' => -87.70, 'lng' => 41.92, 'fulfillment_type' => Pickup::FULFILLMENT_TYPE_PICKUP]);
        $pickup_two = Pickup::factory()->create(['lat' => 41.92, 'lng' => -87.70, 'fulfillment_type' => Pickup::FULFILLMENT_TYPE_PICKUP]);

        $delivery_one = Pickup::factory()->create(['lat' => -87.70, 'lng' => 41.92, 'fulfillment_type' => Pickup::FULFILLMENT_TYPE_DELIVERY]);
        $delivery_two = Pickup::factory()->create(['lat' => 41.92, 'lng' => -87.70, 'fulfillment_type' => Pickup::FULFILLMENT_TYPE_DELIVERY]);

        // lat and lng for 60647
        $address = new GeocodedAddress(
            lat: 41.92,
            lng: -87.70,
            city: 'Anytown',
            state: 'NY',
            postalCode: '12345',
            country: 'US',
            accuracy: 1
        );

        $results = (new DeliveryMethodService)->find($address);

        $this->assertFalse($results->contains(fn(Pickup $result) => $result->id === $pickup_one->id));
        $this->assertTrue($results->contains(fn(Pickup $result) => $result->id === $pickup_two->id));
        $this->assertFalse($results->contains(fn(Pickup $result) => $result->id === $delivery_one->id));
        // only pickup locations should be returned
        $this->assertFalse($results->contains(fn(Pickup $result) => $result->id === $delivery_two->id));
    }

    #[Test]
    public function it_can_filter_by_delivery_method_type(): void
    {
        $pickup = Pickup::factory()->create(['fulfillment_type' => Pickup::FULFILLMENT_TYPE_PICKUP]);
        $pickup->zips()->create(['zip' => '12345']);

        $delivery = Pickup::factory()->create(['fulfillment_type' => Pickup::FULFILLMENT_TYPE_DELIVERY]);
        $delivery->zips()->create(['zip' => '12345']);

        $address = new GeocodedAddress(
            lat: 1.234,
            lng: 2.345,
            city: 'Anytown',
            state: 'NY',
            postalCode: '12345',
            country: 'US',
            accuracy: 1
        );

        $results = (new DeliveryMethodService)->pickupLocations()->find($address);

        $this->assertTrue($results->contains(fn(Pickup $result) => $result->id === $pickup->id));
        $this->assertFalse($results->contains(fn(Pickup $result) => $result->id === $delivery->id));

        $results = (new DeliveryMethodService)->deliveryZones()->find($address);

        $this->assertFalse($results->contains(fn(Pickup $result) => $result->id === $pickup->id));
        $this->assertTrue($results->contains(fn(Pickup $result) => $result->id === $delivery->id));
    }

    #[Test]
    public function it_can_filter_by_products(): void
    {
        $product = Product::factory()->create();

        $pickup_one = Pickup::factory()->create(['fulfillment_type' => Pickup::FULFILLMENT_TYPE_PICKUP]);
        $pickup_one->zips()->create(['zip' => '12345']);
        $pickup_one->products()->attach($product);

        $pickup_two = Pickup::factory()->create(['fulfillment_type' => Pickup::FULFILLMENT_TYPE_PICKUP]);
        $pickup_two->zips()->create(['zip' => '12345']);

        $delivery_one = Pickup::factory()->create(['fulfillment_type' => Pickup::FULFILLMENT_TYPE_DELIVERY]);
        $delivery_one->zips()->create(['zip' => '12345']);
        $delivery_one->products()->attach($product);

        $delivery_two = Pickup::factory()->create(['fulfillment_type' => Pickup::FULFILLMENT_TYPE_DELIVERY]);
        $delivery_two->zips()->create(['zip' => '12345']);

        $address = new GeocodedAddress(
            lat: 1.234,
            lng: 2.345,
            city: 'Anytown',
            state: 'NY',
            postalCode: '12345',
            country: 'US',
            accuracy: 1
        );

        $results = (new DeliveryMethodService)
            ->hasProducts([$product->id])
            ->find($address);

        $this->assertFalse($results->contains(fn(Pickup $result) => $result->id === $pickup_one->id));
        $this->assertTrue($results->contains(fn(Pickup $result) => $result->id === $pickup_two->id));
        $this->assertFalse($results->contains(fn(Pickup $result) => $result->id === $delivery_one->id));
        $this->assertTrue($results->contains(fn(Pickup $result) => $result->id === $delivery_two->id));
    }
}
