<?php

namespace Tests\Unit\Services\FilterService\Filters;

use App\Services\FilterService\Filters\PaymentDate;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class PaymentDateTest extends TestCase
{
    use CreatesFakeRequest;

    #[Test]
    public function it_can_return_its_properties_for_one_date(): void
    {
        $collection = (new PaymentDate)->handle([$this->createRequest(['payment_date' => '2020-12-20']), collect()], function ($passable) {
            list($request, $collection) = $passable;
            return $collection;
        });

        $this->assertTrue($collection->has('payment_date'));

        /** @var PaymentDate $filter */
        $filter = $collection->get('payment_date');

        $this->assertEquals('Payment Date:', $filter->label());
        $this->assertEquals('2020-12-20', $filter->value());
        $this->assertEquals('http://test.test?foo=bar', $filter->removeFilterUrl());

    }

    #[Test]
    public function it_can_return_its_properties_for_an_array_of_dates(): void
    {
        $collection = (new PaymentDate)->handle([$this->createRequest(['payment_date' => ['2020-12-20', '2020-12-25']]), collect()], function ($passable) {
            list($request, $collection) = $passable;
            return $collection;
        });

        $this->assertTrue($collection->has('payment_date'));

        /** @var PaymentDate $filter */
        $filter = $collection->get('payment_date');

        $this->assertEquals('Payment Date:', $filter->label());
        $this->assertEquals('2020-12-20 - 2020-12-25', $filter->value());
        $this->assertEquals('http://test.test?foo=bar', $filter->removeFilterUrl());

    }
}