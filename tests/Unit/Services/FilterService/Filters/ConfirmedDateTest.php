<?php

namespace Tests\Unit\Services\FilterService\Filters;

use App\Services\FilterService\Filters\ConfirmedDate;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class ConfirmedDateTest extends TestCase
{
    use CreatesFakeRequest;

    #[Test]
    public function it_doesnt_return_when_empty(): void
    {
        $collection = (new ConfirmedDate)->handle([$this->createRequest(['confirmed_date' => []]), collect()], function ($passable) {
            list($request, $collection) = $passable;
            return $collection;
        });

        $this->assertFalse($collection->has('confirmed_date'));

        $collection = (new ConfirmedDate)->handle([$this->createRequest(['confirmed_date' => ['start' => '', 'end' => '']]), collect()], function ($passable) {
            list($request, $collection) = $passable;
            return $collection;
        });

        $this->assertFalse($collection->has('confirmed_date'));
    }

    #[Test]
    public function it_can_return_its_properties_for_one_date(): void
    {
        $collection = (new ConfirmedDate)->handle([$this->createRequest(['confirmed_date' => '2020-12-20']), collect()], function ($passable) {
            list($request, $collection) = $passable;
            return $collection;
        });

        $this->assertTrue($collection->has('confirmed_date'));

        /** @var ConfirmedDate $filter */
        $filter = $collection->get('confirmed_date');

        $this->assertEquals('Confirmed Date:', $filter->label());
        $this->assertEquals('2020-12-20', $filter->value());
        $this->assertEquals('http://test.test?foo=bar', $filter->removeFilterUrl());

    }

    #[Test]
    public function it_can_return_its_properties_for_an_array_of_dates(): void
    {
        $collection = (new ConfirmedDate)->handle([$this->createRequest(['confirmed_date' => ['2020-12-20', '2020-12-25']]), collect()], function ($passable) {
            list($request, $collection) = $passable;
            return $collection;
        });

        $this->assertTrue($collection->has('confirmed_date'));

        /** @var ConfirmedDate $filter */
        $filter = $collection->get('confirmed_date');

        $this->assertEquals('Confirmed Date:', $filter->label());
        $this->assertEquals('2020-12-20 - 2020-12-25', $filter->value());
        $this->assertEquals('http://test.test?foo=bar', $filter->removeFilterUrl());

    }
}