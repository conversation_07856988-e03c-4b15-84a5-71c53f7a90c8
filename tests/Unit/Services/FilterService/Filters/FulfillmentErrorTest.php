<?php

namespace Tests\Unit\Services\FilterService\Filters;

use App\Services\FilterService\Filters\FulfillmentError;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class FulfillmentErrorTest extends TestCase
{
    use CreatesFakeRequest;

    #[Test]
    public function it_can_return_its_properties_for_fulfillment_error(): void
    {
        $collection = (new FulfillmentError)->handle([$this->createRequest(['fulfillment_error' => 'true']), collect()], function ($passable) {
            list($request, $collection) = $passable;
            return $collection;
        });

        $this->assertTrue($collection->has('fulfillment_error'));

        /** @var FulfillmentError $filter */
        $filter = $collection->get('fulfillment_error');

        $this->assertEquals('Mispacked', $filter->label());
        $this->assertEquals('', $filter->value());
        $this->assertEquals('http://test.test?foo=bar', $filter->removeFilterUrl());

    }
}