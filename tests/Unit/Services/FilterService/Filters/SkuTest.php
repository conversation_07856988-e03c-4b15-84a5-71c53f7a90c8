<?php

namespace Tests\Unit\Services\FilterService\Filters;

use App\Services\FilterService\Filters\Sku;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class SkuTest extends TestCase
{
    use CreatesFakeRequest;

    #[Test]
    public function it_can_return_its_properties(): void
    {
        $collection = (new Sku)->handle([$this->createRequest(['sku' => '123']), collect()], function ($passable) {
            list($request, $collection) = $passable;
            return $collection;
        });

        $this->assertTrue($collection->has('sku'));

        /** @var Sku $filter */
        $filter = $collection->get('sku');

        $this->assertEquals('SKU:', $filter->label());
        $this->assertEquals('123', $filter->value());
        $this->assertEquals('http://test.test?foo=bar', $filter->removeFilterUrl());

    }
}