<?php

namespace Tests\Unit\Services\FilterService\Filters;

use App\Services\FilterService\Filters\Orders;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class OrdersTest extends TestCase
{
    use CreatesFakeRequest;

    #[Test]
    public function it_doesnt_return_when_empty(): void
    {
        $collection = (new Orders)->handle([$this->createRequest(['orders' => '']), collect()], function ($passable) {
            list($request, $collection) = $passable;
            return $collection;
        });

        $this->assertFalse($collection->has('orders'));
    }

    #[Test]
    public function it_can_return_its_properties(): void
    {
        $collection = (new Orders)->handle([$this->createRequest(['orders' => '123']), collect()], function ($passable) {
            list($request, $collection) = $passable;
            return $collection;
        });

        $this->assertTrue($collection->has('orders'));

        /** @var Orders $filter */
        $filter = $collection->get('orders');

        $this->assertEquals('Search:', $filter->label());
        $this->assertEquals('123', $filter->value());
        $this->assertEquals('http://test.test?foo=bar', $filter->removeFilterUrl());
    }
}