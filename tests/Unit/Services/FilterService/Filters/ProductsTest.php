<?php

namespace Tests\Unit\Services\FilterService\Filters;

use App\Services\FilterService\Filters\Products;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class ProductsTest extends TestCase
{
    use CreatesFakeRequest;

    #[Test]
    public function it_can_return_its_properties(): void
    {
        $collection = (new Products)->handle([$this->createRequest(['products' => '123']), collect()], function ($passable) {
            list($request, $collection) = $passable;
            return $collection;
        });

        $this->assertTrue($collection->has('products'));

        /** @var Products $filter */
        $filter = $collection->get('products');

        $this->assertEquals('Products:', $filter->label());
        $this->assertEquals('123', $filter->value());
        $this->assertEquals('http://test.test?foo=bar', $filter->removeFilterUrl());

    }
}