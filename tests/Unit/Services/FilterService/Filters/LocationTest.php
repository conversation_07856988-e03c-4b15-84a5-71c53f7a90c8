<?php

namespace Tests\Unit\Services\FilterService\Filters;

use App\Models\Pickup as PickupModel;
use App\Services\FilterService\Filters\Location;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class LocationTest extends TenantTestCase
{
    use CreatesFakeRequest;

    #[Test]
    public function it_can_return_its_properties_for_one_pickup(): void
    {
        $pickup = PickupModel::factory()->create();

        $collection = (new Location)->handle([$this->createRequest(['pickup_id' => $pickup->id]), collect()], function ($passable) {
            list($request, $collection) = $passable;
            return $collection;
        });

        $this->assertTrue($collection->has('pickup_id'));

        /** @var Location $filter */
        $filter = $collection->get('pickup_id');

        $this->assertEquals('Location:', $filter->label());
        $this->assertEquals($pickup->title, $filter->value());
        $this->assertEquals('http://test.test?foo=bar', $filter->removeFilterUrl());

    }

    #[Test]
    public function it_can_return_its_properties_for_an_array_of_pickups(): void
    {
        $pickups = PickupModel::factory()->times(2)->create();

        $collection = (new Location)->handle([$this->createRequest(['pickup_id' => $pickups->pluck('id')->toArray()]), collect()], function ($passable) {
            list($request, $collection) = $passable;
            return $collection;
        });

        $this->assertTrue($collection->has('pickup_id'));

        /** @var Location $filter */
        $filter = $collection->get('pickup_id');

        $this->assertEquals('Location:', $filter->label());
        $this->assertEquals($pickups->pluck('title')->implode(', '), $filter->value());
        $this->assertEquals('http://test.test?foo=bar', $filter->removeFilterUrl());

    }
}