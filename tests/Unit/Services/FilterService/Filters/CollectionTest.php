<?php

namespace Tests\Unit\Services\FilterService\Filters;

use App\Models\Collection as CollectionModel;
use App\Services\FilterService\Filters\Collection;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class CollectionTest extends TenantTestCase
{
    use CreatesFakeRequest;

    #[Test]
    public function it_can_return_its_properties_for_one_collection(): void
    {
        $collection = CollectionModel::factory()->create();

        $result = (new Collection)->handle([$this->createRequest(['collection_id' => $collection->id]), collect()], function ($passable) {
            list($request, $collection) = $passable;
            return $collection;
        });

        $this->assertTrue($result->has('collection_id'));

        /** @var Collection $filter */
        $filter = $result->get('collection_id');

        $this->assertEquals('Collection:', $filter->label());
        $this->assertEquals($collection->title, $filter->value());
        $this->assertEquals('http://test.test?foo=bar', $filter->removeFilterUrl());

    }

    #[Test]
    public function it_can_return_its_properties_for_an_array_of_statuses(): void
    {
        $collections = CollectionModel::factory()->times(2)->create();

        $result = (new Collection)->handle([$this->createRequest(['collection_id' => $collections->pluck('id')->toArray()]), collect()], function ($passable) {
            list($request, $collection) = $passable;
            return $collection;
        });

        $this->assertTrue($result->has('collection_id'));

        /** @var Collection $filter */
        $filter = $result->get('collection_id');

        $this->assertEquals('Collection:', $filter->label());
        $this->assertEquals($collections->pluck('title')->implode(', '), $filter->value());
        $this->assertEquals('http://test.test?foo=bar', $filter->removeFilterUrl());

    }
}