<?php

namespace Tests\Unit\Services;

use App\Services\PhoneNumberService;
use App\Services\Twilio;
use <PERSON><PERSON>y\MockInterface;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class PhoneNumberServiceTest extends TenantTestCase
{
    #[Test]
    public function it_can_format_a_number(): void
    {
        $this->mock(Twilio::class);

        $service = app(PhoneNumberService::class);

        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('The phone number could not be parsed.');

        $service->format('');

        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('The phone number is not valid.');

        $service->format('555-0146');

        $this->assertEquals('+12025550147', $service->format('************'));
        $this->assertEquals('+12025550147', $service->format('2025550147'));
        $this->assertEquals('+12025550147', $service->format('(202)5550147'));
        $this->assertEquals('+12025550147', $service->format('(*************'));
    }

    #[Test]
    public function it_can_lookup_a_valid_phone_number(): void
    {
        $this->mock(Twilio::class, function (MockInterface $mock) {
            $mock->shouldReceive('lookup')
                ->once()
                ->with('+12025550147')
                ->andReturn([
                    'valid' => true,
                    'line_type_intelligence' => [
                        'type' => 'mobile',
                    ],
                ]);
        });

        $service = app(PhoneNumberService::class);

        $this->assertEquals([
            'valid' => true,
            'type' => 'mobile',
        ], $service->lookup('************'));
    }

    #[Test]
    public function it_cannot_lookup_a_unparsable_phone_number(): void
    {
        $this->mock(Twilio::class, function (MockInterface $mock) {
            $mock->shouldNotReceive('lookup');
        });

        $service = app(PhoneNumberService::class);

        $this->assertEquals([
            'valid' => false,
            'type' => null,
        ], $service->lookup(''));
    }

    #[Test]
    public function it_cannot_lookup_an_invalid_phone_number(): void
    {
        $this->mock(Twilio::class, function (MockInterface $mock) {
            $mock->shouldNotReceive('lookup');
        });

        $service = app(PhoneNumberService::class);

        $this->assertEquals([
            'valid' => false,
            'type' => null,
        ], $service->lookup('555-0146'));
    }
}
