<?php

namespace Tests;

use App\Contracts\Geocoder;
use App\Models\Setting;
use App\Models\User;
use App\Services\Geocoding\FakeGeocoder;
use App\Services\Geocoding\Geocodio;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\TestCase as BaseTestCase;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Once;
use Laravel\Sanctum\Sanctum;

abstract class TestCase extends BaseTestCase
{
    use RefreshDatabase;

    protected $seed = true;
    
    public function actingAsApiAdmin()
    {
        return $this->actingAsAdmin('api');
    }

    public function actingAsAdmin($guard = null)
    {
        return $this->actingAs(User::factory()->admin()->create(), $guard);
    }

    public function actingAsApiOwner()
    {
        return $this->actingAsOwner('api');
    }

    public function actingAsOwner($guard = null)
    {
        return $this->actingAs(User::factory()->owner()->create(), $guard);
    }

    public function actingAsApiCustomer()
    {
        return $this->actingAsCustomer('api');
    }

    public function actingAsCustomer($guard = null)
    {
        return $this->actingAs(User::factory()->customer()->create(), $guard);
    }

    public function actingAsSanctumUser(User $user = null)
    {
        Sanctum::actingAs($user ?? User::factory()->create());
        return $this;
    }

    public function actingAsApiEditor()
    {
        return $this->actingAsEditor('api');
    }

    public function actingAsEditor($guard = null)
    {
        return $this->actingAs(User::factory()->editor()->create(), $guard);
    }

    protected function setUp(): void
    {
        parent::setUp();

        Http::preventStrayRequests();
        Mail::fake();
        Once::disable();

        $this->fakeGeocoder();
//        $this->seed([PaymentSeeder::class, MenuSeeder::class, PageSeeder::class, WidgetSeeder::class]);
    }

    private function fakeGeocoder()
    {
        $this->swap(Geocoder::class, new FakeGeocoder);
        $this->swap(Geocodio::class, new FakeGeocoder); // for tenant geocode of initial delivery method
    }

    protected function tearDown(): void
    {
        Setting::flushCache();
        parent::tearDown();
    }
}
