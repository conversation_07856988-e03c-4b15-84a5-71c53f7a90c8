<?php

namespace Tests\Helpers;

use App\Models\User;

trait LoginAdmin
{
    protected $adminUser = null;

    public function createAdmin($params = ['role_id' => 2])
    {
        $this->adminUser = User::factory()->create($params);
    }

    public function loginAsAdmin()
    {
        if (is_null($this->adminUser)) {
            $this->createAdmin();
        }

        $this->actingAs($this->adminUser);
    }

    public function loginAsCustomer()
    {
        if (is_null($this->adminUser)) {
            $this->createAdmin(['role_id' => 3]);
        }

        $this->actingAs($this->adminUser);
    }
}
