<?php

namespace Tests\Feature\API;

use App\Models\Pickup;
use App\Models\Product;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class LocationCartEligibilityTest extends TenantTestCase
{
    #[Test]
    public function it_returns_eligible_when_all_product_ids_are_available_at_a_location(): void
    {
        $location = Pickup::factory()->create();

        $products = Product::factory()->times(2)->create();

        $this->actingAsApiCustomer()
            ->postJson(route('api.locations.cart-eligibility', compact('location')), [
                'product_ids' => $products->map->id->toArray()
            ])
            ->assertOk()
            ->assertExactJson(['eligible' => true]);
    }

    #[Test]
    public function it_returns_ineligible_when_product_ids_are_not_available_at_a_location(): void
    {
        $location = Pickup::factory()->create();

        $eligible_products = Product::factory()->times(2)->create();
        $ineligible_products = Product::factory()->times(2)->create();
        $location->products()->saveMany($ineligible_products);

        $product_ids = $eligible_products->merge($ineligible_products)->map->id;

        $this->actingAsApiCustomer()
            ->postJson(route('api.locations.cart-eligibility', compact('location')), [
                'product_ids' => $product_ids->toArray()
            ])
            ->assertOk()
            ->assertExactJson([
                'eligible' => false,
                'ineligible_reason' => 'PRODUCT_ELIGIBILITY',
                'products' => $ineligible_products->map->title->toArray()
            ]);
    }
}