<?php

namespace Tests\Feature\API;

use App\Models\Pickup;
use App\Models\Schedule;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class PickupTest extends TenantTestCase
{
    #[Test]
    public function a_guest_cannot_fetch_pickups(): void
    {
        $this->getJson(route('api.pickups.index'))
            ->assertUnauthorized();
    }

    #[Test]
    public function a_non_admin_cannot_fetch_pickups(): void
    {
        $this->actingAsApiCustomer()
            ->getJson(route('api.pickups.index'))
            ->assertForbidden();
    }

    #[Test]
    public function an_admin_can_fetch_pickups_without_schedule_id(): void
    {
        $count = Pickup::whereNull('schedule_id')
            ->orWhere('schedule_id', 0)
            ->count();

        Pickup::factory()->times(2)->create(['schedule_id' => null]);
        Pickup::factory()->times(2)->create(['schedule_id' => 0]);

        $this->actingAsApiAdmin()
            ->getJson(route('api.pickups.index'))
            ->assertOk()
            ->assertJsonCount($count + 4);
    }

    #[Test]
    public function an_admin_can_fetch_pickups_by_schedule_id(): void
    {
        $schedule = Schedule::factory()->create();

        Pickup::factory()->times(2)->create(['schedule_id' => null]);
        Pickup::factory()->times(2)->create(['schedule_id' => $schedule->id]);

        $this->actingAsApiAdmin()
            ->getJson(route('api.pickups.index', ['schedule_id' => $schedule->id]))
            ->assertOk()
            ->assertJsonCount(2);
    }

    #[Test]
    public function it_sorts_pickups_by_title(): void
    {
        $count = Pickup::whereNull('schedule_id')
            ->orWhere('schedule_id', 0)
            ->count();

        $one = Pickup::factory()->create(['title' => 'aaaab', 'schedule_id' => null]);
        $two = Pickup::factory()->create(['title' => 'aaaaa', 'schedule_id' => null]);

        $response = $this->actingAsApiAdmin()
            ->getJson(route('api.pickups.index'))
            ->assertOk()
            ->assertJsonCount($count + 2)
            ->json();

        $this->assertEquals($two->title, $response[0]['title']);
        $this->assertEquals($one->title, $response[1]['title']);
    }

    #[Test]
    public function a_guest_cannot_fetch_a_single_pickup(): void
    {
        $pickup = Pickup::factory()->create();
        $this->getJson(route('api.pickups.show', compact('pickup')))
            ->assertUnauthorized();
    }

    #[Test]
    public function a_non_admin_cannot_fetch_a_single_pickup(): void
    {
        $pickup = Pickup::factory()->create();

        $this->actingAsApiCustomer()
            ->getJson(route('api.pickups.show', compact('pickup')))
            ->assertForbidden();
    }

    #[Test]
    public function a_admin_cannot_fetch_an_invalid_pickup(): void
    {
        $this->actingAsApiAdmin()
            ->getJson(route('api.pickups.show', [29384723]))
            ->assertNotFound();
    }

    #[Test]
    public function a_admin_can_fetch_a_single_pickup(): void
    {
        $pickup = Pickup::factory()->create();

        $this->actingAsApiAdmin()
            ->getJson(route('api.pickups.show', compact('pickup')))
            ->assertOk()
            ->assertJsonFragment(['id' => $pickup->id]);
    }
}