<?php

namespace Tests\Feature\API;

use App\Actions\Billing\ProcessOrderPayment;
use App\Contracts\Billing;
use App\Events\Order\OrderWasPaid;
use App\Exceptions\OrderChargeException;
use App\Models\Card;
use App\Models\Order;
use App\Models\OrderPayment;
use App\Models\Pickup;
use App\Support\Enums\OrderStatus;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Event;
use Mockery\MockInterface;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class PickupManagerTest extends TenantTestCase
{
    #[Test]
    public function a_guest_cannot_fetch_a_pickups_orders(): void
    {
        $pickup = Pickup::factory()->create();

        $this->getJson(route('api.pickup-manager.orders.index', compact('pickup')))
            ->assertUnauthorized();
    }

    #[Test]
    public function a_non_admin_cannot_fetch_a_pickups_orders(): void
    {
        $pickup = Pickup::factory()->create();

        $this->actingAsApiCustomer()
            ->getJson(route('api.pickup-manager.orders.index', compact('pickup')))
            ->assertForbidden();
    }

    #[Test]
    public function a_admin_cannot_fetch_an_invalid_pickups_orders(): void
    {
        $this->actingAsApiAdmin()
            ->getJson(route('api.pickup-manager.orders.index', [29384723]))
            ->assertNotFound();
    }

    #[Test]
    public function a_admin_can_fetch_a_pickups_orders(): void
    {
        $pickup = Pickup::factory()->create();

        Order::factory()->times(2)->create([
            'pickup_id' => $pickup->id,
            'confirmed' => true,
            'canceled' => false,
            'status_id' => OrderStatus::packed()
        ]);
        Order::factory()->times(2)->create([
            'confirmed' => true,
            'canceled' => false,
            'status_id' => OrderStatus::packed()
        ]);

        $this->actingAsApiAdmin()
            ->getJson(route('api.pickup-manager.orders.index', compact('pickup')))
            ->assertOk()
            ->assertJsonCount(2)
            ->assertJsonStructure([
                '*' => [
                    'id', 'items', 'fees', 'customer', 'discounts'
                ]
            ]);
    }

    #[Test]
    public function it_sorts_pickup_orders_by_customer_last_name(): void
    {
        $pickup = Pickup::factory()->create();

        $one = Order::factory()->create([
            'pickup_id' => $pickup->id,
            'confirmed' => true,
            'canceled' => false,
            'status_id' => OrderStatus::packed(),
            'customer_last_name' => 'xyz'
        ]);

        $two = Order::factory()->create([
            'pickup_id' => $pickup->id,
            'confirmed' => true,
            'canceled' => false,
            'status_id' => OrderStatus::packed(),
            'customer_last_name' => 'abc'
        ]);

        $response = $this->actingAsApiAdmin()
            ->getJson(route('api.pickup-manager.orders.index', compact('pickup')))
            ->assertOk()
            ->assertJsonCount(2)
            ->json();

        $this->assertEquals($two->id, $response[0]['id']);
        $this->assertEquals($one->id, $response[1]['id']);
    }

    #[Test]
    public function it_filters_orders_index(): void
    {
        $pickup = Pickup::factory()->create();

        Order::factory()->create([
            'pickup_id' => $pickup->id,
            'confirmed' => true,
            'canceled' => false,
            'status_id' => OrderStatus::packed()
        ]);

        Order::factory()->create([
            'pickup_id' => $pickup->id,
            'confirmed' => true,
            'canceled' => false,
            'status_id' => OrderStatus::pickedUp()
        ]);

        Order::factory()->create([
            'pickup_id' => $pickup->id,
            'confirmed' => true,
            'canceled' => true, // filtered
            'status_id' => OrderStatus::packed()
        ]);

        Order::factory()->create([
            'pickup_id' => $pickup->id,
            'confirmed' => false, // filtered
            'canceled' => false,
            'status_id' => OrderStatus::packed()
        ]);

        Order::factory()->create([
            'pickup_id' => $pickup->id,
            'confirmed' => true,
            'canceled' => false,
            'status_id' => OrderStatus::confirmed() // filtered
        ]);

        $this->actingAsApiAdmin()
            ->getJson(route('api.pickup-manager.orders.index', compact('pickup')))
            ->assertOk()
            ->assertJsonCount(2);
    }

    #[Test]
    public function a_guest_cannot_fetch_charge_an_order(): void
    {
        $order = Order::factory()->create();

        $this->postJson(route('api.orders.charge-customer.store', compact('order')))
            ->assertUnauthorized();
    }

    #[Test]
    public function a_non_admin_cannot_fetch_charge_an_order(): void
    {
        $order = Order::factory()->create();

        $this->actingAsApiCustomer()
            ->postJson(route('api.orders.charge-customer.store', compact('order')))
            ->assertForbidden();
    }

    #[Test]
    public function a_admin_cannot_charge_an_invalid_order(): void
    {
        $this->actingAsApiAdmin()
            ->postJson(route('api.orders.charge-customer.store', [29384723]))
            ->assertNotFound();
    }

    #[Test]
    public function an_admin_cannot_charge_a_paid_order(): void
    {
        $order = Order::factory()->create(['paid' => true]);

        $this->actingAsApiAdmin()
            ->postJson(route('api.orders.charge-customer.store', compact('order')))
            ->assertStatus(409);
    }

    #[Test]
    public function an_admin_can_charge_an_order(): void
    {
        Carbon::setTestNow(now());

        $order = Order::factory()->create(['paid' => false, 'confirmed' => true]);

        $this->mock(ProcessOrderPayment::class, function (MockInterface $mock) use ($order) {
            $mock->shouldReceive('handle')
                ->once()
                ->with(\Mockery::on(fn (Order $arg) => $arg->id === $order->id), null)
                ->andReturn(OrderPayment::factory()->create());
        });

        $this->actingAsApiAdmin()
            ->postJson(route('api.orders.charge-customer.store', compact('order')))
            ->assertStatus(200)
            ->assertJsonFragment(['responseText' => 'Order charged to saved card.']);

        Carbon::setTestNow();
    }

    #[Test]
    public function an_admin_can_charge_an_order_with_a_valid_payment_source(): void
    {
        Carbon::setTestNow(now());

        Event::fake([OrderWasPaid::class]);

        $order = Order::factory()->create(['paid' => false, 'confirmed' => true]);
        $card = Card::factory()->create(['source_id' => 'something']);

        $this->mock(ProcessOrderPayment::class, function (MockInterface $mock) use ($order, $card) {
            $mock->shouldReceive('handle')
                ->once()
                ->with(
                    \Mockery::on(fn (Order $arg) => $arg->id === $order->id),
                    \Mockery::on(fn (Card $arg) => $arg->id === $card->id),
                )
                ->andReturn(OrderPayment::factory()->create());
        });

        $payment_source_id = $card->id;

        $this->actingAsApiAdmin()
            ->postJson(route('api.orders.charge-customer.store', compact('order', 'payment_source_id')))
            ->assertStatus(200)
            ->assertJsonFragment(['responseText' => 'Order charged to saved card.']);

        Carbon::setTestNow();
    }

    #[Test]
    public function an_admin_cannot_charge_an_order_with_an_invalid_payment_source(): void
    {
        $order = Order::factory()->create(['paid' => false, 'confirmed' => true]);

        $this->mock(Billing::class, function (MockInterface $mock) use ($order) {
            $mock->shouldNotReceive('chargeOrder');
        });

        $payment_source_id = 123123123;

        $this->actingAsApiAdmin()
            ->postJson(route('api.orders.charge-customer.store', compact('order', 'payment_source_id')))
            ->assertUnprocessable()
            ->assertInvalid(['payment_source_id' => 'The selected payment source id is invalid.']);
    }

    #[Test]
    public function it_returns_an_error_when_payment_fails(): void
    {
        $order = Order::factory()->create(['paid' => false, 'confirmed' => true]);

        $this->mock(ProcessOrderPayment::class, function (MockInterface $mock) use ($order) {
            $mock->shouldReceive('handle')
                ->once()
                ->with(
                    \Mockery::on(fn (Order $arg) => $arg->id === $order->id),
                    null
                )
                ->andThrow(new OrderChargeException('some error'));
        });

        $this->actingAsApiAdmin()
            ->postJson(route('api.orders.charge-customer.store', compact('order')))
            ->assertStatus(400)
            ->assertJsonFragment(['responseText' => 'some error']);
    }
}
