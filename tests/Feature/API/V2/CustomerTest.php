<?php

namespace Tests\Feature\API\V2;

use App\Models\User;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class CustomerTest extends TenantTestCase
{
    #[Test]
    public function unauthorized_requests_cannot_fetch_customer_list(): void
    {
        $this->getJson(route('api.v2.customers.index'))
            ->assertUnauthorized();
    }

    #[Test]
    public function it_successfully_returns_customer_list(): void
    {
        $current_count = User::count();

        User::factory(2)->create();

        $user = User::factory()->create();

        $this->actingAsSanctumUser($user)
            ->getJson(route('api.v2.customers.index'))
            ->assertOk()
            ->assertJsonCount(min($current_count + 3, 10),'data');
    }

    #[Test]
    public function it_successfully_returns_a_specific_customer(): void
    {
        $customer = User::factory()->create();

        $this->actingAsSanctumUser()
            ->getJson(route('api.v2.customers.show', compact('customer')))
            ->assertOk()
            ->assertJsonStructure([
                'data' => [
                    'id',
                    'email',
                ],
            ])
            ->assertJsonFragment([
                'id' => $customer->id,
                'email' => $customer->email,
            ]);
    }
}
