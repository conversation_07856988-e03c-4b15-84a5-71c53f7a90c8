<?php

namespace Tests\Feature\Admin;

use App\Models\Setting;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class SettingsCustomerTest extends TenantTestCase
{
    #[Test]
    public function a_guest_cannot_update_customer_settings(): void
    {
        $this->put(route('admin.settings.customer.update'))
            ->assertRedirect(route('admin.login'));
    }

    #[Test]
    public function a_non_admin_cannot_update_customer_settings(): void
    {
        $this->actingAsCustomer()
            ->put(route('admin.settings.customer.update'))
            ->assertRedirect(route('admin.login'));
    }

    #[Test]
    public function it_validates_the_update_customer_settings_request(): void
    {
        $this->actingAsAdmin()
            ->get(route('admin.settings.edit', ['setting' => 'customers']))
            ->assertOk();

        $this->put(route('admin.settings.customer.update'))
            ->assertRedirect(route('admin.settings.edit', ['setting' => 'customers']))
            ->assertSessionHasErrors([
                'registration_wall' => 'The registration wall field is required.',
                'customer_location_wall' => 'The customer location wall field is required.',
                'enable_referrals' => 'The enable referrals field is required.',
                'require_user_activation' => 'The require user activation field is required.',
                'user_export_format' => 'The user export format field is required.',
            ]);

        $this->put(route('admin.settings.customer.update'), [
            'registration_wall' => 'checkout',
            'customer_location_wall' => 'off',
        ])
            ->assertRedirect(route('admin.settings.edit', ['setting' => 'customers']))
            ->assertSessionHasErrors([
                'customer_location_wall' => 'The geofencing setting must be enabled when authenticating at checkout.',
            ]);

        $this->put(route('admin.settings.customer.update'), [
            'user_registration_credit' => -1,
            'pickup_results_count' => 0,
            'pickup_results_radius' => 0,
        ])
            ->assertRedirect(route('admin.settings.edit', ['setting' => 'customers']))
            ->assertSessionHasErrors([
                'user_registration_credit' => 'The user registration credit field must be at least 0.',
                'pickup_results_count' => 'The pickup results count field must be at least 1.',
                'pickup_results_radius' => 'The pickup results radius field must be at least 1.',
            ]);

        $this->put(route('admin.settings.customer.update'), [
            'user_registration_redirect' => 0,
            'user_login_redirect' => 0,
            'lead_capture_url' => 0,
        ])
            ->assertRedirect(route('admin.settings.edit', ['setting' => 'customers']))
            ->assertSessionHasErrors([
                'user_registration_redirect' => 'The user registration redirect field must be a string.',
                'user_login_redirect' => 'The user login redirect field must be a string.',
                'lead_capture_url' => 'The lead capture url field must be a string.',
            ]);

        $this->put(route('admin.settings.customer.update'), [
            'enable_referrals' =>'a',
            'require_user_activation' => 'a',
        ])
            ->assertRedirect(route('admin.settings.edit', ['setting' => 'customers']))
            ->assertSessionHasErrors([
                'enable_referrals' => 'The enable referrals field must be true or false.',
                'require_user_activation' => 'The require user activation field must be true or false.',
            ]);

        $this->put(route('admin.settings.customer.update'), [
            'user_export_format' => 'a',
        ])
            ->assertRedirect(route('admin.settings.edit', ['setting' => 'customers']))
            ->assertSessionHasErrors([
                'user_export_format' => 'The selected user export format is invalid.',
            ]);
    }

    #[Test]
    public function customer_settings_can_be_updated(): void
    {
        $this->actingAsAdmin()
            ->get(route('admin.settings.edit', ['setting' => 'customers']))
            ->assertOk();

        $settings = [
            'registration_wall' => 'site',
            'customer_location_wall' => 'store',
            'user_registration_credit' => '10.00',
            'user_registration_redirect' => '/register/redirect',
            'user_login_redirect' =>  '/login/redirect',
            'lead_capture_url' =>  '/lead/redirect',
            'pickup_results_count' => 2,
            'pickup_results_radius' => 50,
            'enable_referrals' => 1,
            'referral_bonus' => '11.00',
            'referral_payout' => '12.00',
            'require_user_activation' => 1,
            'user_export_format' => 'drip',
            'newsletter_auto_opt_in' => 1,
        ];

        $this->put(route('admin.settings.customer.update'), $settings)
            ->assertRedirect(route('admin.settings.edit', ['setting' => 'customers']))
            ->assertSessionDoesntHaveErrors();

        foreach ($settings as $key => $value) {
            if (in_array($key, ['referral_bonus', 'referral_payout'])) {
                $value = formatCurrencyForDB($value);
            }

            $this->assertDatabaseHas(Setting::class, compact('key', 'value'));
        }
    }

    #[Test]
    public function customer_settings_can_be_updated_via_ajax(): void
    {
        $settings = [
            'registration_wall' => 'site',
            'customer_location_wall' => 'store',
            'user_registration_credit' => '10.00',
            'user_registration_redirect' => '/register/redirect',
            'user_login_redirect' =>  '/login/redirect',
            'lead_capture_url' =>  '/lead/redirect',
            'pickup_results_count' => 2,
            'pickup_results_radius' => 50,
            'enable_referrals' => 1,
            'referral_bonus' => '11.00',
            'referral_payout' => '12.00',
            'require_user_activation' => 1,
            'user_export_format' => 'drip',
            'newsletter_auto_opt_in' => 1,
        ];

        $this->actingAsApiAdmin()
            ->putJson(route('admin.settings.customer.update'), $settings)
            ->assertOk();

        foreach ($settings as $key => $value) {
            if (in_array($key, ['referral_bonus', 'referral_payout'])) {
                $value = formatCurrencyForDB($value);
            }

            $this->assertDatabaseHas(Setting::class, compact('key', 'value'));
        }
    }

    #[Test]
    public function customer_newsletter_settings_can_be_updated_correctly(): void
    {
        $this->actingAsAdmin()
            ->get(route('admin.settings.edit', ['setting' => 'customers']))
            ->assertOk();

        $settings = [
            'registration_wall' => 'site',
            'customer_location_wall' => 'store',
            'user_registration_credit' => '10.00',
            'user_registration_redirect' => '/register/redirect',
            'user_login_redirect' =>  '/login/redirect',
            'lead_capture_url' =>  '/lead/redirect',
            'pickup_results_count' => 2,
            'pickup_results_radius' => 50,
            'enable_referrals' => 1,
            'referral_bonus' => '11.00',
            'referral_payout' => '12.00',
            'require_user_activation' => 1,
            'user_export_format' => 'drip',
        ];

        Setting::updateOrCreate(['key' => 'newsletter_auto_opt_in'], ['value' => false]);

        $newsletter_settings = ['newsletter_auto_opt_in' => 1];

        $this->put(route('admin.settings.customer.update'), array_merge($settings, $newsletter_settings))
            ->assertRedirect(route('admin.settings.edit', ['setting' => 'customers']))
            ->assertSessionDoesntHaveErrors();

        $this->assertEquals(1, setting('newsletter_auto_opt_in'));

        Setting::updateOrCreate(['key' => 'newsletter_auto_opt_in'], ['value' => true]);

        $newsletter_settings = ['newsletter_auto_opt_in' => 0];

        $this->put(route('admin.settings.customer.update'), array_merge($settings, $newsletter_settings))
            ->assertRedirect(route('admin.settings.edit', ['setting' => 'customers']))
            ->assertSessionDoesntHaveErrors();

        $this->assertEquals(0, setting('newsletter_auto_opt_in'));
    }


    #[Test]
    public function customer_newsletter_settings_can_be_updated_correctly_via_ajax(): void
    {
        $settings = [
            'registration_wall' => 'site',
            'customer_location_wall' => 'store',
            'user_registration_credit' => '10.00',
            'user_registration_redirect' => '/register/redirect',
            'user_login_redirect' =>  '/login/redirect',
            'lead_capture_url' =>  '/lead/redirect',
            'pickup_results_count' => 2,
            'pickup_results_radius' => 50,
            'enable_referrals' => 1,
            'referral_bonus' => '11.00',
            'referral_payout' => '12.00',
            'require_user_activation' => 1,
            'user_export_format' => 'drip',
        ];

        Setting::updateOrCreate(['key' => 'newsletter_auto_opt_in'], ['value' => false]);

        $newsletter_settings = ['newsletter_auto_opt_in' => 1];

        $this->actingAsApiAdmin()
            ->putJson(route('admin.settings.customer.update'), array_merge($settings, $newsletter_settings))
            ->assertOk();

        $this->assertEquals(1, setting('newsletter_auto_opt_in'));

        Setting::updateOrCreate(['key' => 'newsletter_auto_opt_in'], ['value' => true]);

        $newsletter_settings = ['newsletter_auto_opt_in' => 0];

        $this->actingAsApiAdmin()
            ->putJson(route('admin.settings.customer.update'), array_merge($settings, $newsletter_settings))
            ->assertOk();

        $this->assertEquals(0, setting('newsletter_auto_opt_in'));
    }
}
