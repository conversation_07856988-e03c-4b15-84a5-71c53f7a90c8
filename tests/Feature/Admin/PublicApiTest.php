<?php

namespace Tests\Feature\Admin;

use App\Models\ApiKey;
use App\Models\Order;
use App\Models\User;
use App\Support\Enums\OrderStatus;
use App\Support\Enums\UserRole;
use Tests\TenantTestCase;

class PublicApiTest extends TenantTestCase
{
    public function setup(): void
    {
        parent::setup();

        Order::query()->delete();
    }

    public function test_an_owner_or_admin_can_add_an_api_key(): void
    {
        $user = User::factory()->create([
            'role_id' => UserRole::admin()
        ]);

        $payload = [
            'name' => 'My cool API key',
            'description' => 'For pulling order for the shipping integration.'
        ];

        $this->actingAs($user)->post('/admin/api-keys', $payload);

        $this->assertDatabaseHas('api_keys', [
            'created_by_id' => $user->id,
            'name' => 'My cool API key',
            'description' => 'For pulling order for the shipping integration.'
        ]);

        $key = ApiKey::where('name', 'My cool API key')->first();
        $this->assertTrue(strlen($key->prefix) == 5);
        $this->assertNotNull($key->key);
    }

    public function test_an_owner_or_admin_can_set_the_scope_of_a_key(): void
    {
        $user = User::factory()->create([
            'role_id' => UserRole::admin()
        ]);

        $apiKey = ApiKey::factory()->create([
            'created_by_id' => $user->id
        ]);

        $payload = [
            'name' => $apiKey->name,
            'scope' => [
                'orders:index' => true,
                'orders:create' => false,
                'inventory:update' => true
            ]
        ];

        $this->actingAs($user)->put('/admin/api-keys/' . $apiKey->id, $payload);

        $apiKey->refresh();

        $this->assertTrue($apiKey->hasScope('orders:index'));
        $this->assertFalse($apiKey->hasScope('orders:create'));
        $this->assertFalse($apiKey->hasScope('orders:show'));
        $this->assertTrue($apiKey->hasScope('inventory:update'));

        $payload = [
            'name' => $apiKey->name,
            'scope' => [
                'orders:index' => false,
                'orders:create' => true,
                'inventory:update' => false
            ]
        ];

        $this->actingAs($user)->put('/admin/api-keys/' . $apiKey->id, $payload);

        $apiKey->refresh();

        $this->assertFalse($apiKey->hasScope('orders:index'));
        $this->assertTrue($apiKey->hasScope('orders:create'));
        $this->assertFalse($apiKey->hasScope('orders:show'));
        $this->assertFalse($apiKey->hasScope('inventory:update'));
    }

    public function test_an_owner_or_admin_can_revoke_an_api_key(): void
    {
        $user = User::factory()->create([
            'role_id' => UserRole::admin()
        ]);

        $apiKey = ApiKey::factory()->create([
            'created_by_id' => $user->id
        ]);

        $this->actingAs($user)->delete('/admin/api-keys/' . $apiKey->id);

        $this->assertSoftDeleted('api_keys', [
            'id' => $apiKey->id
        ]);
    }

    public function test_api_consumer_can_get_list_of_orders(): void
    {
        $limit = 100;

        $orders = Order::factory()->count(10)->create([
            'confirmed' => true,
            'status_id' => OrderStatus::preOrder(),
            'subtotal' => rand(1, 500)
        ]);

        $unconfirmedOrders = Order::factory()->count(5)->create([
            'confirmed' => false,
            'status_id' => OrderStatus::confirmed(),
            'subtotal' => rand(1, 500)
        ]);

        $packedOrders = Order::factory()->count(3)->create([
            'confirmed' => true,
            'status_id' => OrderStatus::packed(),
            'subtotal' => rand(1, 500)
        ]);

        $apiKey = ApiKey::factory()->create([
            'scope' => [
                'orders:index' => true
            ]
        ])->generateKey();

        // Get only unconfirmed orders
        $this->json('GET', '/api/v1/orders', [
            'limit' => $limit,
            'status' => ['new', 'pre_order'],
            'confirmed' => false
        ], [
            'Authorization' => 'Bearer ' . $apiKey,
            'Accept' => 'application/json'
        ])
            ->assertOk()
            ->assertJsonCount(5, 'data');

        // Get only confirmed orders.
       $this->json('GET', '/api/v1/orders', [
            'limit' => $limit,
            'status' => ['new', 'pre_order'],
            'confirmed' => true
        ], [
            'Authorization' => 'Bearer ' . $apiKey,
            'Accept' => 'application/json'
        ])
            ->assertOk()
            ->assertJsonCount(count($orders), 'data');

        // Get only packed orders.
        $this->json('GET', '/api/v1/orders', [
            'limit' => $limit,
            'status' => ['packed'],
            'confirmed' => true
        ], [
            'Authorization' => 'Bearer ' . $apiKey,
            'Accept' => 'application/json'
        ])
            ->assertOk()
            ->assertJsonCount(count($packedOrders), 'data');
    }

    public function test_api_consumer_can_get_individual_order(): void
    {
        $order = Order::factory()->create([
            'confirmed' => true,
            'status_id' => OrderStatus::packed(),
            'weight' => 7.3456,
            'original_weight' => 7.4453
        ]);

        $order->refresh();

        $apiKey = ApiKey::factory()->create([
            'scope' => [
                'orders:show' => true
            ]
        ])->generateKey();

        $response = $this->json('GET', "/api/v1/orders/{$order->id}", [], [
            'Authorization' => 'Bearer ' . $apiKey,
            'Accept' => 'application/json'
        ]);

        $response->assertOk();
        $response->assertJson([
            'data' => [
                'id' => $order->id,
                'tracking_id' => $order->tracking_id,
                'type_id' => $order->type_id,
                'order_number' => $order->order_number,
                'customer_id' => $order->customer_id,
                'staff_id' => $order->staff_id,
                'accounting_id' => $order->accounting_id,
                'total' => $order->total,
                'payments_subtotal' => $order->payments_subtotal,
                'original_total' => $order->original_total,
                'subtotal' => $order->subtotal,
                'original_subtotal' => $order->original_subtotal,
                'tax' => $order->tax,
                'original_tax' => $order->original_tax,
                'tax_rate' => $order->tax_rate,
                'weight_in_pounds' => (float) $order->weight,
                'weight_in_ounces' => round(($order->weight * 16), 2),
                'original_weight_in_pounds' => (float) $order->original_weight,
                'order_discount' => $order->order_discount,
                'coupon_subtotal' => $order->coupon_subtotal,
                'discount_type' => $order->discount_type,
                'fees_subtotal' => $order->fees_subtotal,
                'original_fees_subtotal' => $order->original_fees_subtotal,
                'credit_applied' => $order->credit_applied,
                'delivery_rate' => $order->delivery_rate,
                'delivery_fee_type' => $order->delivery_fee_type,
                'delivery_fee' => $order->delivery_fee,
                'schedule_id' => $order->schedule_id,
                'pickup_id' => $order->pickup_id,
                'status_id' => $order->status_id,
                'deadline_date' => $order->deadline_date->format('Y-m-d H:i:s'),
                'pickup_date' => $order->pickup_date->format('Y-m-d H:i:s'),
                'original_pickup_date' => $order->original_pickup_date,
                'customer_first_name' => $order->customer_first_name,
                'customer_last_name' => $order->customer_last_name,
                'customer_email' => $order->customer_email,
                'customer_email_alt' => $order->customer_email_alt,
                'customer_phone' => $order->customer_phone,
                'customer_phone_2' => $order->customer_phone_2,
                'shipping_street' => $order->shipping_street,
                'shipping_street_2' => $order->shipping_street_2,
                'shipping_city' => $order->shipping_city,
                'shipping_state' => $order->shipping_state,
                'shipping_zip' => $order->shipping_zip,
                'shipping_country' => $order->shipping_country,
                'billing_street' => $order->billing_street,
                'billing_street_2' => $order->billing_street_2,
                'billing_city' => $order->billing_city,
                'billing_state' => $order->billing_state,
                'billing_zip' => $order->billing_zip,
                'billing_country' => $order->billing_country,
                'customer_notes' => $order->customer_notes,
                'packing_notes' => $order->packing_notes,
                'invoice_notes' => $order->invoice_notes,
                'payment_notes' => $order->payment_notes,
                'flagged' => $order->flagged,
                'fulfillment_error' => $order->fulfillment_error,
                'packed' => $order->packed,
                'canceled' => $order->canceled,
                'confirmed' => $order->confirmed,
                'confirmed_date' => $order->confirmed_date,
                'due_date' => $order->due_date,
                'payment_terms' => $order->payment_terms,
                'processed' => $order->processed,
                'processed_date' => $order->processed_date,
                'payment_id' => $order->payment_id,
                'payment_source_id' => $order->payment_source_id,
                'paid' => $order->paid,
                'payment_date' => $order->payment_date,
                'containers' => $order->containers,
                'containers_2' => $order->containers_2,
                'exported' => $order->exported,
                'picked_up' => $order->picked_up,
                'last_modified_by' => $order->last_modified_by,
                'first_time_order' => $order->irst_time_order,
                'created_at' => $order->created_at->toIso8601String(),
                'updated_at' => $order->updated_at->toIso8601String(),
            ]
        ]);
    }

    public function test_api_consumer_can_must_have_scope_to_see_individual_order(): void
    {
        $order = Order::factory()->create([
            'confirmed' => true,
            'status_id' => OrderStatus::packed()
        ]);

        $order->refresh();

        $apiKey = ApiKey::factory()->create([
            'scope' => [
                'orders:show' => false
            ]
        ])->generateKey();

        $response = $this->json('GET', "/api/v1/orders/{$order->id}", [], [
            'Authorization' => 'Bearer ' . $apiKey,
            'Accept' => 'application/json'
        ]);

        $response->assertJson([
            'error' => 'You do not have permission to view this order.'
        ]);
        $response->assertForbidden();
    }
}
