<?php

namespace Tests\Feature\Admin;

use App\Models\Cart;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Pickup;
use App\Models\Schedule;
use App\Models\Setting;
use App\Models\User;
use Illuminate\Support\Str;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class ReorderTest extends TenantTestCase
{
    #[Test]
    public function a_guest_cannot_reorder_an_order(): void
    {
        $order = Order::factory()->create();

        $this->post(route('customer.orders.reorder', compact('order')))
            ->assertRedirect(route('login'));
    }

    #[Test]
    public function a_user_cannot_reorder_an_unknown_order(): void
    {
        $this->actingAsCustomer()
            ->get(route('store.index'))
            ->assertOk();

        $this->post(route('customer.orders.reorder', [ 'order' => 0]))
            ->assertRedirect(route('store.index'));
    }

    #[Test]
    public function a_user_cannot_reorder_another_users_order(): void
    {
        $order = Order::factory()->create();

        $this->actingAsCustomer()
            ->get(route('store.index'))
            ->assertOk();

        $this->post(route('customer.orders.reorder', compact('order')))
            ->assertRedirect(route('store.index'));
    }

    #[Test]
    public function a_user_can_reorder_one_of_their_orders(): void
    {
        $pickup = Pickup::factory()->create(['schedule_id' => Schedule::factory()->hasDate()]);
        $user = User::factory()->create(['pickup_point' => $pickup->id]);

        /** @var Order $order */
        $order = Order::factory()->create(['customer_id' => $user->id, 'pickup_id' => $pickup->id, 'pickup_date' => today()->subWeek()->format('Y-m-d')]);
        $items = OrderItem::factory()->count(2)->create(['order_id' => $order->id]);

        $this->actingAs($order->customer)
            ->get(route('store.index'))
            ->assertOk();

        $this->assertDatabaseMissing(Cart::class, [
            'shopper_id' => $user->id,
            'shopper_type' => User::class
        ]);

        $this->post(route('customer.orders.reorder', compact('order')))
            ->assertRedirect(route('cart.show'))
            ->assertSessionHas('flash_notification', function ($message) use ($items) {
                return is_array($message)
                    && isset($message['level'])
                    && $message['level'] === 'info'
                    && isset($message['message'])
                    && Str::contains($message['message'], $items[0]->title)
                    && Str::contains($message['message'], $items[1]->title);
            });

        $this->assertDatabaseHas(Cart::class, [
            'shopper_id' => $user->id,
            'shopper_type' => User::class
        ]);
    }

    protected function setUp(): void
    {
        parent::setUp();
        Setting::updateOrCreate(['key' => 'one_page_checkout'], ['value' => 0]);
        Setting::updateOrCreate(['key' => 'cart_service'], ['value' => 'order']);
    }
}
