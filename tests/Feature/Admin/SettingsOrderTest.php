<?php

namespace Tests\Feature\Admin;

use App\Models\Setting;
use Illuminate\Support\Arr;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class SettingsOrderTest extends TenantTestCase
{
    #[Test]
    public function a_guest_cannot_access_orders_settings(): void
    {
        $this->get(route('admin.settings.edit', ['setting' => 'orders']))
            ->assertRedirect(route('admin.login'));
    }

    #[Test]
    public function a_guest_cannot_update_orders_settings(): void
    {
        $this->put(route('admin.settings.update', ['setting' => 'orders']))
            ->assertRedirect(route('admin.login'));
    }

    #[Test]
    public function a_non_admin_cannot_update_customer_settings(): void
    {
        $this->actingAsCustomer()
            ->put(route('admin.settings.update', ['setting' => 'orders']))
            ->assertRedirect(route('admin.login'));
    }

    #[Test]
    public function it_validates_the_update_orders_settings_request(): void
    {
        $this->actingAsAdmin()
            ->get(route('admin.settings.edit', ['setting' => 'orders']))
            ->assertOk();

        $settings = [
            'settings' => [
                'default_parcel_count' => -1,
            ]
        ];

        $this->put(route('admin.settings.update', ['setting' => 'orders']), $settings)
            ->assertRedirect(route('admin.settings.edit', ['setting' => 'orders']))
            ->assertSessionHasErrors([
                'settings.default_parcel_count' => 'Default parcel count field can not be negative',
            ]);
    }

    #[Test]
    public function order_settings_can_be_updated(): void
    {
        $this->actingAsAdmin()
            ->get(route('admin.settings.edit', ['setting' => 'orders']))
            ->assertOk();

        $settings['settings'] = Arr::add($this->orderSettingsAttributes(), 'default_parcel_count', 8);

        $this->put(route('admin.settings.update', ['setting' => 'orders']), $settings)
            ->assertRedirect(route('admin.settings.edit', ['setting' => 'orders']))
            ->assertSessionDoesntHaveErrors();

        foreach ($settings['settings'] as $key => $value) {
            $this->assertDatabaseHas(Setting::class, compact('key', 'value'));
        }
    }

    #[Test]
    public function it_validates_the_default_parcel_count_field(): void
    {
        $this->actingAsAdmin()
            ->get(route('admin.settings.edit', ['setting' => 'orders']))
            ->assertOk();

        $settings['settings'] = Arr::add($this->orderSettingsAttributes(), 'default_parcel_count', -1);

        $this->put(route('admin.settings.update', ['setting' => 'orders']), $settings)
            ->assertRedirect(route('admin.settings.edit', ['setting' => 'orders']))
            ->assertSessionHasErrors([
                'settings.default_parcel_count' => 'Default parcel count field can not be negative',
            ]);

        $settings['settings'] = Arr::add($this->orderSettingsAttributes(), 'default_parcel_count', 15);

        $this->put(route('admin.settings.update', ['setting' => 'orders']), $settings)
            ->assertRedirect(route('admin.settings.edit', ['setting' => 'orders']))
            ->assertSessionHasErrors([
                'settings.default_parcel_count' => 'Default parcel count field can not exceed 10',
            ]);

        $settings['settings'] = Arr::add($this->orderSettingsAttributes(), 'default_parcel_count', 'count');

        $this->put(route('admin.settings.update', ['setting' => 'orders']), $settings)
            ->assertRedirect(route('admin.settings.edit', ['setting' => 'orders']))
            ->assertSessionHasErrors([
                'settings.default_parcel_count' => 'Default parcel count field must be a number between 0 - 10',
            ]);

        $settings['settings'] = Arr::add($this->orderSettingsAttributes(), 'default_parcel_count', 5);

        $this->put(route('admin.settings.update', ['setting' => 'orders']), $settings)
            ->assertRedirect(route('admin.settings.edit', ['setting' => 'orders']))
            ->assertSessionHasNoErrors();
    }

    private function orderSettingsAttributes()
    {
        return [
            'ordering_mode' => '1',
            'layout_page_break' => 'Page Break',
            'print_template_packing' => 'default',
            'show_customer_notes_on_invoice' => '0',
            'print_template_invoice' => 'default',
            'order_items_sort_order' => 'title-asc',
            'order_items_sort_order_2' => 'title-desc',
            'treat_custom_sort_as_integer' => '0',
            'process_order_show' => '1',
            'process_order_status' => '4',
            'process_order_email' => '10',
            'process_order_charge' => '1',
        ];
    }
}
