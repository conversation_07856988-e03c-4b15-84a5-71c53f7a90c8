<?php

namespace Tests\Feature\Admin;

use App\Models\Filter;
use App\Models\Payment;
use App\Models\Pickup;
use App\Models\PickupState;
use App\Models\PickupZip;
use App\Models\ProductPriceGroup;
use App\Models\RecurringOrder;
use App\Models\Schedule;
use App\Models\Template;
use App\Support\Enums\PickupStatus;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Str;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class DeliveryTest extends TenantTestCase
{
    #[Test]
    public function a_non_admin_cannot_view_the_delivery_location_list(): void
    {
        $this->actingAsCustomer()
            ->get(route('admin.delivery.index'))
            ->assertRedirect(route('admin.login'));
    }

    #[Test]
    public function an_admin_can_view_an_unfiltered_delivery_location_list(): void
    {
        $deliveries = Pickup::factory()->delivery()->count(2)->create();

        $this->actingAsAdmin()
            ->get(route('admin.delivery.index'))
            ->assertOk()
            ->assertViewIs('logistics.delivery.index')
            ->assertViewHas('savedFilters', new Collection())
            ->assertViewHas('appliedFilter', function ($value) { return is_null($value); })
            ->assertViewHas('appliedFilters', collect())
            ->assertViewHas('pickups', function ($view_data) use ($deliveries) {
                return $view_data instanceof \Illuminate\Support\Collection
                    && $view_data->count() === 2
                    && $view_data->contains($deliveries->first())
                    && $view_data->contains($deliveries->last());
            });
    }

    #[Test]
    public function an_admin_can_filter_delivery_zones_by_title(): void
    {
        Pickup::factory()->delivery()->create(['title' => 'abcde']);
        $expected = Pickup::factory()->delivery()->create(['title' => 'vwxyz']);

        $this->actingAsAdmin()
            ->get(route('admin.delivery.index', ['locations' => 'wxy']))
            ->assertOk()
            ->assertViewHas('pickups', function (Collection $arg) use ($expected) {
                return $arg->contains(fn (Pickup $pickup) => $pickup->id === $expected->id);
            });
    }

    #[Test]
    public function an_admin_can_filter_delivery_zones_by_ids(): void
    {
        Pickup::factory()->delivery()->create();
        $expected_one = Pickup::factory()->delivery()->create();
        $expected_two = Pickup::factory()->delivery()->create();

        $this->actingAsAdmin()
            ->get(route('admin.delivery.index', ['pickup_id' => [$expected_one->id, $expected_two->id]]))
            ->assertOk()
            ->assertViewHas('pickups', function (Collection $arg) use ($expected_one, $expected_two) {
                return $arg->contains(fn (Pickup $pickup) => $pickup->id === $expected_one->id)
                    && $arg->contains(fn (Pickup $pickup) => $pickup->id === $expected_two->id);
            });
    }

    #[Test]
    public function an_admin_can_filter_delivery_zones_by_status(): void
    {
        Pickup::factory()->delivery()->create(['status_id' => 1]);
        $expected = Pickup::factory()->delivery()->create(['status_id' => 2]);

        $this->actingAsAdmin()
            ->get(route('admin.delivery.index', ['location_status' => 2]))
            ->assertOk()
            ->assertViewHas('pickups', function (Collection $arg) use ($expected) {
                return $arg->contains(fn (Pickup $pickup) => $pickup->id === $expected->id);
            });
    }

    #[Test]
    public function an_admin_can_filter_delivery_zones_by_schedule_ids(): void
    {
        Pickup::factory()->delivery()->create();
        $schedule_one = Schedule::factory()->create();
        $expected_one = Pickup::factory()->delivery()->create(['schedule_id' => $schedule_one->id]);
        $schedule_two = Schedule::factory()->create();
        $expected_two = Pickup::factory()->delivery()->create(['schedule_id' => $schedule_two->id]);

        $this->actingAsAdmin()
            ->get(route('admin.delivery.index', ['schedule_id' => $schedule_two->id]))
            ->assertOk()
            ->assertViewHas('pickups', function (Collection $arg) use ($expected_two, $expected_one) {
                return $arg->contains(fn (Pickup $pickup) => $pickup->id === $expected_two->id)
                    && $arg->doesntContain(fn (Pickup $pickup) => $pickup->id === $expected_one->id);
            });


        $this->get(route('admin.delivery.index', ['schedule_id' => [$schedule_one->id, $schedule_two->id]]))
            ->assertOk()
            ->assertViewHas('pickups', function (Collection $arg) use ($expected_one, $expected_two) {
                return $arg->contains(fn (Pickup $pickup) => $pickup->id === $expected_two->id)
                    && $arg->contains(fn (Pickup $pickup) => $pickup->id === $expected_one->id);
            });
    }

    #[Test]
    public function an_admin_can_filter_delivery_zones_by_state(): void
    {
        Pickup::factory()->delivery()->create(['state' => 'AB']);
        $expected = Pickup::factory()->delivery()->create(['state' => 'YZ']);

        $this->actingAsAdmin()
            ->get(route('admin.delivery.index', ['state' => 'YZ']))
            ->assertOk()
            ->assertViewHas('pickups', function (Collection $arg) use ($expected) {
                return $arg->contains(fn (Pickup $pickup) => $pickup->id === $expected->id);
            });
    }

    #[Test]
    public function an_admin_can_filter_delivery_zones_by_deleted_status(): void
    {
        $active = Pickup::factory()->delivery()->create(['deleted_at' => null]);
        $deleted = Pickup::factory()->delivery()->create(['deleted_at' => now()]);

        $this->actingAsAdmin()
            ->get(route('admin.delivery.index'))
            ->assertOk()
            ->assertViewHas('pickups', function (Collection $arg) use ($active, $deleted) {
                return $arg->contains(fn (Pickup $pickup) => $pickup->id === $active->id)
                    && $arg->doesntContain(fn (Pickup $pickup) => $pickup->id === $deleted->id);
            });

        $this->actingAsAdmin()
            ->get(route('admin.delivery.index', ['show_deleted' => 'true']))
            ->assertOk()
            ->assertViewHas('pickups', function (Collection $arg) use ($active, $deleted) {
                return $arg->contains(fn (Pickup $pickup) => $pickup->id === $active->id)
                    && $arg->contains(fn (Pickup $pickup) => $pickup->id === $deleted->id);
            });
    }

    #[Test]
    public function an_admin_can_filter_delivery_zones_by_visibility(): void
    {
        $hidden = Pickup::factory()->delivery()->create(['visible' => 0]);
        $visible = Pickup::factory()->delivery()->create(['visible' => 1]);

        $this->actingAsAdmin()
            ->get(route('admin.delivery.index'))
            ->assertOk()
            ->assertViewHas('pickups', function (Collection $arg) use ($hidden, $visible) {
                return $arg->contains(fn (Pickup $pickup) => $pickup->id === $hidden->id)
                    && $arg->contains(fn (Pickup $pickup) => $pickup->id === $visible->id);
            });

        $this->actingAsAdmin()
            ->get(route('admin.delivery.index', ['location_visibility' => 0]))
            ->assertOk()
            ->assertViewHas('pickups', function (Collection $arg) use ($hidden, $visible) {
                return $arg->contains(fn (Pickup $pickup) => $pickup->id === $hidden->id)
                    && $arg->doesntContain(fn (Pickup $pickup) => $pickup->id === $visible->id);
            });

        $this->actingAsAdmin()
            ->get(route('admin.delivery.index', ['location_visibility' => 1]))
            ->assertOk()
            ->assertViewHas('pickups', function (Collection $arg) use ($visible, $hidden) {
                return $arg->contains(fn (Pickup $pickup) => $pickup->id === $visible->id)
                    && $arg->doesntContain(fn (Pickup $pickup) => $pickup->id === $hidden->id);
            });
    }

    #[Test]
    public function an_admin_can_filter_delivery_zones_by_postal_code(): void
    {
        Pickup::factory()->delivery()->create();
        $expected = Pickup::factory()->delivery()->create();
        /** @var PickupZip $zip */
        $zip = PickupZip::factory()->create(['pickup_id' => $expected->id]);

        $this->actingAsAdmin()
            ->get(route('admin.delivery.index', ['postal_code' => $zip->zip]))
            ->assertOk()
            ->assertViewHas('pickups', function (Collection $arg) use ($expected) {
                return $arg->contains(fn (Pickup $pickup) => $pickup->id === $expected->id);
            });
    }

    #[Test]
    public function an_admin_can_filter_delivery_zones_by_shipping_state(): void
    {
        Pickup::factory()->delivery()->create();
        $expected = Pickup::factory()->delivery()->create();
        /** @var PickupState $state */
        $state = PickupState::factory()->create(['pickup_id' => $expected->id]);

        $this->actingAsAdmin()
            ->get(route('admin.delivery.index', ['shipping_state' => $state->state]))
            ->assertOk()
            ->assertViewHas('pickups', function (Collection $arg) use ($expected) {
                return $arg->contains(fn (Pickup $pickup) => $pickup->id === $expected->id);
            });
    }

    #[Test]
    public function it_can_sort_delivery_zones_by_attributes(): void
    {
        /** @var Pickup $pickup_one  */
        $pickup_one = Pickup::factory()->delivery()->create([
            'title' => 'abc',
            'state' => 'AB'
        ]);

        /** @var Pickup $pickup_two */
        $pickup_two = Pickup::factory()->delivery()->create([
            'title' => 'xyz',
            'state' => 'YZ'
        ]);

        $this->actingAsAdmin()
            ->get(route('admin.delivery.index', ['orderBy' => 'title', 'sort' => 'asc']))
            ->assertOk()
            ->assertViewHas('pickups', function (Collection $arg) use ($pickup_one, $pickup_two) {
                $pickupIds = $arg->map(function (Pickup $pickup) {
                    return $pickup->id;
                });

                return $pickupIds->search($pickup_one->id) < $pickupIds->search($pickup_two->id);
            });

        $this->get(route('admin.delivery.index', ['orderBy' => 'title', 'sort' => 'desc']))
            ->assertOk()
            ->assertViewHas('pickups', function (Collection $arg) use ($pickup_one, $pickup_two) {
                $pickupIds = $arg->map(function (Pickup $pickup) {
                    return $pickup->id;
                });

                return $pickupIds->search($pickup_two->id) < $pickupIds->search($pickup_one->id);
            });

        $this->actingAsAdmin()
            ->get(route('admin.delivery.index', ['orderBy' => 'state', 'sort' => 'asc']))
            ->assertOk()
            ->assertViewHas('pickups', function (Collection $arg) use ($pickup_one, $pickup_two) {
                $pickupIds = $arg->map(function (Pickup $pickup) {
                    return $pickup->id;
                });

                return $pickupIds->search($pickup_one->id) < $pickupIds->search($pickup_two->id);
            });

        $this->get(route('admin.delivery.index', ['orderBy' => 'state', 'sort' => 'desc']))
            ->assertOk()
            ->assertViewHas('pickups', function (Collection $arg) use ($pickup_one, $pickup_two) {
                $pickupIds = $arg->map(function (Pickup $pickup) {
                    return $pickup->id;
                });

                return $pickupIds->search($pickup_two->id) < $pickupIds->search($pickup_one->id);
            });
    }

    #[Test]
    public function delivery_location_list_is_ordered_by_title_in_asc_order_by_default(): void
    {
        $delivery_one = Pickup::factory()->delivery()->create(['title' => 'xyz']);
        $delivery_two = Pickup::factory()->delivery()->create(['title' => 'abc']);

        $this->actingAsAdmin()
            ->get(route('admin.delivery.index'))
            ->assertOk()
            ->assertViewHas('pickups', function ($view_data) use ($delivery_one, $delivery_two) {
                $pickupIds = $view_data->map(function (Pickup $pickup) {
                    return $pickup->id;
                });

                return $pickupIds->search($delivery_two->id) < $pickupIds->search($delivery_one->id);
            });
    }

    #[Test]
    public function delivery_location_list_can_be_sorted_in_desc_order(): void
    {
        $delivery_one = Pickup::factory()->delivery()->create(['title' => 'xyz']);
        $delivery_two = Pickup::factory()->delivery()->create(['title' => 'abc']);

        $this->actingAsAdmin()
            ->get(route('admin.delivery.index', ['sort' => 'desc']))
            ->assertOk()
            ->assertViewHas('pickups', function ($view_data) use ($delivery_one, $delivery_two) {
                $pickupIds = $view_data->map(function (Pickup $pickup) {
                    return $pickup->id;
                });

                return $pickupIds->search($delivery_one->id) < $pickupIds->search($delivery_two->id);
            });
    }

    #[Test]
    public function saved_delivery_filters_are_available_when_viewing_delivery_location_list(): void
    {
        $delivery_filters = Filter::factory()->count(2)->create(['type' => 'delivery']);
        $other_filters = Filter::factory()->count(2)->create(['type' => 'other']);

        $this->actingAsAdmin()
            ->get(route('admin.delivery.index'))
            ->assertOk()
            ->assertViewHas('appliedFilter', function ($value) { return is_null($value); })
            ->assertViewHas('savedFilters', function ($savedFilters) use ($delivery_filters, $other_filters) {
                return $savedFilters->count() === 2
                    && $savedFilters->contains($delivery_filters[0])
                    && $savedFilters->contains($delivery_filters[1])
                    && $savedFilters->doesntContain($other_filters[0])
                    && $savedFilters->doesntContain($other_filters[1]);
            });
    }

    #[Test]
    public function saved_delivery_filters_can_be_applied_when_viewing_delivery_location_list(): void
    {
        $delivery_filters = Filter::factory()->count(2)->create(['type' => 'delivery']);

        $this->actingAsAdmin()
            ->get(route('admin.delivery.index', ['filter_id' => $delivery_filters->last()->id]))
            ->assertOk()
            ->assertViewHas('appliedFilter', $delivery_filters->last());
    }

    #[Test]
    public function invalid_delivery_filters_cannot_be_applied_when_viewing_delivery_location_list(): void
    {
        Filter::factory()->count(2)->create(['type' => 'delivery']);

        $this->actingAsAdmin()
            ->get(route('admin.delivery.index', ['filter_id' => 'abc123']))
            ->assertOk()
            ->assertViewHas('appliedFilter', function ($value) { return is_null($value); });
    }

    #[Test]
    public function applied_filters_are_available_when_viewing_delivery_location_list(): void
    {
        Filter::factory()->count(2)->create(['type' => 'delivery']);

        $this->actingAsAdmin()
            ->get(route('admin.delivery.index', ['filter_id' => 'abc123']))
            ->assertOk()
            ->assertViewHas('appliedFilters');
    }

    #[Test]
    public function a_non_admin_cannot_create_a_delivery_location(): void
    {
        $this->actingAsCustomer()
            ->post(route('admin.delivery.store'))
            ->assertRedirect(route('admin.login'));
    }

    #[Test]
    public function a_title_is_required_when_creating_a_delivery_location(): void
    {
        $this->actingAsAdmin()
            ->post(route('admin.delivery.store'))
            ->assertSessionHasErrors(['title' => 'Please provide a name.']);
    }

    #[Test]
    public function field_character_limits_are_enforced_when_creating_a_delivery_location(): void
    {
        $this->actingAsAdmin()
            ->post(route('admin.delivery.store', [
                'title' => Str::random(256),
            ]))
            ->assertSessionHasErrors([
                'title' => 'The title field must not be greater than 255 characters.',
            ]);
    }

    #[Test]
    public function an_admin_can_create_a_delivery_location(): void
    {
        $currentPickupCount = Pickup::count();

        $response = $this->actingAsAdmin()
            ->post(route('admin.delivery.store', ['title' => 'test location']));

        $this->assertDatabaseHas('pickups', [
            'title' => 'test location',
            'fulfillment_type' => 2
        ]);

        $this->assertDatabaseCount(Pickup::class, $currentPickupCount + 1);

        $new_delivery = Pickup::latest('id')->first();

        $response->assertRedirect(route('admin.delivery.edit', ['delivery' => $new_delivery]));
    }

    #[Test]
    public function a_non_admin_cannot_view_edit_page_for_a_delivery_location(): void
    {
        $delivery = Pickup::factory()->delivery()->create();

        $this->actingAsCustomer()
            ->get(route('admin.delivery.edit', compact('delivery')))
            ->assertRedirect(route('admin.login'));
    }

    #[Test]
    public function a_admin_cannot_view_edit_page_for_a_delivery_location_that_doesnt_exist(): void
    {
        $this->actingAsAdmin()
            ->get(route('admin.delivery.index'))
            ->assertOk();

        $this->get(route('admin.delivery.edit', ['delivery' => 'abc']))
            ->assertRedirect(route('admin.delivery.index'));
    }

    #[Test]
    public function a_admin_can_view_the_edit_page_for_a_delivery_location(): void
    {
        $schedule = Schedule::factory()->create();
        $delivery = Pickup::factory()->delivery()->create(['schedule_id' => $schedule->id]);

        $this->actingAsAdmin()
            ->get(route('admin.delivery.edit', compact('delivery')))
            ->assertOk()
            ->assertViewIs('logistics.delivery.edit')
            ->assertViewHas('option', function (Pickup $loaded_delivery) use ($delivery) {
                return $loaded_delivery->id === $delivery->id
                    && $loaded_delivery->relationLoaded('schedule')
                    && $loaded_delivery->schedule->relationLoaded('dates');
            });
    }

    #[Test]
    public function a_non_admin_cannot_update_a_delivery_location(): void
    {
        $delivery = Pickup::factory()->delivery()->create();

        $this->actingAsCustomer()
            ->patch(route('admin.delivery.update', compact('delivery')))
            ->assertRedirect(route('admin.login'));
    }

    #[Test]
    public function a_admin_cannot_view_update_a_delivery_location_that_doesnt_exist(): void
    {
        $this->actingAsAdmin()
            ->get(route('admin.delivery.index'))
            ->assertOk();

        $this->patch(route('admin.delivery.update', ['delivery' => 'abc']))
            ->assertRedirect(route('admin.delivery.index'));
    }

    #[Test]
    public function a_title_cannot_be_blank_when_updating_a_delivery_location(): void
    {
        $delivery = Pickup::factory()->delivery()->create();

        $this->actingAsAdmin()
            ->patch(route('admin.delivery.update', compact('delivery')), ['title' => ''])
            ->assertSessionHasErrors(['title' => 'The title field is required.']);
    }

    #[Test]
    public function field_character_limits_are_enforced_when_updating_a_delivery_location(): void
    {
        $delivery = Pickup::factory()->delivery()->create();

        $this->actingAsAdmin()
            ->patch(route('admin.delivery.update', compact('delivery')), [
                'title' => Str::random(256),
                'display_name' => Str::random(256),
            ])
            ->assertSessionHasErrors([
                'title' => 'The title field must not be greater than 255 characters.',
                'display_name' => 'The display name field must not be greater than 255 characters.',
            ]);
    }

    #[Test]
    public function the_selected_schedule_must_be_valid_when_updating_a_delivery(): void
    {
        $delivery = Pickup::factory()->delivery()->create();

        $this->actingAsAdmin()
            ->patch(route('admin.delivery.update', compact('delivery')), ['schedule_id' => 'abc'])
            ->assertSessionHasErrors(['schedule_id' => 'The selected schedule id is invalid.']);
    }

    #[Test]
    public function the_selected_delivery_fee_type_be_valid_when_updating_a_delivery(): void
    {
        $delivery = Pickup::factory()->delivery()->create();

        $this->actingAsAdmin()
            ->patch(route('admin.delivery.update', compact('delivery')), ['settings' => ['delivery_fee_type' => 0]])
            ->assertSessionHasErrors(['settings.delivery_fee_type' => 'The selected settings.delivery fee type is invalid.']);

        $this->actingAsAdmin()
            ->patch(route('admin.delivery.update', compact('delivery')), ['settings' => ['delivery_fee_type' => 3]])
            ->assertSessionHasErrors(['settings.delivery_fee_type' => 'The selected settings.delivery fee type is invalid.']);

        $this->actingAsAdmin()
            ->patch(route('admin.delivery.update', compact('delivery')), ['settings' => ['delivery_fee_type' => 1]])
            ->assertSessionDoesntHaveErrors(['settings.delivery_fee_type']);

        $this->actingAsAdmin()
            ->patch(route('admin.delivery.update', compact('delivery')), ['settings' => ['delivery_fee_type' => 2]])
            ->assertSessionDoesntHaveErrors(['settings.delivery_fee_type']);
    }

    #[Test]
    public function the_delivery_rate_validation_must_pass_when_updating_a_delivery(): void
    {
        $delivery = Pickup::factory()->delivery()->create();

        $this->actingAsAdmin()
            ->patch(route('admin.delivery.update', compact('delivery')), ['delivery_rate' => 'abc'])
            ->assertSessionHasErrors(['delivery_rate' => 'The Delivery Fee price field must be a number.']);

        $this->actingAsAdmin()
            ->patch(route('admin.delivery.update', compact('delivery')), ['delivery_rate' => -1])
            ->assertSessionHasErrors(['delivery_rate' => 'The Delivery Fee price field cannot be negative.']);
    }

    #[Test]
    public function the_tax_delivery_fee_must_be_a_boolean_when_updating_a_delivery(): void
    {
        $delivery = Pickup::factory()->delivery()->create();

        $this->actingAsAdmin()
            ->patch(route('admin.delivery.update', compact('delivery')), ['tax_delivery_fee' => 'a'])
            ->assertSessionHasErrors(['tax_delivery_fee' => 'The tax delivery fee field must be true or false.']);
    }

    #[Test]
    public function the_apply_limit_must_be_a_boolean_when_updating_a_delivery(): void
    {
        $delivery = Pickup::factory()->delivery()->create();

        $this->actingAsAdmin()
            ->patch(route('admin.delivery.update', compact('delivery')), ['apply_limit' => 'a'])
            ->assertSessionHasErrors(['apply_limit' => 'The cap delivery fee field must be true or false.']);
    }

    #[Test]
    public function the_delivery_total_threshold_validation_must_pass_when_updating_a_delivery(): void
    {
        $delivery = Pickup::factory()->delivery()->create();

        $this->actingAsAdmin()
            ->patch(route('admin.delivery.update', compact('delivery')), ['delivery_total_threshold' => 'abc'])
            ->assertSessionHasErrors(['delivery_total_threshold' => 'The Cap Threshold field must be a number.']);

        $this->actingAsAdmin()
            ->patch(route('admin.delivery.update', compact('delivery')), ['delivery_total_threshold' => -1])
            ->assertSessionHasErrors(['delivery_total_threshold' => 'The Cap Threshold field cannot be negative.']);
    }

    #[Test]
    public function the_delivery_fee_cap_validation_must_pass_when_updating_a_delivery(): void
    {
        $delivery = Pickup::factory()->delivery()->create();

        $this->actingAsAdmin()
            ->patch(route('admin.delivery.update', compact('delivery')), ['delivery_fee_cap' => 'abc'])
            ->assertSessionHasErrors(['delivery_fee_cap' => 'The Capped Delivery Fee Total field must be a number.']);

        $this->actingAsAdmin()
            ->patch(route('admin.delivery.update', compact('delivery')), ['delivery_fee_cap' => -1])
            ->assertSessionHasErrors(['delivery_fee_cap' => 'The Capped Delivery Fee Total field cannot be negative.']);
    }

    #[Test]
    public function the_display_cart_shipping_calculator_must_be_a_boolean_when_updating_a_delivery(): void
    {
        $delivery = Pickup::factory()->delivery()->create();

        $this->actingAsAdmin()
            ->patch(route('admin.delivery.update', compact('delivery')), ['display_cart_shipping_calculator' => 'a'])
            ->assertSessionHasErrors(['display_cart_shipping_calculator' => 'The display cart shipping calculator field must be true or false.']);
    }

    #[Test]
    public function the_status_validation_must_pass_when_updating_a_delivery(): void
    {
        $delivery = Pickup::factory()->delivery()->create();

        $this->actingAsAdmin()
            ->patch(route('admin.delivery.update', compact('delivery')), ['status_id' => 10])
            ->assertSessionHasErrors(['status_id' => 'The selected status id is invalid.']);

        $this->actingAsAdmin()
            ->patch(route('admin.delivery.update', compact('delivery')), ['status_id' => 1])
            ->assertSessionDoesntHaveErrors(['status_id']);

        $this->actingAsAdmin()
            ->patch(route('admin.delivery.update', compact('delivery')), ['status_id' => 2])
            ->assertSessionHasErrors(['status_id' => 'The selected status id is invalid.']);

        $this->actingAsAdmin()
            ->patch(route('admin.delivery.update', compact('delivery')), ['status_id' => 3])
            ->assertSessionDoesntHaveErrors(['status_id']);

        $this->actingAsAdmin()
            ->patch(route('admin.delivery.update', compact('delivery')), ['status_id' => 4])
            ->assertSessionDoesntHaveErrors(['status_id']);
    }

    #[Test]
    public function the_visible_field_must_be_a_boolean_when_updating_a_delivery(): void
    {
        $delivery = Pickup::factory()->delivery()->create();

        $this->actingAsAdmin()
            ->patch(route('admin.delivery.update', compact('delivery')), ['visible' => 'a'])
            ->assertSessionHasErrors(['visible' => 'The visible field must be true or false.']);
    }

    #[Test]
    public function the_tax_rate_validation_must_pass_when_updating_a_delivery(): void
    {
        $delivery = Pickup::factory()->delivery()->create();

        $this->actingAsAdmin()
            ->patch(route('admin.delivery.update', compact('delivery')), ['tax_rate' => 'abc'])
            ->assertSessionHasErrors(['tax_rate' => 'The tax rate field must be a number.']);

        $this->actingAsAdmin()
            ->patch(route('admin.delivery.update', compact('delivery')), ['tax_rate' => -1])
            ->assertSessionHasErrors(['tax_rate' => 'The tax rate field must be at least 0.']);

        $this->actingAsAdmin()
            ->patch(route('admin.delivery.update', compact('delivery')), ['tax_rate' => 10000.01])
            ->assertSessionHasErrors(['tax_rate' => 'The tax rate field must not be greater than 100.']);
    }

    #[Test]
    public function the_min_customer_orders_validation_must_pass_when_updating_a_delivery(): void
    {
        $delivery = Pickup::factory()->delivery()->create();

        $this->actingAsAdmin()
            ->patch(route('admin.delivery.update', compact('delivery')), ['min_customer_orders' => 'abc'])
            ->assertSessionHasErrors(['min_customer_orders' => 'The min customer orders field must be a number.']);

        $this->actingAsAdmin()
            ->patch(route('admin.delivery.update', compact('delivery')), ['min_customer_orders' => -1])
            ->assertSessionHasErrors(['min_customer_orders' => 'The min customer orders field must be at least 0.']);
    }

    #[Test]
    public function the_payment_methods_validation_must_pass_when_updating_a_delivery(): void
    {
        $delivery = Pickup::factory()->delivery()->create();

        $this->actingAsAdmin()
            ->patch(route('admin.delivery.update', compact('delivery')), ['payment_methods' => 'abc'])
            ->assertSessionHasErrors(['payment_methods' => 'The payment methods field must be an array.']);
    }

    #[Test]
    public function the_sales_channel_validation_must_pass_when_updating_a_delivery(): void
    {
        $delivery = Pickup::factory()->delivery()->create();

        $this->actingAsAdmin()
            ->patch(route('admin.delivery.update', compact('delivery')), ['settings' => ['sales_channel' => 'abc']])
            ->assertSessionHasErrors(['settings.sales_channel' => 'The settings.sales channel field must be an integer.']);

        $this->actingAsAdmin()
            ->patch(route('admin.delivery.update', compact('delivery')), ['settings' => ['sales_channel' => 1]])
            ->assertSessionDoesntHaveErrors(['settings.sales_channel']);

        $this->actingAsAdmin()
            ->patch(route('admin.delivery.update', compact('delivery')), ['settings' => ['sales_channel' => 2]])
            ->assertSessionDoesntHaveErrors(['settings.sales_channel']);

        $this->actingAsAdmin()
            ->patch(route('admin.delivery.update', compact('delivery')), ['settings' => ['sales_channel' => 3]])
            ->assertSessionDoesntHaveErrors(['settings.sales_channel']);

        $this->actingAsAdmin()
            ->patch(route('admin.delivery.update', compact('delivery')), ['settings' => ['sales_channel' => 4]])
            ->assertSessionDoesntHaveErrors(['settings.sales_channel']);

        $this->actingAsAdmin()
            ->patch(route('admin.delivery.update', compact('delivery')), ['settings' => ['sales_channel' => 5]])
            ->assertSessionDoesntHaveErrors(['settings.sales_channel']);

        $this->actingAsAdmin()
            ->patch(route('admin.delivery.update', compact('delivery')), ['settings' => ['sales_channel' => 6]])
            ->assertSessionDoesntHaveErrors(['settings.sales_channel']);
    }

    #[Test]
    public function the_pricing_group_id_validation_must_pass_when_updating_a_delivery(): void
    {
        $delivery = Pickup::factory()->delivery()->create();
        $price_group = ProductPriceGroup::factory()->create();

        $this->actingAsAdmin()
            ->patch(route('admin.delivery.update', compact('delivery')), ['pricing_group_id' => 'abc'])
            ->assertSessionHasErrors(['pricing_group_id' => 'The selected pricing group id is invalid.']);

        $this->actingAsAdmin()
            ->patch(route('admin.delivery.update', compact('delivery')), ['pricing_group_id' => ''])
            ->assertSessionDoesntHaveErrors(['pricing_group_id']);

        $this->actingAsAdmin()
            ->patch(route('admin.delivery.update', compact('delivery')), ['pricing_group_id' => null])
            ->assertSessionDoesntHaveErrors(['pricing_group_id']);

        $this->actingAsAdmin()
            ->patch(route('admin.delivery.update', compact('delivery')), ['pricing_group_id' => $price_group->id])
            ->assertSessionDoesntHaveErrors(['pricing_group_id']);
    }

    #[Test]
    public function the_email_order_confirmation_template_validation_must_pass_when_updating_a_delivery(): void
    {
        $delivery = Pickup::factory()->delivery()->create();
        $template = Template::factory()->create();

        $this->actingAsAdmin()
            ->patch(route('admin.delivery.update', compact('delivery')), ['settings' => ['email_order_confirmation_template' => 'abc']])
            ->assertSessionHasErrors(['settings.email_order_confirmation_template' => 'The selected settings.email order confirmation template is invalid.']);

        $this->actingAsAdmin()
            ->patch(route('admin.delivery.update', compact('delivery')), ['settings' => ['email_order_confirmation_template' => $template->id]])
            ->assertSessionDoesntHaveErrors(['settings.email_order_confirmation_template']);

        $this->actingAsAdmin()
            ->patch(route('admin.delivery.update', compact('delivery')), ['settings' => ['email_order_confirmation_template' => '']])
            ->assertSessionDoesntHaveErrors(['settings.email_order_confirmation_template']);

        $this->actingAsAdmin()
            ->patch(route('admin.delivery.update', compact('delivery')), ['settings' => ['email_order_confirmation_template' => null]])
            ->assertSessionDoesntHaveErrors(['settings.email_order_confirmation_template']);
    }

    #[Test]
    public function the_email_order_packed_template_validation_must_pass_when_updating_a_delivery(): void
    {
        $delivery = Pickup::factory()->delivery()->create();
        $template = Template::factory()->create();

        $this->actingAsAdmin()
            ->patch(route('admin.delivery.update', compact('delivery')), ['settings' => ['email_order_packed_template' => 'abc']])
            ->assertSessionHasErrors(['settings.email_order_packed_template' => 'The selected settings.email order packed template is invalid.']);

        $this->actingAsAdmin()
            ->patch(route('admin.delivery.update', compact('delivery')), ['settings' => ['email_order_packed_template' => $template->id]])
            ->assertSessionDoesntHaveErrors(['settings.email_order_packed_template']);

        $this->actingAsAdmin()
            ->patch(route('admin.delivery.update', compact('delivery')), ['settings' => ['email_order_packed_template' => '']])
            ->assertSessionDoesntHaveErrors(['settings.email_order_packed_template']);

        $this->actingAsAdmin()
            ->patch(route('admin.delivery.update', compact('delivery')), ['settings' => ['email_order_packed_template' => null]])
            ->assertSessionDoesntHaveErrors(['settings.email_order_packed_template']);
    }

    #[Test]
    public function the_process_order_email_validation_must_pass_when_updating_a_delivery(): void
    {
        $delivery = Pickup::factory()->delivery()->create();
        $template = Template::factory()->create();

        $this->actingAsAdmin()
            ->patch(route('admin.delivery.update', compact('delivery')), ['settings' => ['process_order_email' => 'abc']])
            ->assertSessionHasErrors(['settings.process_order_email' => 'The selected settings.process order email is invalid.']);

        $this->actingAsAdmin()
            ->patch(route('admin.delivery.update', compact('delivery')), ['settings' => ['process_order_email' => $template->id]])
            ->assertSessionDoesntHaveErrors(['settings.process_order_email']);

        $this->actingAsAdmin()
            ->patch(route('admin.delivery.update', compact('delivery')), ['settings' => ['process_order_email' => '']])
            ->assertSessionDoesntHaveErrors(['settings.process_order_email']);

        $this->actingAsAdmin()
            ->patch(route('admin.delivery.update', compact('delivery')), ['settings' => ['process_order_email' => null]])
            ->assertSessionDoesntHaveErrors(['settings.process_order_email']);
    }

    #[Test]
    public function the_email_subscription_welcome_template_validation_must_pass_when_updating_a_delivery(): void
    {
        $delivery = Pickup::factory()->delivery()->create();
        $template = Template::factory()->create();

        $this->actingAsAdmin()
            ->patch(route('admin.delivery.update', compact('delivery')), ['settings' => ['recurring_orders_welcome_email_template_id' => 'abc']])
            ->assertSessionHasErrors(['settings.recurring_orders_welcome_email_template_id' => 'The selected settings.recurring orders welcome email template id is invalid.']);

        $this->actingAsAdmin()
            ->patch(route('admin.delivery.update', compact('delivery')), ['settings' => ['recurring_orders_welcome_email_template_id' => $template->id]])
            ->assertSessionDoesntHaveErrors(['settings.recurring_orders_welcome_email_template_id']);

        $this->actingAsAdmin()
            ->patch(route('admin.delivery.update', compact('delivery')), ['settings' => ['recurring_orders_welcome_email_template_id' => '']])
            ->assertSessionDoesntHaveErrors(['settings.recurring_orders_welcome_email_template_id']);

        $this->actingAsAdmin()
            ->patch(route('admin.delivery.update', compact('delivery')), ['settings' => ['recurring_orders_welcome_email_template_id' => null]])
            ->assertSessionDoesntHaveErrors(['settings.recurring_orders_welcome_email_template_id']);
    }

    #[Test]
    public function the_email_subscription_reorder_template_validation_must_pass_when_updating_a_delivery(): void
    {
        $delivery = Pickup::factory()->delivery()->create();
        $template = Template::factory()->create();

        $this->actingAsAdmin()
            ->patch(route('admin.delivery.update', compact('delivery')), ['settings' => ['recurring_orders_reorder_email_template_id' => 'abc']])
            ->assertSessionHasErrors(['settings.recurring_orders_reorder_email_template_id' => 'The selected settings.recurring orders reorder email template id is invalid.']);

        $this->actingAsAdmin()
            ->patch(route('admin.delivery.update', compact('delivery')), ['settings' => ['recurring_orders_reorder_email_template_id' => $template->id]])
            ->assertSessionDoesntHaveErrors(['settings.recurring_orders_reorder_email_template_id']);

        $this->actingAsAdmin()
            ->patch(route('admin.delivery.update', compact('delivery')), ['settings' => ['recurring_orders_reorder_email_template_id' => '']])
            ->assertSessionDoesntHaveErrors(['settings.recurring_orders_reorder_email_template_id']);

        $this->actingAsAdmin()
            ->patch(route('admin.delivery.update', compact('delivery')), ['settings' => ['recurring_orders_reorder_email_template_id' => null]])
            ->assertSessionDoesntHaveErrors(['settings.recurring_orders_reorder_email_template_id']);
    }

    #[Test]
    public function the_checkout_notes_validation_must_pass_when_updating_a_delivery(): void
    {
        $delivery = Pickup::factory()->delivery()->create();

        $this->actingAsAdmin()
            ->patch(route('admin.delivery.update', compact('delivery')), ['settings' => ['checkout_notes' => '']])
            ->assertSessionDoesntHaveErrors(['settings.checkout_notes']);
    }

    #[Test]
    public function an_admin_can_update_a_delivery_location(): void
    {
        /** @var Pickup $delivery */
        $delivery = Pickup::factory()->delivery()->create();
        $template = Template::factory()->create();
        $payment_methods = Payment::factory()->count(2)->create();
        $product_price_group = ProductPriceGroup::factory()->create();
        $schedule = Schedule::factory()->create();

        $this->actingAsAdmin()
            ->patch(route('admin.delivery.update', compact('delivery')), [
                'title' => 'new title',
                'display_name' => 'new display name',
                'settings' => [
                    'delivery_fee_type' => 2,
                    'sales_channel' => 2,
                    'email_order_confirmation_template' => $template->id,
                    'email_order_packed_template' => $template->id,
                    'process_order_email' => $template->id,
                    'checkout_notes' => 'new checkout_notes',
                    'recurring_orders_reorder_email_template_id' => $template->id,
                    'recurring_orders_welcome_email_template_id' => $template->id,
                    'sms_subscription_reorder_template' => '',
                ],
                'delivery_rate' => 10,
                'tax_delivery_fee' => true,
                'apply_limit' => true,
                'delivery_total_threshold' => 20,
                'delivery_fee_cap' => 30,
                'display_cart_shipping_calculator' => true,
                'status_id' => 3,
                'visible' => true,
                'tax_rate' => 12,
                'min_customer_orders' => 10,
                'payment_methods' => $payment_methods->pluck('id')->toArray(),
                'pricing_group_id' => $product_price_group->id,
                'pickup_times' => 'new pickup times',
                'schedule_id' => $schedule->id
            ])
            ->assertRedirect()
            ->assertSessionHasNoErrors();

        $this->assertDatabaseHas('pickups', [
            'id' => $delivery->id,
            'title' => 'new title',
            'display_name' => 'new display name',
            'delivery_rate' => 1000, // 10 * 100
            'tax_delivery_fee' => true,
            'apply_limit' => true,
            'delivery_total_threshold' => 2000, // 20 * 100
            'delivery_fee_cap' => 3000, // 30 * 100
            'display_cart_shipping_calculator' => true,
            'status_id' => 3,
            'visible' => true,
            'tax_rate' => 0.12000, // 12 / 100
            'min_customer_orders' => 1000, // 10 * 100
            'payment_methods' => json_encode($payment_methods->pluck('id')->toArray()),
            'pricing_group_id' => $product_price_group->id,
            'pickup_times' => 'new pickup times',
            'schedule_id' => $schedule->id
        ]);

        $delivery->refresh();

        $this->assertEquals(2, $delivery->setting('delivery_fee_type'));
        $this->assertEquals(2, $delivery->setting('sales_channel'));
        $this->assertEquals($template->id, $delivery->setting('email_order_confirmation_template'));
        $this->assertEquals($template->id, $delivery->setting('email_order_packed_template'));
        $this->assertEquals($template->id, $delivery->setting('process_order_email'));
        $this->assertNUll($delivery->setting('sms_subscription_reorder_template'));
        $this->assertEquals($template->id, $delivery->setting('recurring_orders_welcome_email_template_id'));
        $this->assertEquals($template->id, $delivery->setting('recurring_orders_reorder_email_template_id'));
        $this->assertEquals('new checkout_notes', $delivery->setting('checkout_notes'));
    }

    #[Test]
    public function admin_can_not_close_an_open_delivery_zone_with_active_subscriptions(): void
    {
        $deliveryZone1 = Pickup::factory()->delivery()->create();
        $deliveryZone2 = Pickup::factory()->delivery()->create();
        $deliveryZone3 = Pickup::factory()->delivery()->create(['status_id' => PickupStatus::closed()]);

        RecurringOrder::factory()->create(['fulfillment_id' => $deliveryZone1]);

        $link = route('admin.users.index', ['pickup_id' => [$deliveryZone1->id], 'subscription_status' => 'active']);
        $this->actingAsAdmin()
            ->patch(route('admin.delivery.update', ['delivery' => $deliveryZone1]), ['status_id' => PickupStatus::closed()])
            ->assertRedirect()
            ->assertSessionHasErrors(['status_id' => "This delivery zone status cannot be changed while there are active subscribers. <a href=$link>View subscribers</a>"]);

        $this->actingAsAdmin()
            ->patch(route('admin.delivery.update', ['delivery' => $deliveryZone3]), ['status_id' => PickupStatus::closed()])
            ->assertSessionHasNoErrors();

        $this->actingAsAdmin()
            ->patch(route('admin.delivery.update', ['delivery' => $deliveryZone2]), ['status_id' => PickupStatus::closed()])
            ->assertSessionHasNoErrors();
    }

    #[Test]
    public function the_selected_schedule_cannot_be_null_with_active_subscriptions(): void
    {
        $delivery = Pickup::factory()->create();
        RecurringOrder::factory()->create(['fulfillment_id' => $delivery->id]);

        $this->actingAsAdmin()
            ->patch(route('admin.delivery.update', compact('delivery')), ['schedule_id' => ''])
            ->assertSessionHasErrors(['schedule_id' => 'The schedule cannot be removed from delivery zones with active subscriptions.']);
    }

    #[Test]
    public function all_requests_to_show_page_are_redirected_to_the_edit_page(): void
    {
        $delivery = Pickup::factory()->delivery()->create();

        $this->actingAsAdmin()->get(route('admin.delivery.show', compact('delivery')))
            ->assertRedirect(route('admin.delivery.edit', compact('delivery')));
    }

    #[Test]
    public function the_selected_schedule_can_be_removed_without_active_subscriptions(): void
    {
        $schedule = Schedule::factory()->create();
        $delivery = Pickup::factory()->create(['schedule_id' => $schedule->id]);

        $this->actingAsAdmin()
            ->patch(route('admin.delivery.update', compact('delivery')), ['schedule_id' => ''])
            ->assertSessionDoesntHaveErrors(['schedule_id']);

        $this->assertDatabaseHas(Pickup::class, [
            'id' => $delivery->id,
            'schedule_id' => null
        ]);
    }
}
