<?php

namespace Tests\Feature\Admin;

use App\Actions\Order\Cancel;
use App\Actions\Subscription\SyncSubscriptionDatetimes;
use App\Models\Order;
use App\Models\RecurringOrder;
use App\Support\Enums\OrderStatus;
use Carbon\Carbon;
use Mo<PERSON>y;
use <PERSON><PERSON>y\MockInterface;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class OrderCancellationTest extends TenantTestCase
{
    #[Test]
    public function a_guest_cannot_cancel_an_order(): void
    {
        $order = Order::factory()->create();

        $this->post(route('admin.orders.cancel.store', compact('order')))
            ->assertRedirect(route('admin.login'));
    }

    #[Test]
    public function a_non_admin_cannot_cancel_an_order(): void
    {
        $order = Order::factory()->create();

        $this->actingAsCustomer()
            ->post(route('admin.orders.cancel.store', compact('order')))
            ->assertRedirect(route('admin.login'));
    }

    #[Test]
    public function an_admin_cannot_cancel_an_order_that_doesnt_exist(): void
    {
        $this->actingAsAdmin()
            ->post(route('admin.orders.cancel.store', ['order' => 'abc']))
            ->assertSessionDoesntHaveErrors()
            ->assertRedirect(url('/'));
    }

    #[Test]
    public function an_admin_can_cancel_an_order(): void
    {
        $order = Order::factory()->create(['canceled' => false, 'status_id' => OrderStatus::processing(), 'canceled_at' => null]);

        $this->mock(Cancel::class, function (MockInterface $mock) use ($order) {
            $mock->shouldReceive('handle')
                ->once()
                ->with(Mockery::on(function ($value) use ($order) {
                    return $value instanceof Order && $value->id === $order->id;
                }))
                ->andReturn($order);
        });

        $this->mock(SyncSubscriptionDatetimes::class, function (MockInterface $mock) {
            $mock->shouldNotReceive('handle');
        });

        $this->actingAsAdmin()
            ->get(route('admin.orders.index'))
            ->assertOk();

        $this->post(route('admin.orders.cancel.store', compact('order')))
            ->assertSessionDoesntHaveErrors()
            ->assertSessionHas('flash_notification', [
                'message' => 'The order has been canceled.',
                'level' => 'info'
            ])
            ->assertRedirect(route('admin.orders.index'));
    }

    #[Test]
    public function an_admin_can_cancel_a_recurring_order(): void
    {
        $blueprint = RecurringOrder::factory()->create();

        /** @var Order $order */
        $order = Order::factory()->create([
            'blueprint_id' => $blueprint->id,
            'customer_id' => $blueprint->customer_id,
            'canceled' => false,
            'status_id' => OrderStatus::processing(),
            'canceled_at' => null,
            'is_recurring' => true,
        ]);

        $this->mock(Cancel::class, function (MockInterface $mock) use ($order) {
            $mock->shouldReceive('handle')
                ->once()
                ->with(Mockery::on(function ($value) use ($order) {
                    return $value instanceof Order && $value->id === $order->id;
                }));
        });

        $this->mock(SyncSubscriptionDatetimes::class, function (MockInterface $mock) use ($blueprint, $order) {
            $mock->shouldReceive('handle')
                ->once()
                ->with(
                    Mockery::on(fn(RecurringOrder $arg) => $arg->id === $blueprint->id),
                    Mockery::on(fn(Carbon $arg) => $arg->eq($order->pickup_date->copy()->addDays($blueprint->reorder_frequency))),
                );
        });

        $this->actingAsAdmin()
            ->get(route('admin.orders.index'))
            ->assertOk();

        $this->post(route('admin.orders.cancel.store', compact('order')))
            ->assertSessionDoesntHaveErrors()
            ->assertSessionHas('flash_notification', [
                'message' => 'The order has been canceled.',
                'level' => 'info'
            ])
            ->assertRedirect(route('admin.orders.index'));
    }
}
