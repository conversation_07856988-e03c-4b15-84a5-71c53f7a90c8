<?php

namespace Tests\Feature\Theme\Account;

use App\Models\User;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class AccountTest extends TenantTestCase
{
    #[Test]
    public function it_redirects_the_choose_password_page_when_attempting_to_view_an_account_without_a_password(): void
    {
        $user = User::factory()->create(['password' => '']);

        $this->actingAs($user)
            ->get(route('customer.profile'))
            ->assertRedirect(route('choose-password.show'));
    }

    #[Test]
    public function it_renders_the_choose_password_page(): void
    {
        $user = User::factory()->create(['password' => '']);

        $this->actingAs($user)
            ->get(route('choose-password.show'))
            ->assertViewIs('theme::authentication.choose-password');
    }

    #[Test]
    public function a_user_can_choose_their_password(): void
    {
        $user = User::factory()->create(['email' => '<EMAIL>', 'password' => '']);

        $this->actingAs($user)
            ->post(route('choose-password.store'), [
                'email' => $user->email,
                'password' => 'password',
            ])
            ->assertSessionHasNoErrors()
            ->assertRedirect(route('homepage.show'));
    }
}
