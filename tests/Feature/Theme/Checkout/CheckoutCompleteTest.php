<?php

namespace Tests\Feature\Theme\Checkout;

use App\Models\Integration;
use App\Models\Order;
use App\Models\Setting;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class CheckoutCompleteTest extends TenantTestCase
{
    #[Test]
    public function it_cannot_be_viewed_by_a_guest(): void
    {
        $this->get(route('checkout.complete.show'))
            ->assertRedirect(route('login'));
    }

    #[Test]
    public function it_cannot_be_viewed_when_session_does_not_have_last_confirmed_order(): void
    {
        $this->actingAsCustomer()
            ->get(route('checkout.complete.show'))
            ->assertRedirect(route('customer.orders'));
    }

    #[Test]
    public function it_cannot_be_viewed_when_last_confirmed_order_cannot_be_found(): void
    {
        $this->actingAsCustomer();

        session(['last_confirmed_order' => 0]);

        $this->get(route('checkout.complete.show'))
            ->assertRedirect(route('customer.orders'));
    }

    #[Test]
    public function it_cannot_be_viewed_when_last_confirmed_order_is_not_confirmed(): void
    {
        $order = Order::factory()->create(['confirmed' => false]);

        session(['last_confirmed_order' => $order->id]);

        $this->actingAs($order->customer)
            ->get(route('checkout.complete.show'))
            ->assertRedirect(route('customer.orders'));
    }

    #[Test]
    public function it_can_be_viewed_with_last_confirmed_order_and_expected_variables_drip(): void
    {
        $order = Order::factory()->create(['confirmed' => true]);

        $expected_scripts = '<script>var something = true;</script>';

        Setting::updateOrCreate(['key' =>'checkout_scripts'], ['value' => $expected_scripts]);

        session(['last_confirmed_order' => $order->id]);

        $opt_in_settings = [
            'name' => 'on_order_confirmation',
            'enabled' => true,
            'settings' => [
                [
                    'name' => 'message',
                    'value' => 'My checkout message'
                ]
            ]
        ];

        Integration::factory()->create([
            'name' => 'drip',
            'enabled' => true,
            'settings' => [
                'opt_in_locations' => [$opt_in_settings]
            ]
        ]);

        $this->actingAs($order->customer)
            ->get(route('checkout.complete.show'))
            ->assertOk()
            ->assertSessionMissing('last_confirmed_order')
            ->assertViewIs('theme::checkout.complete')
            ->assertViewHas('order', function ($arg) use ($order) {
                return $arg instanceof Order && $arg->id === $order->id;
            })
            ->assertViewHas('order', function ($arg) use ($order) {
                return $arg instanceof Order && $arg->id === $order->id;
            })
            ->assertViewHas('checkoutScripts', $expected_scripts)
            ->assertViewHas('sms_opt_in_settings', [
                'message'=> 'My checkout message'
            ]);
    }
}
