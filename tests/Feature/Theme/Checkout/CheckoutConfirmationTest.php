<?php

namespace Tests\Feature\Theme\Checkout;

use App\Models\Cart;
use App\Models\Date;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Payment;
use App\Models\Pickup;
use App\Models\Product;
use App\Models\RecurringOrder;
use App\Models\Schedule;
use App\Models\Setting;
use App\Models\User;
use App\Services\SubscriptionSettingsService;
use Inertia\Testing\AssertableInertia as Assert;
use Mockery\MockInterface;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class CheckoutConfirmationTest extends TenantTestCase
{
    #[Test]
    public function it_redirects_to_one_page_checkout_promotion_page_for_recurring_cart_without_valid_subscription(): void
    {
        Setting::updateOrCreate(['key' => 'require_address_at_checkout'], ['value' => 0]);
        Setting::updateOrCreate(['key' => 'require_checkout_agreement'], ['value' => 0]);
        Setting::updateOrCreate(['key' => 'one_page_checkout'], ['value' => 1]);

        $this->partialMock(SubscriptionSettingsService::class, function (MockInterface $mock) {
            $mock->shouldReceive('excludedProductIds')->andReturn(collect());
            $mock->shouldReceive('hasProductIncentive')->andReturnTrue();
        });

        $gateway = Payment::factory()->create();
        $pickup = Pickup::factory()->create(['schedule_id' => Schedule::factory()->hasDate()]);
        $user = User::factory()->create(['pickup_point' => $pickup->id]);
        $pickup->payment_methods = [$gateway->id];
        $pickup->save();

        $cart = Cart::factory()->create(['shopper_type' => User::class, 'shopper_id' => $user->id]);
        $cart->updateCartLocation($pickup);

        $cart->setCartAsSubscriptionPurchase();

        $product_one = Product::factory()->create();
        $cart->addProduct($product_one, 2);

        $product_two = Product::factory()->create();
        $cart->addProduct($product_two, 2);

        $this->actingAs($user)
            ->get(route('checkout.confirm.show'))
            ->assertRedirect(route('checkout.offers.show'));
    }

    #[Test]
    public function it_can_show_the_one_page_checkout_page(): void
    {
        Setting::updateOrCreate(['key' => 'require_address_at_checkout'], ['value' => 0]);
        Setting::updateOrCreate(['key' => 'require_checkout_agreement'], ['value' => 0]);
        Setting::updateOrCreate(['key' => 'one_page_checkout'], ['value' => 1]);

        $gateway = Payment::factory()->create();

        $pickup = Pickup::factory()->create(['schedule_id' => Schedule::factory()->hasDate()]);
        $user = User::factory()->create(['pickup_point' => $pickup->id]);
        $pickup->payment_methods = [$gateway->id];
        $pickup->save();

        $cart = Cart::factory()->create(['shopper_type' => User::class, 'shopper_id' => $user->id]);
        $cart->updateCartLocation($pickup);

        $product_one = Product::factory()->create();
        $cart->addProduct($product_one, 2);

        $product_two = Product::factory()->create();
        $cart->addProduct($product_two, 2);

        $this->actingAs($user)
            ->get(route('checkout.confirm.show'))
            ->assertOk()
            ->assertInertia(fn (Assert $page) => $page
                ->component('Checkout/Standard')
                ->has('cart')
                ->has('customer')
                ->has('checkout_settings')
                // HandleThemeInertiaMiddleware
                ->has('settings')
                ->has('auth.user')
            );
    }

    #[Test]
    public function a_user_must_be_authenticated_to_confirm_an_order(): void
    {
        $this->post(route('checkout.confirm.store'))
            ->assertRedirect(route('login'));
    }

    #[Test]
    public function a_user_cannot_confirm_that_has_no_items(): void
    {
        $cart = Cart::factory()->create();

        $this->actingAs($cart->cartCustomer())
            ->post(route('checkout.confirm.store'))
            ->assertRedirect(route('cart.show'));
    }

    #[Test]
    public function it_redirects_to_recurring_edit_page_when_user_has_a_blueprint(): void
    {
        $schedule = Schedule::factory()->create(['reorder_frequency' => ['7']]);
        Date::factory()->create(['schedule_id' => $schedule->id]);
        $pickup = Pickup::factory()->create(['schedule_id' => $schedule->id, 'min_customer_orders' => '100.01']);

        $user = User::factory()->create(['pickup_point' => $pickup->id]);
        $blueprint = RecurringOrder::factory()->create(['customer_id' => $user->id]);
        $order = Order::factory()->create([
            'blueprint_id' => $blueprint->id,
            'is_recurring' => true,
            'confirmed' => true,
            'customer_id' => $user->id,
            'pickup_id' => $user->pickup_point
        ]);

        $this->actingAs($user)
            ->get(route('cart.show'))
            ->assertRedirect(route('customer.orders.show', compact('order')));
    }

    #[Test]
    public function it_redirects_to_store_view_when_order_is_already_confirmed_and_order_modifications_are_not_allowed(): void
    {
        Setting::updateOrCreate(['key' => 'ordering_mode'], ['value' => false]);

        $schedule = Schedule::factory()->create(['reorder_frequency' => ['7']]);
        Date::factory()->create(['schedule_id' => $schedule->id]);
        $pickup = Pickup::factory()->create(['schedule_id' => $schedule->id, 'min_customer_orders' => '100.01']);

        $user = User::factory()->create(['pickup_point' => $pickup->id]);
        $order = Order::factory()->create([
            'customer_id' => $user->id,
            'pickup_id' => $user->pickup_point,
            'confirmed' => true,
        ]);

        OrderItem::factory()->create(['order_id' => $order->id]);

        $this->actingAs($user)
            ->get(route('checkout.confirm.show'))
            ->assertRedirect(route('store.index'));
    }

    #[Test]
    public function it_redirects_to_store_view_when_order_is_already_confirmed_and_order_pickup_date_has_passed(): void
    {
        Setting::updateOrCreate(['key' => 'ordering_mode'], ['value' => false]);

        $schedule = Schedule::factory()->create(['reorder_frequency' => ['7']]);
        Date::factory()->create(['schedule_id' => $schedule->id]);
        $pickup = Pickup::factory()->create(['schedule_id' => $schedule->id, 'min_customer_orders' => '100.01']);

        $user = User::factory()->create(['pickup_point' => $pickup->id]);
        $order = Order::factory()->create([
            'customer_id' => $user->id,
            'pickup_id' => $user->pickup_point,
            'confirmed' => true,
            'pickup_date' => now()->subDay(),
        ]);

        OrderItem::factory()->create(['order_id' => $order->id]);

        $this->actingAs($user)
            ->get(route('checkout.confirm.show'))
            ->assertRedirect(route('store.index'));
    }

    #[Test]
    public function it_redirects_to_checkout_address_view_when_setting_does_not_requires_address_at_checkout_for_pickup_location(): void
    {
        Setting::updateOrCreate(['key' => 'require_address_at_checkout'], ['value' => false]);

        $pickup = Pickup::factory()->create(['schedule_id' => Schedule::factory()->hasDate()]);
        $user = User::factory()->create(['pickup_point' => $pickup->id]);
        $cart = Cart::factory()->create(['shopper_type' => User::class, 'shopper_id' => $user->id]);
        $cart->updateCartLocation($pickup);

        $product = Product::factory()->create();
        $cart->addProduct($product);

        $this->actingAs($user)
            ->get(route('checkout.confirm.show'))
            ->assertSessionMissing('validate');
    }


    #[Test]
    public function it_redirects_to_store_when_attempting_to_view_checkout_without_products_in_order(): void
    {
        $user = User::factory()->create();

        $this->actingAs($user)
            ->get(route('checkout.confirm.show'))
            ->assertRedirect(route('store.index'))
            ->assertSessionHas('flash_notification', [
                'level' => 'error',
                'message' => 'You have not added any items to your cart yet.'
            ]);
    }

    protected function setUp(): void
    {
        parent::setUp();
        Setting::updateOrCreate(['key' => 'one_page_checkout'], ['value' => 0]);
        Setting::updateOrCreate(['key' => 'cart_service'], ['value' => 'order']);
    }
}
