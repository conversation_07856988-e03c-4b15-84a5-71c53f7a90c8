<?php

namespace Tests\Feature\Theme\Store;

use App\Events\Order\OrderWasPaid;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Payment;
use App\Models\Pickup;
use App\Models\Product;
use App\Models\RecurringOrder;
use App\Models\RecurringOrderItem;
use App\Models\Setting;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\Event;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class OrderTest extends TenantTestCase
{
    #[Test]
    public function test_order_can_be_charged(): void
    {
        Event::fake([OrderWasPaid::class]);

        $referralBonusGlobal = 1000;
        $referralBonusUser = 500;

        Setting::updateOrCreate(['key' => 'stripe_user_id'], ['value' => 'acct_1AuksMDqNIWkMQK2']);
        Setting::updateOrCreate(['key' => 'referral_bonus'], ['value' => $referralBonusGlobal]);

        $referrer = User::factory()->create([
            'credit' => 0,
            'referral_bonus' => $referralBonusUser
        ]);

        $customer = User::factory()->create([
            'customer_id' => 'cus_Bt8KTpKPi2cTqQ',
            'referral_user_id' => $referrer->id
        ]);

        $orderTotal = 1599;

        $order = Order::factory()->create([
            'customer_id' => $customer->id,
            'first_time_order' => true,
            'paid' => false,
            'confirmed' => true,
            'total' => $orderTotal,
            'customer_email' => $customer->email,
            'customer_first_name' => $customer->first_name,
            'customer_last_name' => $customer->last_name,
            'customer_phone' => $customer->phone,
            'payment_id' => Payment::where('key', 'card')->first()->id
        ]);

        OrderItem::factory()->create([
            'order_id' => $order->id,
            'product_id' => Product::factory()->create(),
            'unit_price' => $orderTotal,
            'qty' => 1,
            'subtotal' => $orderTotal,
        ]);

        $user = User::factory()->admin()->create();

        $this->actingAs($user)
            ->post('/admin/orders/' . $order->id . '/payments', [
                'amount' => money($orderTotal, ''),
                'full_charge' => true
            ]);

        $this->assertDatabaseHas('orders', [
            'id' => $order->id,
            'paid' => 1
        ]);

        $this->assertDatabaseHas('order_payments', [
            'order_id' => $order->id,
            'payment_type_id' => 1,
            'customer_id' => $order->customer_id,
            'amount' => $orderTotal
        ]);

        Event::assertDispatched(OrderWasPaid::class, function (OrderWasPaid $event) use ($order) {
            return $event->order->id === $order->id;
        });
    }

    public function test_taxable_products_subtotal_is_calculated(): void
    {
        // When an order has taxable products.
        $customer = User::factory()->create();

        $pickupLocation = Pickup::factory()->create([
            'tax_rate' => 0.070
        ]);

        $order = Order::factory()->create([
            'customer_id' => $customer->id,
            'first_time_order' => true,
            'paid' => false,
            'confirmed' => true,
            'pickup_id' => $pickupLocation->id,
            'order_discount' => 1000
        ]);

        Product::factory()->count(5)->create([
            'taxable' => true
        ])->each(function ($product) use ($order) {
            $qty = rand(1, 10);
            OrderItem::factory()->count(5)->create([
                'product_id' => $product->id,
                'title' => $product->title,
                'unit_of_issue' => $product->unit_of_issue,
                'unit_price' => $product->unit_price,
                'qty' => $qty,
                'weight' => $product->weight,
                'subtotal' => $product->unit_price * $qty,
                'order_id' => $order->id,
                'taxable' => false
            ]);
        });

        Product::factory()->count(5)->create([
            'taxable' => false
        ])->each(function ($product) use ($order) {
            $qty = rand(1, 10);
            OrderItem::factory()->count(5)->create([
                'product_id' => $product->id,
                'title' => $product->title,
                'unit_of_issue' => $product->unit_of_issue,
                'unit_price' => $product->unit_price,
                'qty' => $qty,
                'weight' => $product->weight,
                'subtotal' => $product->unit_price * $qty,
                'order_id' => $order->id,
                'taxable' => false
            ]);
        });

        $order = Order::with(['items.product'])->where('id', $order->id)->first();

        $taxableSubtotal = $order->items->filter(function ($item) {
            return $item->product->taxable;
        })->sum('subtotal');

        $noneTaxableSubtotal = $order->items->filter(function ($item) {
            return !$item->product->taxable;
        })->sum('subtotal');

        $order->updateTotals();

        $this->assertEquals($order->subtotal, $taxableSubtotal + $noneTaxableSubtotal);

        $totalTax = round($taxableSubtotal * $pickupLocation->tax_rate, 0, PHP_ROUND_HALF_UP);
        $this->assertEquals($order->tax, $totalTax);

        $this->assertEquals($order->subtotal - $order->order_discount + $order->tax, $order->total);
    }

    #[Test]
    public function it_updates_recurring_blueprint_when_incentive_is_updated(): void
    {
        Carbon::setTestNow(now());

        $promo_product_a = Product::factory()->create();
        $promo_product_b = Product::factory()->create();

        Setting::updateOrCreate(['key' => 'recurring_orders_default_item'], ['value' => $promo_product_a->id]);
        Setting::updateOrCreate(['key' => 'recurring_orders_option_one'], ['value' => $promo_product_b->id]);

        $blueprint = RecurringOrder::factory()->create();
        RecurringOrderItem::factory()->create([
            'order_id' => $blueprint->id,
            'product_id' => $promo_product_a->id,
            'type' => 'promo'
        ]);

        $order = Order::factory()->create([
            'customer_id' => $blueprint->customer_id,
            'is_recurring' => true,
            'blueprint_id' => $blueprint->id,
            'pickup_date' => today()->addDay(),
            'deadline_date' => today(),
        ]);

        $this->actingAs($order->customer)
            ->put(route('customers.promo-item.update', ['promo_item' => $promo_product_b]))
            ->assertSessionDoesntHaveErrors();

        $this->assertDatabaseHas(RecurringOrderItem::class, [
            'product_id' => $promo_product_b->id,
            'order_id' => $blueprint->id,
            'type' => 'promo',
        ]);

        $this->assertDatabaseMissing(RecurringOrderItem::class, [
            'product_id' => $promo_product_a->id,
            'order_id' => $blueprint->id,
            'type' => 'promo',
        ]);

        Carbon::setTestNow();
    }
}
