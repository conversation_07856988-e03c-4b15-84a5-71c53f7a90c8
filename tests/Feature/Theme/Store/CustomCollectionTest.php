<?php

namespace Tests\Feature\Theme\Store;

use App\Models\BundleProduct;
use App\Models\Cart;
use App\Models\Collection;
use App\Models\Order;
use App\Models\Product;
use App\Models\ProductPrice;
use App\Models\ProductPriceGroup;
use App\Models\Setting;
use App\Models\Tag;
use App\Models\User;
use App\Models\Vendor;
use Illuminate\Database\Eloquent\Collection as EloquentCollection;
use Illuminate\Pagination\Paginator;
use Illuminate\Support\Facades\DB;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class CustomCollectionTest extends TenantTestCase
{
    public function setup(): void
    {
        parent::setup();

        // Disable the ZipGate feature.
        Setting::updateOrCreate(['key' => 'require_shopper_local'],['value' => false]);
    }

    #[Test]
    public function it_can_load_the_configured_store_page_title(): void
    {
        $collection = Collection::factory()->create([
            'page_title' => 'Custom Collection Page Title',
        ]);

        Setting::updateOrCreate(['key' => 'store_page_title'], ['value' => 'Custom Title']);
        Setting::updateOrCreate(['key' => 'store_default_collection'], ['value' =>  $collection->id]);

        $this->get(route('store.index'))
            ->assertOk()
            ->assertViewHas('pageTitle', 'Custom Collection Page Title');
    }

    #[Test]
    public function it_loads_the_store_index_page_variables(): void
    {
        $collection = Collection::factory()->create([
            'slug' => 'my-collection-slug',
            'cover_photo' => 'https://some.image',
            'description' => 'My custom description',
            'canonical_url' => 'https://some.url',
            'page_title' => 'Custom Page Title',
            'settings' => [
                'display_name' => 'Page Heading'
            ]
        ]);

        Setting::updateOrCreate(['key' => 'store_default_collection'], ['value' => $collection->id]);

        $products = Product::factory()->count(2)->create()
            ->each(function (Product $product) use ($collection) {
                $product->collections()->attach($collection->id);
            });

        // products not in collection
        Product::factory()->count(2)->create();

        Tag::factory()->count(2)->create()
            ->each(function (Tag $tag) use ($products) {
                $products->each(function (Product $product) use ($tag) {
                    $product->tags()->attach($tag->id);
                });
            });

        $expected_tags = Tag::select(['slug', 'title'])->get();

        $this->get(route('store.index'))
            ->assertOk()
            ->assertViewIs('theme::store.index')
            ->assertViewHas('pageTitle', 'Custom Page Title')
            ->assertViewHas('heading', 'Page Heading')
            ->assertViewHas('subheading', 'My custom description')
            ->assertViewHas('pageDescription', 'My custom description')
            ->assertViewHas('headerPhoto', 'https://some.image')
            ->assertViewHas('tags', $expected_tags)
            ->assertViewHas('pageCanonical', $collection->getCanonicalUrl())
            ->assertViewHas('products');
    }

    #[Test]
    public function it_loads_the_expected_products_ordered_by_title_asc(): void
    {
        $collection = Collection::factory()->create();
        Setting::updateOrCreate(['key' => 'store_default_collection'], ['value' => $collection->id]);

        $product_one = Product::factory()->create(['title' => 'b']);
        $product_one->collections()->attach($collection->id);
        $product_two = Product::factory()->create(['title' => 'a']);
        $product_two->collections()->attach($collection->id);

        $this->get(route('store.index'))
            ->assertOk()
            ->assertViewHas('products', function ($value) use ($product_one, $product_two) {
                return $value instanceof Paginator
                    && $value->count() === 2
                    && $value[0]->id = $product_two->id
                        && $value[1]->id = $product_one->id;
            })
            ->assertSee($product_one->title)
            ->assertSee($product_two->title);;
    }

    #[Test]
    public function it_loads_the_expected_product_bundle_relation(): void
    {
        $collection = Collection::factory()->create();
        Setting::updateOrCreate(['key' => 'store_default_collection'], ['value' => $collection->id]);

        $bundle_product = Product::factory()->create(['title' => 'bundle of something']);
        $bundle_product->collections()->attach($collection->id);

        $product_in_bundle = Product::factory()->create(['title' => 'single product']);
        BundleProduct::factory()->create([
            'bundle_id' => $bundle_product->id,
            'product_id' => $product_in_bundle->id
        ]);

        $this->get(route('store.index'))
            ->assertOk()
            ->assertViewHas('products', function ($value) use ($bundle_product, $product_in_bundle) {
                return $value[0] instanceof Product
                    && $value[0]->id === $bundle_product->id
                    && $value[0]->relationLoaded('bundle')
                    && ! is_null($value[0]->bundle)
                    && $value[0]->bundle instanceof EloquentCollection
                    && $value[0]->bundle->count() === 1
                    && $value[0]->bundle->first() instanceof Product
                    && $value[0]->bundle->first()->id = $product_in_bundle->id;
            });
    }

    #[Test]
    public function it_loads_the_expected_product_vendor_relation(): void
    {
        $collection = Collection::factory()->create();
        Setting::updateOrCreate(['key' => 'store_default_collection'], ['value' => $collection->id]);

        $vendor = Vendor::factory()->create();
        $product = Product::factory()->create(['vendor_id' => $vendor->id]);
        $product->collections()->attach($collection->id);

        $this->get(route('store.index'))
            ->assertOk()
            ->assertViewHas('products', function ($value) use ($product, $vendor) {
                return $value[0] instanceof Product
                    && $value[0]->id === $product->id
                    && $value[0]->relationLoaded('vendor')
                    && ! is_null($value[0]->vendor)
                    && $value[0]->vendor instanceof Vendor
                    && $value[0]->vendor->id === $vendor->id
                    && count($value[0]->vendor->toArray()) === 3
                    && count(array_diff(array_keys($value[0]->vendor->toArray()), ['id', 'title', 'slug'])) === 0;
            });
    }

    #[Test]
    public function it_loads_the_expected_product_price_relation_when_customer_has_no_price_group(): void
    {
        $collection = Collection::factory()->create();
        Setting::updateOrCreate(['key' => 'store_default_collection'], ['value' => $collection->id]);

        $product = Product::factory()->create();
        $product->collections()->attach($collection->id);

        $price_group = ProductPriceGroup::factory()->create();
        ProductPrice::factory()->create(['product_id' => $product->id, 'group_id' => $price_group->id]);

        $this->get(route('store.index'))
            ->assertOk()
            ->assertViewHas('products', function ($value) use ($product) {
                return $value[0] instanceof Product
                    && $value[0]->id === $product->id
                    && $value[0]->relationLoaded('price')
                    && is_null($value[0]->price);
            });
    }

    #[Test]
    public function it_loads_the_expected_product_price_relation_when_customer_has_a_price_group(): void
    {
        $collection = Collection::factory()->create();
        Setting::updateOrCreate(['key' => 'store_default_collection'], ['value' => $collection->id]);

        $product = Product::factory()->create();
        $product->collections()->attach($collection->id);

        $price_group = ProductPriceGroup::factory()->create();
        $customer = User::factory()->create(['pricing_group_id' => $price_group->id]);

        // set the current order for customer
        $order = Order::factory()->create(['customer_id' => $customer->id, 'pickup_id' => $customer->pickup_point]);
        

        $product_price = ProductPrice::factory()->create(['product_id' => $product->id, 'group_id' => $price_group->id]);

        $this->actingAs($customer)
            ->get(route('store.index'))
            ->assertOk()
            ->assertViewHas('products', function ($value) use ($product, $product_price) {
                return $value[0] instanceof Product
                    && $value[0]->id === $product->id
                    && $value[0]->relationLoaded('price')
                    && ! is_null($value[0]->price)
                    && $value[0]->price instanceof ProductPrice
                    && $value[0]->price->id === $product_price->id;
            });
    }

    #[Test]
    public function it_does_not_load_the_product_price_relation_when_customer_has_an_unknown_price_group(): void
    {
        $collection = Collection::factory()->create();
        Setting::updateOrCreate(['key' => 'store_default_collection'], ['value' => $collection->id]);

        $product = Product::factory()->create();
        $product->collections()->attach($collection->id);

        $price_group = ProductPriceGroup::factory()->create();
        $customer = User::factory()->create(['pricing_group_id' => 0]);

        // set the current order for customer
        $order = Order::factory()->create(['customer_id' => $customer->id, 'pickup_id' => $customer->pickup_point]);
        

        ProductPrice::factory()->create(['product_id' => $product->id, 'group_id' => $price_group->id]);

        $this->actingAs($customer)
            ->get(route('store.index'))
            ->assertOk()
            ->assertViewHas('products', function ($value) use ($product) {
                return $value[0] instanceof Product
                    && $value[0]->id === $product->id
                    && $value[0]->relationLoaded('price')
                    && is_null($value[0]->price);
            });
    }

    #[Test]
    public function it_loads_expected_products_ordered_by_custom_sort(): void
    {
        $collection = Collection::factory()->create();
        Setting::updateOrCreate(['key' => 'store_default_collection'], ['value' => $collection->id]);

        Setting::factory()->create(['key' => 'store_sort_order', 'value' => 'sku-desc']);

        $product_one = Product::factory()->create(['title' => 'b', 'sku' => 'a1']);
        $product_one->collections()->attach($collection->id);
        $product_two = Product::factory()->create(['title' => 'a', 'sku' => 'b2']);
        $product_two->collections()->attach($collection->id);


        $this->get(route('store.index'))
            ->assertOk()
            ->assertViewHas('products', function ($value) use ($product_one, $product_two) {
                return $value instanceof Paginator
                    && $value->count() === 2
                    && $value[0]->id = $product_one->id
                    && $value[1]->id = $product_one->id;
            });
    }

    #[Test]
    public function it_loads_expected_products_ordered_by_title_asc_when_custom_sort_is_unknown(): void
    {
        $collection = Collection::factory()->create();
        Setting::updateOrCreate(['key' => 'store_default_collection'], ['value' => $collection->id]);

        Setting::factory()->create(['key' => 'store_sort_order', 'value' => 'skudesc']);

        $product_one = Product::factory()->create(['title' => 'b', 'sku' => 'a1']);
        $product_one->collections()->attach($collection->id);
        $product_two = Product::factory()->create(['title' => 'a', 'sku' => 'b2']);
        $product_two->collections()->attach($collection->id);

        $this->get(route('store.index'))
            ->assertOk()
            ->assertViewHas('products', function ($value) use ($product_one, $product_two) {
                return $value instanceof Paginator
                    && $value->count() === 2
                    && $value[0]->id = $product_two->id
                        && $value[1]->id = $product_one->id;
            });
    }

    #[Test]
    public function it_loads_the_expected_product_variants_relation(): void
    {
        $collection = Collection::factory()->create();
        Setting::updateOrCreate(['key' => 'store_default_collection'], ['value' => $collection->id]);

        $product = Product::factory()->create(['title' => 'a']);
        $product->collections()->attach($collection->id);

        $variant_product = Product::factory()->create(['title' => 'b']);
        $variant_product->collections()->attach($collection->id);

        $product->variants()->attach($variant_product->id);

        $this->get(route('store.index'))
            ->assertOk()
            ->assertViewHas('products', function ($value) use ($product, $variant_product) {
                return $value instanceof Paginator
                    && $value->count() === 2
                    && $value[0] instanceof Product
                    && $value[0]->id === $product->id
                    && $value[0]->relationLoaded('variants')
                    && ! is_null($value[0]->variants)
                    && $value[0]->variants instanceof EloquentCollection
                    && $value[0]->variants->count() === 1
                    && $value[0]->variants->first() instanceof Product
                    && $value[0]->variants->first()->id === $variant_product->id;
            });
    }

    #[Test]
    public function it_does_not_load_products_variants_excluded_by_order_pickup_location(): void
    {
        $collection = Collection::factory()->create();
        Setting::updateOrCreate(['key' => 'store_default_collection'], ['value' => $collection->id]);

        $customer = User::factory()->create();

        $cart = Cart::factory()->create(['shopper_type' => User::class, 'shopper_id' => $customer->id]);
        $cart->updateCartLocation($customer->pickup);

        $product = Product::factory()->create();
        $product->collections()->attach($collection->id);

        $variant_product = Product::factory()->create();
        $variant_product->collections()->attach($collection->id);

        $product->variants()->attach($variant_product->id);
        $customer->pickup->products()->attach($variant_product->id);

        $this->actingAs($customer)
            ->get(route('store.index'))
            ->assertOk()
            ->assertViewHas('products', function ($value) use ($product) {
                return $value instanceof Paginator
                    && $value->count() === 1
                    && $value[0] instanceof Product
                    && $value[0]->id === $product->id
                    && $value[0]->relationLoaded('variants')
                    && ! is_null($value[0]->variants)
                    && $value[0]->variants instanceof EloquentCollection
                    && $value[0]->variants->count() === 0;
            });
    }

    #[Test]
    public function it_loads_the_expected_product_variants_price_group_relation(): void
    {
        $collection = Collection::factory()->create();
        Setting::updateOrCreate(['key' => 'store_default_collection'], ['value' => $collection->id]);

        $product = Product::factory()->create(['title' => 'a']);
        $product->collections()->attach($collection->id);

        $variant_product = Product::factory()->create(['title' => 'b']);
        $variant_product->collections()->attach($collection->id);

        $product->variants()->attach($variant_product->id);

        $price_group = ProductPriceGroup::factory()->create();
        $customer = User::factory()->create(['pricing_group_id' => $price_group->id]);

        // set the current order for customer
        $order = Order::factory()->create(['customer_id' => $customer->id, 'pickup_id' => $customer->pickup_point]);
        

        $product_price = ProductPrice::factory()->create(['product_id' => $variant_product->id, 'group_id' => $price_group->id]);

        $this->actingAs($customer)
            ->get(route('store.index'))
            ->assertOk()
            ->assertViewHas('products', function ($value) use ($product, $variant_product, $product_price) {
                return $value instanceof Paginator
                    && $value->count() === 2
                    && $value[0] instanceof Product
                    && $value[0]->id === $product->id
                    && $value[0]->relationLoaded('variants')
                    && ! is_null($value[0]->variants)
                    && $value[0]->variants instanceof EloquentCollection
                    && $value[0]->variants->count() === 1
                    && $value[0]->variants->first() instanceof Product
                    && $value[0]->variants->first()->id === $variant_product->id
                    && $value[0]->variants->first()->relationLoaded('price')
                    && ! is_null($value[0]->variants->first()->price)
                    && $value[0]->variants->first()->price instanceof ProductPrice
                    && $value[0]->variants->first()->price->id === $product_price->id;
            });
    }

    #[Test]
    public function it_does_not_load_products_when_not_visible(): void
    {
        $collection = Collection::factory()->create();
        Setting::updateOrCreate(['key' => 'store_default_collection'], ['value' => $collection->id]);

        $product_one = Product::factory()->create(['visible' => false]);
        $product_one->collections()->attach($collection->id);
        $product_two = Product::factory()->create(['visible' => true]);
        $product_two->collections()->attach($collection->id);


        $this->get(route('store.index'))
            ->assertOk()
            ->assertViewHas('products', function ($value) use ($product_one, $product_two) {
                return $value instanceof Paginator
                    && $value->count() === 1
                    && $value[0]->id = $product_two->id;
            });
    }

    #[Test]
    public function it_loads_the_expected_visible_products_and_their_expected_invisible_variants(): void
    {
        $collection = Collection::factory()->create();
        Setting::updateOrCreate(['key' => 'store_default_collection'], ['value' => $collection->id]);

        $product = Product::factory()->create(['title' => 'a', 'visible' => true]);
        $product->collections()->attach($collection->id);
        $variant_product = Product::factory()->create(['title' => 'b', 'visible' => false]);
        $variant_product->collections()->attach($collection->id);
        $product->variants()->attach($variant_product->id);

        $this->get(route('store.index'))
            ->assertOk()
            ->assertViewHas('products', function ($value) use ($product, $variant_product) {
                return $value instanceof Paginator
                    && $value->count() === 1
                    && $value[0] instanceof Product
                    && $value[0]->id === $product->id
                    && $value[0]->relationLoaded('variants')
                    && ! is_null($value[0]->variants)
                    && $value[0]->variants instanceof EloquentCollection
                    && $value[0]->variants->count() === 1
                    && $value[0]->variants->first() instanceof Product
                    && $value[0]->variants->first()->id === $variant_product->id;
            });
    }

    #[Test]
    public function it_does_not_load_products_when_they_are_hidden_from_search(): void
    {
        // Full text searching doesn't work inside a database transaction.
        // Need to commit started transaction and truncate manually after test
        DB::commit();

        $collection = Collection::factory()->create();
        Setting::updateOrCreate(['key' => 'store_default_collection'], ['value' => $collection->id]);

        $product_one = Product::factory()->create(['title' => 'abcd', 'hide_from_search' => true]);
        $product_one->collections()->attach($collection->id);

        $product_two = Product::factory()->create(['title' => 'abcd', 'hide_from_search' => false]);
        $product_two->collections()->attach($collection->id);

        $this->get(route('store.index', ['q' => 'abcd']))
            ->assertOk()
            ->assertViewHas('products', function ($value) use ($product_two) {
                return $value instanceof Paginator
                    && $value->count() === 1
                    && $value[0]->id === $product_two->id;
            });

        DB::statement('SET FOREIGN_KEY_CHECKS=0;');
        Product::truncate();
        Collection::truncate();
        DB::table('collection_product')->truncate();
        DB::statement('SET FOREIGN_KEY_CHECKS=1;');
    }

    #[Test]
    public function it_filters_down_to_sale_products_when_sale_term_is_used(): void
    {
        config(['grazecart.sale_keywords' => ['saleterm', 'anotherterm']]);

        $collection = Collection::factory()->create();
        Setting::updateOrCreate(['key' => 'store_default_collection'], ['value' => $collection->id]);

        $product_one = Product::factory()->create(['sale' => false]);
        $product_one->collections()->attach($collection->id);

        $product_two = Product::factory()->create(['sale' => true]);
        $product_two->collections()->attach($collection->id);


        $this->get(route('store.index', ['q' => 'saleterm']))
            ->assertOk()
            ->assertViewHas('products', function ($value) use ($product_two) {
                return $value instanceof Paginator
                    && $value->count() === 1
                    && $value[0]->id === $product_two->id;
            });

        $this->get(route('store.index', ['q' => 'anotherterm']))
            ->assertOk()
            ->assertViewHas('products', function ($value) use ($product_two) {
                return $value instanceof Paginator
                    && $value->count() === 1
                    && $value[0]->id === $product_two->id;
            });
    }

    #[Test]
    public function it_rank_filters_by_search_term(): void
    {
        // Full text searching doesn't work inside a database transaction.
        // Need to commit started transaction and truncate manually after test
        DB::commit();

        $collection = Collection::factory()->create();
        Setting::updateOrCreate(['key' => 'store_default_collection'], ['value' => $collection->id]);

        $product_one = Product::factory()->create(['title' => 'tenderloin', 'keywords' => 'shoulder']);
        $product_one->collections()->attach($collection->id);

        $product_two = Product::factory()->create(['title' => 'shoulder']);
        $product_two->collections()->attach($collection->id);

        $product_three = Product::factory()->create(['title' => 'lettuce']);
        $product_three->collections()->attach($collection->id);

        $this->get(route('store.index', ['q' => 'shoulder']))
            ->assertOk()
            ->assertViewHas('products', function ($value) use ($product_one, $product_two) {
                return $value instanceof Paginator
                    && $value->count() === 2
                    && $value[0]->id === $product_two->id
                    && $value[1]->id === $product_one->id;
            });

        DB::statement('SET FOREIGN_KEY_CHECKS=0;');
        Product::truncate();
        Collection::truncate();
        DB::table('collection_product')->truncate();
        DB::statement('SET FOREIGN_KEY_CHECKS=1;');
    }
}
