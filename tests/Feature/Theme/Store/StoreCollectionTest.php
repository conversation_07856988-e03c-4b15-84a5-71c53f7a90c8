<?php

namespace Tests\Feature\Theme\Store;

use App\Models\Collection;
use App\Models\Product;
use App\Support\Enums\ProductType;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class StoreCollectionTest extends TenantTestCase
{
    #[Test]
    public function it_redirects_to_store_if_collection_does_not_exist(): void
    {
        $this->get(route('store.collections.show', ['does-not-exist']))
            ->assertRedirect(route('store.index'));
    }

    #[Test]
    public function it_loads_the_expected_products_within_a_collection(): void
    {
        $collection = Collection::factory()->create(['slug' => 'demo']);

        $product_one = Product::factory()->create(['title' => 'Product A']);
        $product_one->collections()->attach($collection->id);
        $product_two = Product::factory()->create(['title' => 'Product B']);
        $product_two->collections()->attach($collection->id);
        $product_three = Product::factory()->create(['title' => 'Product C']);

        $this->get(route('store.collections.show', [$collection->slug]))
            ->assertOk()
            ->assertSee($product_one->title)
            ->assertSee($product_two->title)
            ->assertDontSee($product_three->title);
    }

    #[Test]
    public function it_loads_the_expected_products_within_a_collection_when_title_is_numerical(): void
    {
        $collection = Collection::factory()->create(['slug' => '2024']);

        $product_one = Product::factory()->create(['title' => 'Product A']);
        $product_one->collections()->attach($collection->id);
        $product_two = Product::factory()->create(['title' => 'Product B']);
        $product_two->collections()->attach($collection->id);
        $product_three = Product::factory()->create(['title' => 'Product C']);

        $this->get(route('store.collections.show', [$collection->slug]))
            ->assertOk()
            ->assertSee($product_one->title)
            ->assertSee($product_two->title)
            ->assertDontSee($product_three->title);
    }

    #[Test]
    public function it_does_not_load_pre_order_in_a_collection(): void
    {
        $collection = Collection::factory()->create(['slug' => 'demo']);

        $product_one = Product::factory()->create(['title' => 'Product A']);
        $product_one->collections()->attach($collection->id);
        $product_two = Product::factory()->create(['title' => 'Product B']);
        $product_two->collections()->attach($collection->id);
        $product_three = Product::factory()->create([
            'title' => 'Product C',
            'type_id' => ProductType::PREORDER->value
        ]);
        $product_three->collections()->attach($collection->id);

        $this->get(route('store.collections.show', [$collection->slug]))
            ->assertOk()
            ->assertSee($product_one->title)
            ->assertSee($product_two->title)
            ->assertDontSee($product_three->title);
    }
}
