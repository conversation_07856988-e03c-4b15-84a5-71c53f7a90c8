<?php

namespace Tests\Feature\Theme\Store;

use App\Models\Order;
use App\Models\Product;
use App\Models\ProductPrice;
use App\Models\ProductPriceGroup;
use App\Models\User;
use App\Support\Enums\ProductType;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class StoreTest extends TenantTestCase
{
    #[Test]
    public function store_does_not_load_pre_order_products(): void
    {
        $user = User::factory()->create();

        Product::factory()->create([
            'unit_price' => 9.50,
            'unit_of_issue' => 'package',
            'type_id' => ProductType::PREORDER->value
        ]);

        // Given a pricing group is set on a product and location
        $this->actingAs($user)
            ->get(route('store.index'))
            ->assertOk()
            ->assertDontSee('9.50');
    }

    #[Test]
    public function store_shows_retail_package_price_when_not_on_sale(): void
    {
        $user = User::factory()->create();

        Product::factory()->create([
            'unit_price' => 9.50,
            'unit_of_issue' => 'package',
            'sale' => false,
            'sale_unit_price' => 8.50,
        ]);

        // Given a pricing group is set on a product and location
        $this->actingAs($user)
            ->get(route('store.index'))
            ->assertOk()
            ->assertSee('9.50')
            ->assertDontSee('8.50');
    }

    #[Test]
    public function store_shows_retail_package_sale_price_when_on_sale(): void
    {
        $user = User::factory()->create();

        Product::factory()->create([
            'unit_price' => 9.50,
            'unit_of_issue' => 'package',
            'sale' => true,
            'sale_unit_price' => 8.50,
        ]);

        // Given a pricing group is set on a product and location
        $this->actingAs($user)
            ->get(route('store.index'))
            ->assertOk()
            ->assertSee('9.50')
            ->assertSee('8.50');
    }

    #[Test]
    public function store_shows_package_price_decrease_when_user_has_decrease_price_group(): void
    {
        $product = Product::factory()->create([
            'unit_price' => 10.00,
            'unit_of_issue' => 'package',
            'sale' => false,
            'sale_unit_price' => 8.50,
        ]);

        $price_group = ProductPriceGroup::factory()->create([
            'type' => ProductPriceGroup::PERCENTAGE_DECREASE,
            'amount' => 10 // 10 percent decrease
        ]);

        $user = User::factory()->create(['pricing_group_id' => $price_group->id]);

        // set the current order for customer
        $order = Order::factory()->create(['customer_id' => $user->id, 'pickup_id' => $user->pickup_point]);
        

        ProductPrice::factory()->create(['product_id' => $product->id, 'group_id' => $price_group->id]);

        // Given a pricing group is set on a product and location
        $this->actingAs($user)
            ->get(route('store.index'))
            ->assertOk()
            ->assertDontSee('10.00') // regular unit price
            ->assertDontSee('8.50') // sale unit price
            ->assertSee('9.00') // regular unit price - discount
            ->assertDontSee('7.65'); // sale unit price - discount
    }

    #[Test]
    public function store_shows_package_sale_price_decrease_when_on_sale_and_user_has_decrease_price_group(): void
    {
        $product = Product::factory()->create([
            'unit_price' => 11.00,
            'unit_of_issue' => 'package',
            'sale' => true,
            'sale_unit_price' => 10.00,
        ]);

        $price_group = ProductPriceGroup::factory()->create([
            'type' => ProductPriceGroup::PERCENTAGE_DECREASE,
            'amount' => 10 // 10 percent decrease
        ]);

        $user = User::factory()->create(['pricing_group_id' => $price_group->id]);

        // set the current order for customer
        $order = Order::factory()->create(['customer_id' => $user->id, 'pickup_id' => $user->pickup_point]);
        

        ProductPrice::factory()->create(['product_id' => $product->id, 'group_id' => $price_group->id]);

        // Given a pricing group is set on a product and location
        $this->actingAs($user)
            ->get(route('store.index'))
            ->assertOk()
            ->assertDontSee('11.00') // regular unit price
            ->assertDontSee('10.00') // sale unit price
            ->assertSee('9.90') // regular unit price - discount
            ->assertSee('9.00'); // sale unit price - discount
    }

    #[Test]
    public function store_shows_package_price_increase_when_user_has_increase_price_group_and_no_sale(): void
    {
        $product = Product::factory()->create([
            'unit_price' => 11.00,
            'unit_of_issue' => 'package',
            'sale' => false,
            'sale_unit_price' => 10.00,
        ]);

        $price_group = ProductPriceGroup::factory()->create([
            'type' => ProductPriceGroup::PERCENTAGE_INCREASE,
            'amount' => 10 // 10 percent decrease
        ]);

        $user = User::factory()->create(['pricing_group_id' => $price_group->id]);

        // set the current order for customer
        $order = Order::factory()->create(['customer_id' => $user->id, 'pickup_id' => $user->pickup_point]);
        

        ProductPrice::factory()->create(['product_id' => $product->id, 'group_id' => $price_group->id]);

        // Given a pricing group is set on a product and location
        $this->actingAs($user)
            ->get(route('store.index'))
            ->assertOk()
            ->assertDontSee('11.00') // regular unit price
            ->assertDontSee('10.00') // sale unit price
            ->assertSee('12.10') // regular unit price + increase
            ->assertDontSee('11.00'); // sale unit price + increase
    }

    #[Test]
    public function store_shows_package_price_increase_when_user_has_increase_price_group_and_product_has_sale(): void
    {
        $product = Product::factory()->create([
            'unit_price' => 12.00,
            'unit_of_issue' => 'package',
            'sale' => true,
            'sale_unit_price' => 10.00,
        ]);

        $price_group = ProductPriceGroup::factory()->create([
            'type' => ProductPriceGroup::PERCENTAGE_INCREASE,
            'amount' => 10 // 10 percent decrease
        ]);

        $user = User::factory()->create(['pricing_group_id' => $price_group->id]);

        // set the current order for customer
        $order = Order::factory()->create(['customer_id' => $user->id, 'pickup_id' => $user->pickup_point]);
        

        ProductPrice::factory()->create(['product_id' => $product->id, 'group_id' => $price_group->id]);

        // Given a pricing group is set on a product and location
        $this->actingAs($user)
            ->get(route('store.index'))
            ->assertOk()
            ->assertDontSee('12.00') // regular unit price
            ->assertDontSee('10.00') // sale unit price
            ->assertSee('13.20') // regular unit price + increase
            ->assertSee('11.00'); // sale unit price + increase
    }

    #[Test]
    public function store_shows_package_fixed_price_when_user_has_fixed_price_group(): void
    {
        $product = Product::factory()->create([
            'unit_price' => 10.00,
            'unit_of_issue' => 'package',
            'sale' => false,
            'sale_unit_price' => 8.00,
        ]);

        $price_group = ProductPriceGroup::factory()->create([
            'type' => ProductPriceGroup::FIXED,
            'amount' => 12
        ]);

        $user = User::factory()->create(['pricing_group_id' => $price_group->id]);

        // set the current order for customer
        $order = Order::factory()->create(['customer_id' => $user->id, 'pickup_id' => $user->pickup_point]);
        

        ProductPrice::factory()->create([
            'product_id' => $product->id,
            'group_id' => $price_group->id,
            'unit_price' => 1500
        ]);

        // Given a pricing group is set on a product and location
        $this->actingAs($user)
            ->get(route('store.index'))
            ->assertOk()
            ->assertDontSee('10.00')
            ->assertDontSee('8.00')
            ->assertSee('15.00');
    }

    #[Test]
    public function store_shows_package_fixed_price_when_user_has_fixed_price_group_and_product_on_sale(): void
    {
        $product = Product::factory()->create([
            'unit_price' => 10.21,
            'unit_of_issue' => 'package',
            'sale' => true,
            'sale_unit_price' => 8.51,
        ]);

        $price_group = ProductPriceGroup::factory()->create([
            'type' => ProductPriceGroup::FIXED,
            'amount' => 12
        ]);

        $user = User::factory()->create(['pricing_group_id' => $price_group->id]);

        ProductPrice::factory()->create([
            'product_id' => $product->id,
            'group_id' => $price_group->id,
            'unit_price' => 1502
        ]);

        // Given a pricing group is set on a product and location
        $this->actingAs($user)
            ->get(route('store.index'))
            ->assertOk()
            ->assertDontSee('10.21')
            ->assertDontSee('8.51')
            ->assertSee('15.02');
    }
}
