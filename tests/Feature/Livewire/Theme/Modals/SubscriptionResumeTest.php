<?php

namespace Tests\Feature\Livewire\Theme\Modals;

use App\Events\Subscription\SubscriptionWasResumed;
use App\Livewire\Theme\Modals\SubscriptionResume;
use App\Models\Date;
use App\Models\Product;
use App\Models\RecurringOrder;
use App\Models\RecurringOrderItem;
use App\Services\SubscriptionSettingsService;
use Carbon\Carbon;
use Illuminate\Support\Facades\Event;
use Livewire\Livewire;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class SubscriptionResumeTest extends TenantTestCase
{
    #[Test]
    public function it_can_render()
    {
        $this->namespaceHints();

        $subscription = RecurringOrder::factory()->create();

        Livewire::test(SubscriptionResume::class, ['subscription_id' => $subscription->id])
            ->assertStatus(200);
    }

    private function namespaceHints(): void
    {
        $hints[] = resource_path("theme/resources/views");

        view()->replaceNamespace('theme', $hints);
    }

    #[Test]
    public function it_renders_with_a_deleted_subscritpion()
    {
        $this->namespaceHints();

        $subscription = RecurringOrder::factory()->create(['deleted_at' => now()]);

        Livewire::test(SubscriptionResume::class, ['subscription_id' => $subscription->id])
            ->assertStatus(200);
    }

    #[Test]
    public function it_can_resume_a_subscription()
    {
        Event::fake([SubscriptionWasResumed::class]);

        Carbon::setTestNow(now());

        $this->namespaceHints();

        $promo_item_one = Product::factory()->create();
        $promo_item_two = Product::factory()->create();

        $this->mock(SubscriptionSettingsService::class, function ($mock) use ($promo_item_one, $promo_item_two) {
            $mock->shouldReceive('productIncentiveIds')->andReturn(collect([$promo_item_one->id, $promo_item_two->id]));
            $mock->shouldReceive('defaultProductIncentiveId')->andReturn($promo_item_one->id);
            $mock->shouldReceive('inventoryManagementDayCount')->andReturn(1);
        });

        $subscription = RecurringOrder::factory()->create([
            'deleted_at' => now(),
            'reorder_frequency' => 7,
            'generate_at' => null,
            'ready_at' => null,
        ]);

        $date = Date::factory()->create([
            'schedule_id' => $subscription->fulfillment->schedule_id,
            'order_end_date' => today()->addDays(5),
            'pickup_date' => today()->addDays(7),
        ]);

        Livewire::test(SubscriptionResume::class, ['subscription_id' => $subscription->id])
            ->set([
                'delivery_date_id' => $date->id,
                'promo_product_id' => $promo_item_two->id,
                'frequency' => 14
            ])
            ->call('submit')
            ->assertSessionHasNoErrors()
            ->assertRedirect(route('store.index'));

        $this->assertDatabaseHas(RecurringOrder::class, [
            'id' => $subscription->id,
            'deleted_at' => null,
            'reorder_frequency' => 14,
            'generate_at' => today()->addDays(4)->endOfDay()->format('Y-m-d H:i:s'),
            'ready_at' => $date->pickup_date->copy()->endOfDay()->format('Y-m-d H:i:s'),
        ]);

        $this->assertDatabaseHas(RecurringOrderItem::class, [
            'order_id' => $subscription->id,
            'product_id' => $promo_item_two->id,
            'type' => 'promo',
            'qty' => 1,
        ]);

        Carbon::setTestNow();
    }

    #[Test]
    public function it_fires_an_event_when_the_subscription_is_resumed()
    {
        Event::fake([SubscriptionWasResumed::class]);

        Carbon::setTestNow($now = now());

        $this->namespaceHints();

        $promo_item_one = Product::factory()->create();

        $this->mock(SubscriptionSettingsService::class, function ($mock) use ($promo_item_one) {
            $mock->shouldReceive('productIncentiveIds')->andReturn(collect([$promo_item_one->id]));
            $mock->shouldReceive('defaultProductIncentiveId')->andReturn($promo_item_one->id);
            $mock->shouldReceive('inventoryManagementDayCount')->andReturn(1);
        });

        $subscription = RecurringOrder::factory()->create([
            'deleted_at' => $now,
            'reorder_frequency' => 7,
            'generate_at' => null,
            'ready_at' => null,
        ]);

        $date = Date::factory()->create([
            'schedule_id' => $subscription->fulfillment->schedule_id,
            'order_end_date' => today()->addDays(5),
            'pickup_date' => today()->addDays(7),
        ]);

        Livewire::test(SubscriptionResume::class, ['subscription_id' => $subscription->id])
            ->set([
                'delivery_date_id' => $date->id,
                'promo_product_id' => $promo_item_one->id,
                'frequency' => 14
            ])
            ->call('submit')
            ->assertSessionHasNoErrors()
            ->assertRedirect(route('store.index'));

        Event::assertDispatched(SubscriptionWasResumed::class, function ($event) use ($subscription, $date, $now, $promo_item_one) {
            return $event->subscription->is($subscription)
                && $event->old_deleted_at->is($now)
                && $event->old_frequency === 7
                && $event->old_promo_item_id === null
                && $event->new_delivery_date->is($date->pickup_date->copy()->endOfDay())
                && $event->new_frequency === 14
                && $event->new_promo_item_id === $promo_item_one->id;
        });

        Carbon::setTestNow();
    }
}
