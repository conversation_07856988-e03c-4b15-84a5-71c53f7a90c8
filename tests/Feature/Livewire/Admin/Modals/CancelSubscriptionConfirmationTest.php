<?php

namespace Tests\Feature\Livewire\Admin\Modals;

use App\Livewire\Admin\Modals\CancelSubscriptionConfirmation;
use App\Models\RecurringOrder;
use Illuminate\Support\Carbon;
use Livewire\Livewire;
use PHPUnit\Framework\Attributes\Test;
use Tests\LivewireTenantTestCase;

class CancelSubscriptionConfirmationTest extends LivewireTenantTestCase
{
    #[Test]
    public function the_component_can_render(): void
    {
        $subscription = RecurringOrder::factory()->create();

        Livewire::test(CancelSubscriptionConfirmation::class)
            ->dispatch('open-modal-cancel-subscription-confirmation', $subscription->id)
            ->assertStatus(200);
    }

    #[Test]
    function it_can_cancel_a_subscription(): void
    {
        Carbon::setTestNow($now = now());

        $subscription = RecurringOrder::factory()->create();

        Livewire::test(CancelSubscriptionConfirmation::class)
            ->dispatch('open-modal-cancel-subscription-confirmation', $subscription->id)
            ->call('submit')
            ->assertHasNoErrors()
            
            ->assertDispatched('subscriptionUpdated')
            ->assertRedirect(route('admin.subscriptions.edit', [
                'subscription' => $subscription->id
            ]));

        $this->assertDatabaseHas(RecurringOrder::class, [
            'id' => $subscription->id,
            'deleted_at' => $now
        ]);

        Carbon::setTestNow();
    }

    #[Test]
    function it_can_closel(): void
    {
        $subscription = RecurringOrder::factory()->create();

        Livewire::test(CancelSubscriptionConfirmation::class)
            ->dispatch('open-modal-cancel-subscription-confirmation', $subscription->id)
            ->call('close');

        $this->assertDatabaseHas(RecurringOrder::class, [
            'id' => $subscription->id,
            'deleted_at' => null
        ]);
    }
}
