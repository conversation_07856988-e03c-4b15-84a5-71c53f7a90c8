<?php

namespace Tests\Feature\Livewire\Admin\Modals;

use App\Livewire\Admin\Modals\DeleteDeliveryMethodConfirmation;
use App\Models\Pickup;
use Illuminate\Support\Carbon;
use Livewire\Livewire;
use PHPUnit\Framework\Attributes\Test;
use Tests\LivewireTenantTestCase;

class DeleteDeliveryMethodConfirmationTest extends LivewireTenantTestCase
{
    #[Test]
    public function the_component_can_render(): void
    {
        $delivery_method = Pickup::factory()->create();

        Livewire::test(DeleteDeliveryMethodConfirmation::class)
            ->assertStatus(200);
    }

    #[Test]
    function a_name_is_required_to_submit(): void
    {
        $delivery_method = Pickup::factory()->create();

        Livewire::test(DeleteDeliveryMethodConfirmation::class)
            ->dispatch('open-modal-delete-delivery-method-confirmation', $delivery_method->id)
            ->set('name', '')
            ->call('submit')
            ->assertHasErrors(['name' => 'required']);
    }

    #[Test]
    function a_matching_is_required_to_submit(): void
    {
        $delivery_method = Pickup::factory()->create();

        Livewire::test(DeleteDeliveryMethodConfirmation::class)
            ->dispatch('open-modal-delete-delivery-method-confirmation', $delivery_method->id)
            ->set('name', 'zzzzz')
            ->call('submit')
            ->assertHasErrors('name');
    }

    #[Test]
    function it_can_delete_a_pickup_location(): void
    {
        Carbon::setTestNow($now = now());

        $delivery_method = Pickup::factory()->create(['fulfillment_type' => Pickup::FULFILLMENT_TYPE_PICKUP]);

        Livewire::test(DeleteDeliveryMethodConfirmation::class)
            ->dispatch('open-modal-delete-delivery-method-confirmation', $delivery_method->id)
            ->set('name', $delivery_method->title)
            ->call('submit')
            ->assertHasNoErrors()
            ->assertRedirect(route('admin.pickups.index'))
            ->assertSessionHas('flash_notification', [
                'message' => 'Delivery method was successfully deleted!',
                'level' => 'info'
            ]);

        $this->assertDatabaseHas(Pickup::class, [
            'id' => $delivery_method->id,
            'deleted_at' => $now
        ]);

        Carbon::setTestNow();
    }

    #[Test]
    function it_can_delete_a_delivery_zone(): void
    {
        Carbon::setTestNow($now = now());

        $delivery_method = Pickup::factory()->create(['fulfillment_type' => Pickup::FULFILLMENT_TYPE_DELIVERY]);

        Livewire::test(DeleteDeliveryMethodConfirmation::class)
            ->dispatch('open-modal-delete-delivery-method-confirmation', $delivery_method->id)
            ->set('name', $delivery_method->title)
            ->call('submit')
            ->assertHasNoErrors()
            ->assertRedirect(route('admin.delivery.index'))
            ->assertSessionHas('flash_notification', [
                'message' => 'Delivery method was successfully deleted!',
                'level' => 'info'
            ]);

        $this->assertDatabaseHas(Pickup::class, [
            'id' => $delivery_method->id,
            'deleted_at' => $now
        ]);

        Carbon::setTestNow();
    }

    #[Test]
    function it_can_close(): void
    {
        $delivery_method = Pickup::factory()->create();

        Livewire::test(DeleteDeliveryMethodConfirmation::class)
            ->dispatch('open-modal-delete-delivery-method-confirmation', $delivery_method->id)
            ->set('name', $delivery_method->title)
            ->call('close');

        $this->assertDatabaseHas(Pickup::class, [
            'id' => $delivery_method->id,
            'deleted_at' => null
        ]);
    }
}
