<?php

namespace Tests\Feature\Livewire\Admin\Modals;

use App\Livewire\Admin\Modals\Modal;
use App\Models\Page;
use App\Models\Pickup;
use Livewire\Livewire;
use PHPUnit\Framework\Attributes\Test;
use Tests\LivewireTenantTestCase;

class ModalTest extends LivewireTenantTestCase
{
    #[Test]
    public function the_component_can_render(): void
    {
        Livewire::test(Modal::class)
            ->assertStatus(200);
    }

    #[Test]
    public function it_opens_when_the_delete_page_confirmation_modal_when_its_openModal_event_is_fired(): void
    {
        $page = Page::factory()->create();

        $component = Livewire::test(Modal::class);

        $this->assertFalse($component->open);
        $this->assertEquals('', $component->component);
        $this->assertEquals([], $component->params);

        $component->dispatch('openModal', 'admin.modals.delete-page-confirmation', ['page_id' => $page->id]);

        $this->assertTrue($component->open);
        $this->assertEquals('admin.modals.delete-page-confirmation', $component->component);
        $this->assertEquals(['page_id' => $page->id], $component->params);
    }

    #[Test]
    public function it_opens_when_the_delete_delivery_method_confirmation_modal_when_its_openModal_event_is_fired(): void
    {
        $delivery_method = Pickup::factory()->create();

        $component = Livewire::test(Modal::class);

        $this->assertFalse($component->open);
        $this->assertEquals('', $component->component);
        $this->assertEquals([], $component->params);

        $component->dispatch('openModal', 'admin.modals.delete-delivery-method-confirmation', ['delivery_method_id' => $delivery_method->id]);

        $this->assertTrue($component->open);
        $this->assertEquals('admin.modals.delete-delivery-method-confirmation', $component->component);
        $this->assertEquals(['delivery_method_id' => $delivery_method->id], $component->params);
    }
}
