#!/bin/bash

DEV_USERNAME=ubuntu
DEV_PROTOCOL=sftp
DEV_SSH_KEY=$HOME/.ssh/reusser/reusser.design.ec2_rsa
DEV_REMOTE_DIR=/var/www/vhosts/sev0619-02-19.reusser.design
DEV_LOCAL_DIR=./
DEV_HOST=reusser.design

# npm run release

# git ftp push --syncroot "$DEV_LOCAL_DIR" -u $DEV_USERNAME --key "$DEV_SSH_KEY" -v $DEV_PROTOCOL://$DEV_HOST"$DEV_REMOTE_DIR"

rsync --delete --exclude=".git*" --exclude=".env" --exclude="logs/" --exclude="storage/" -avz "./" "$DEV_HOST:$DEV_REMOTE_DIR"

ssh -i "$DEV_SSH_KEY" $DEV_USERNAME@$DEV_HOST 'bash -s' < ./scripts/post-deploy.sh
