tinymce.PluginManager.add("save",function(e){function t(){var t;if(t=tinymce.DOM.getParent(e.id,"form"),!e.getParam("save_enablewhendirty",!0)||e.isDirty())return tinymce.triggerSave(),e.getParam("save_onsavecallback")?(e.execCallback("save_onsavecallback",e),void e.nodeChanged()):void(t?(e.setDirty(!1),t.onsubmit&&!t.onsubmit()||("function"==typeof t.submit?t.submit():n(e.translate("Error: Form submit field collision."))),e.nodeChanged()):n(e.translate("Error: No form element found.")))}function n(t){e.notificationManager.open({text:t,type:"error"})}function r(){var t=tinymce.trim(e.startContent);return e.getParam("save_oncancelcallback")?void e.execCallback("save_oncancelcallback",e):(e.setContent(t),e.undoManager.clear(),void e.nodeChanged())}function i(){var t=this;e.on("nodeChange dirty",function(){t.disabled(e.getParam("save_enablewhendirty",!0)&&!e.isDirty())})}e.addCommand("mceSave",t),e.addCommand("mceCancel",r),e.addButton("save",{icon:"save",text:"Save",cmd:"mceSave",disabled:!0,onPostRender:i}),e.addButton("cancel",{text:"Cancel",icon:!1,cmd:"mceCancel",disabled:!0,onPostRender:i}),e.addShortcut("Meta+S","","mceSave")});