<?php

namespace App\Services\Geocoding;

use App\Contracts\Geocoder;
use App\Exceptions\NoGeocodeResultsException;
use App\Services\SettingsService;
use Bugsnag\BugsnagLaravel\Facades\Bugsnag;
use Illuminate\Http\Client\RequestException;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Http;

class Geocodio implements Geocoder
{
    protected string $baseUrl = 'https://api.geocod.io/v1.7/geocode';

    /**
     * @throws NoGeocodeResultsException
     */
    public function fromAddress(string $address): GeocodedAddress
    {
        try {
            $response = Http::get($this->baseUrl, [
                'q' => $this->formatAddress($address),
                'country' => $this->getCountry(),
                'api_key' => $this->apiKey()
            ])
                ->throw();
        } catch (RequestException $exception) {
            // 422's are basic "could not geocode address" errors
            if ($exception->response->status() !== 422) {
                Bugsnag::notifyException($exception);
            }

            throw new NoGeocodeResultsException;
        }

        return $this->parseResponse($response->json());
    }

    /**
     * @throws NoGeocodeResultsException
     */
    public function fromAddressParts(array $addressParts = []) : GeocodedAddress
    {
        try {
            $response = Http::get($this->baseUrl, [
                'q' => $this->formatAddress($addressParts),
                'country' => $this->getCountry(),
                'api_key' => $this->apiKey()
            ])
            ->throw();
        } catch (RequestException $exception) {
            // 422's are basic "could not geocode address" errors
            if ($exception->response->status() !== 422) {
                Bugsnag::notifyException($exception);
            }

            throw new NoGeocodeResultsException;
        }

        return $this->parseResponse($response->json());
    }

    /**
     * @throws NoGeocodeResultsException
     */
    public function fromZipcode(string $zip) : GeocodedAddress
    {
        try {
            $response = Http::get($this->baseUrl, [
                'postal_code' => trim($zip),
                'country' => $this->getCountry(),
                'api_key' => $this->apiKey()
            ])
                ->throw();
        } catch (RequestException $exception) {
            // 422's are basic "could not geocode address" errors
            if ($exception->response->status() !== 422) {
                Bugsnag::notifyException($exception);
            }

            throw new NoGeocodeResultsException;
        }

        return $this->parseResponse($response->json());
    }

    /**
     * @throws NoGeocodeResultsException
     */
    private function parseResponse(array $response): GeocodedAddress
    {
        if ( ! isset($response['results']) || count($response['results']) === 0) {
            throw new NoGeocodeResultsException;
        }

        return new GeocodedAddress(
            $response['results'][0]['location']['lat'],
            $response['results'][0]['location']['lng'],
            $response['results'][0]['address_components']['city'] ?? null,
            $response['results'][0]['address_components']['state'] ?? null,
            $response['results'][0]['address_components']['zip'] ?? null,
            $response['results'][0]['address_components']['country'] ?? null,
            $response['results'][0]['accuracy'] ?? 1
        );
    }

    private function formatAddress(string|array $address): string
    {
        return trim(
            collect(Arr::wrap($address))
                ->map(fn($value) => trim($value))
                ->implode(', ')
        );
    }

    private function getCountry() : string
    {
        return app(SettingsService::class)->farmCountry();
    }

    private function apiKey(): ?string
    {
        return config('services.geocodio.key');
    }
}
