<?php

namespace App\Services\FilterService\Filters;

use App\Support\Enums\UserRole;
use Arr;
use Illuminate\Http\Request;

class Role extends Filter
{
    public static string $query_param = 'role_id';

    protected string $label = 'Role:';

    public function setValue(Request $request): void
    {
        $id = $request->get(static::$query_param);

        if (is_null($id)) return;

        $this->value = UserRole::whereIn(Arr::wrap($id))
            ->implode(', ');
    }
}
