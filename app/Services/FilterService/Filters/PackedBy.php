<?php

namespace App\Services\FilterService\Filters;

use App\Models\User;
use Illuminate\Http\Request;

class PackedBy extends Filter
{
    public static string $query_param = 'staff_id';

    protected string $label = 'Packed By:';

    public function setValue(Request $request): void
    {
        $id = $request->get(static::$query_param);

        if (is_null($id)) return;

        $this->value = User::whereIn('id', \Arr::wrap($id))
            ->select(['first_name', 'last_name'])
            ->get()
            ->map(fn(User $user) => $user->full_name ?? 'N/A')
            ->implode(', ');
    }
}