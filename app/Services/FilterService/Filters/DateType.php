<?php

namespace App\Services\FilterService\Filters;

use Illuminate\Http\Request;
class DateType extends Filter
{
    public static string $query_param = 'date_type';

    protected string $label = 'Date Type:';

    public function setValue(Request $request): void
    {
        $id = $request->get(static::$query_param);

        if (is_null($id)) return;

        $this->value = match($id) {
            'payment_date' => 'Payment Date',
            'pickup_date', 'ready_at' => 'Delivery Date',
            'generate_at' => 'Generate Date',
            default => '',
        };
    }
}