<?php

namespace App\Services\FilterService\Filters;

use Illuminate\Http\Request;

class Rating extends Filter
{
    public static string $query_param = 'rating';

    protected string $label = 'Rating:';

    public function setValue(Request $request): void
    {
        $rating = $request->get(static::$query_param);

        if (empty($rating)) return;

        $this->value = [
            '1' => '&#9733;&#9734;&#9734;&#9734;&#9734;',
            '2' => '&#9733;&#9733;&#9734;&#9734;&#9734;',
            '3' => '&#9733;&#9733;&#9733;&#9734;&#9734;',
            '4' => '&#9733;&#9733;&#9733;&#9733;&#9734;',
            '5' => '&#9733;&#9733;&#9733;&#9733;&#9733;',
        ][$rating];
    }
}