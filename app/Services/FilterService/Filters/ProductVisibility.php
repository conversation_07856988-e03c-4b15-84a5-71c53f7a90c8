<?php

namespace App\Services\FilterService\Filters;

use Illuminate\Http\Request;

class ProductVisibility extends Filter
{
    public static string $query_param = 'product_visibility';

    public function setLabel(Request $request): void
    {
        if ( ! $request->has(static::$query_param)) return;

        $this->label = $request->get(static::$query_param) ? 'Visible in Store' : 'Hidden from Store';
    }

    protected function setValue(Request $request): void
    {
        $this->value = '';
    }
}