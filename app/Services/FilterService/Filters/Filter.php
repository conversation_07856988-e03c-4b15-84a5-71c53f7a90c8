<?php

namespace App\Services\FilterService\Filters;

use Illuminate\Http\Request;
use Illuminate\Support\Collection;

abstract class Filter
{
    public static string $query_param;

    protected string $label;

    protected string $value;

    protected string $remove_filter_url;

    /**
     * @return Collection<string, Filter>
     */
    public function handle(array $passable, \Closure $next): Collection
    {
        /** @var Request $request */
        list($request, $collection) = $passable;

        $value = $request->get(static::$query_param);

        if (is_null($value) || $value === '') {
            return $next([$request, $collection]);
        }

        // handle date range filters having empty values for both start and end
        if (is_array($value)) {
            if (collect(array_values($value))->filter()->isEmpty()) {
                return $next([$request, $collection]);
            }
        }

        $this->setLabel($request);
        $this->setValue($request);
        $this->setClearParamUrl($request);

        return $this->applyFilter($next([$request, $collection]));
    }

    /**
     * @param  Collection<string, Filter> $collection
     * @return Collection<string, Filter>
     */
    private function applyFilter(Collection $collection): Collection
    {
        return $collection->put(static::$query_param, $this);
    }

    public function label(): string
    {
        return $this->label;
    }

    public function value(): string
    {
        return $this->value;
    }

    public function removeFilterUrl(): string
    {
        return $this->remove_filter_url;
    }

    protected function setLabel(Request $request): void
    {
    }

    protected function setValue(Request $request): void
    {
        $value = $request->get(static::$query_param);

        if (is_null($value)) return;

        $this->value = $value;
    }

    protected function setClearParamUrl(Request $request): void
    {
        $this->remove_filter_url = $this->getClearUrl($request, static::$query_param);
    }

    protected function getClearUrl(Request $request, string $exclude): string
    {
        return $request->url() . '?' . http_build_query($request->except([$exclude, 'filter_id']));
    }
}