<?php

namespace App\Services\FilterService\Filters;

use Illuminate\Http\Request;

class Collection extends Filter
{
    public static string $query_param = 'collection_id';

    protected string $label = 'Collection:';

    public function setValue(Request $request): void
    {
        $id = $request->get(static::$query_param);

        if (is_null($id)) return;

        $this->value = \App\Models\Collection::whereIn('id', \Arr::wrap($id))
            ->pluck('title')
            ->implode(', ');
    }
}