import { createApp, defineAsyncComponent } from 'vue';
import vSelect from 'vue-select';
import 'vue-select/dist/vue-select.css';
import mitt from 'mitt';
import axios from 'axios';

window.axios = axios;
window.axios.defaults.headers.common = {
    'X-Requested-With': 'XMLHttpRequest'
};

const eventHub = mitt();
window.eventHub = eventHub;

const PickupManager = defineAsyncComponent(() =>
    import('./_pickupManager.vue')
);

const SystemNotification = defineAsyncComponent(() =>
    import('../../../resources/assets/js/admin/components/SystemNotification.vue')
);

const app = createApp({
    created: function() {
        eventHub.on('showProgressBar', function() {
            $('#progressBar').addClass('show-progress-bar');
        });

        eventHub.on('hideProgressBar', function() {
            $('#progressBar').removeClass('show-progress-bar');
        });

        eventHub.on('showModal', this.showModal);

        eventHub.on('hideModal', this.hideModal);
    },

    components: {
        PickupManager,
        SystemNotification
    }
});

app.component('v-select', vSelect);

app.config.globalProperties.$filters = {
    cents(value) {
        value = value / 100;
        return value.toFixed(2);
    },

    currency(value) {
        return '$' + value;
    },

    weight(value) {
        value = value / 1;
        return value.toFixed(2) + 'lb.';
    }
};

app.mixin({
    methods: {
        showModal: function(modalId) {
            eventHub.emit(modalId + ':opened');
            $(document.body).addClass('modal-open');
            let modal = $('#' + modalId);
            modal.addClass('gc-modal-show').outerWidth();
            modal.addClass('gc-modal-enter');
        },

        hideModal: function(modalId) {
            eventHub.emit(modalId + ':closed');
            $(document.body).removeClass('modal-open');
            let modal = $('#' + modalId);
            modal.removeClass('gc-modal-enter');
            setTimeout(function() {
                modal.removeClass('gc-modal-show');
            }, 300);
        },
        submitForm: function(formId, e) {
            let form = document.getElementById(formId);
            if (!form) return false;

            if (e != undefined) {
                var button = e.target;
                button.disabled = true;
            }

            eventHub.emit('showProgressBar');
            form.submit();
        }
    }
});

app.mount('#app');
