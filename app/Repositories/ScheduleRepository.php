<?php

namespace App\Repositories;

use App\Models\Date;
use App\Models\Schedule;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Collection;

class ScheduleRepository
{
    /**
     * @return Collection<int, array>
     */
    public function all(Request $request, bool $onlyActiveDates = false): Collection
    {
        $schedules = Schedule::query()
            ->with('nextDate', 'nextOrderWindow', 'pickups')
            ->when($onlyActiveDates, function ($query) {
                $query->whereHas('nextDate', fn($query) => $query->where('pickup_date', '>=', Carbon::today()));
            })
            ->get();

        $response = collect();

        foreach ($schedules as $schedule) {
            /** @var Schedule $schedule */

            /** @var Date|null $next_order_window */
            $next_order_window = $schedule->nextOrderWindow;

            /** @var Date|null $next_date */
            $next_date = $schedule->nextDate;

            $response->push([
                'id' => $schedule->id,
                'title' => $schedule->title,
                'type_id' => $schedule->type_id,
                'next_pickup_date' => $next_order_window?->pickup_date->format('D, M jS') ?? 'N/A',
                'next_deadline_date' => $next_order_window?->order_end_date->format('D, M jS') ?? 'N/A',
                'deadline_date' => $next_date?->order_end_date->format('Y-m-d') ?? 'N/A',
                'deadline_date_formatted' => $next_date?->order_end_date->format('D, M jS') ?? 'N/A',
                'pickup_date' => $next_date?->pickup_date->format('Y-m-d') ?? 'N/A',
                'pickup_date_formatted' => $next_date?->pickup_date->format('D, M jS') ?? 'N/A',
                'date_id' => $next_date->id ?? 0,
                'reminder_sent' => $next_order_window->reminder_sent ?? 0,
                'pickups' => $schedule->pickups->toArray(),
                'reminder_enabled' => $schedule->reminder_enabled
            ]);
        }

        $order_by = $request->get('orderBy', 'pickup_date');

        return $request->get('sort', 'asc') === 'desc'
            ? $response->sortByDesc($order_by)
            : $response->sortBy($order_by);

    }
}
