<?php

namespace App\Repositories\Reports;

use App\Models\OrderItem;
use App\Traits\DateRangeTrait;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class AggregateSaleByOrderReport
{
    use DateRangeTrait;

    public function __construct(
        protected Collection $results
    ) {}

    public static function query(Request $request): Collection
    {
        $standardQuery = static::getStandardItemsQuery($request);
        
        $bundleQuery = static::getBundleComponentsQuery($request);
        
        $unionQuery = $standardQuery->unionAll($bundleQuery);
        
        return collect(DB::query()
            ->fromSub($unionQuery, 'combined_results')
            ->orderBy('order_id')
            ->orderBy('order_item_id')
            ->get());
    }

    private static function getStandardItemsQuery(Request $request): Builder
    {
        return OrderItem::query()
            ->join('orders as o', 'o.id', '=', 'order_items.order_id')
            ->join('users as u', 'u.id', '=', 'o.customer_id')
            ->join('products as p', 'p.id', '=', 'order_items.product_id')
            ->join('packing_groups as pg', 'pg.id', '=', 'p.inventory_type')
            ->join('pickups as PI', 'PI.id', '=', 'o.pickup_id')
            ->join('schedules as s', 's.id', '=', 'o.schedule_id')
            ->selectRaw(implode(',', [
                'o.id as order_id',
                'CASE WHEN o.blueprint_id IS NOT NULL THEN "Subscription" ELSE "One Time" END as order_type',
                'order_items.id as order_item_id',
                'CASE WHEN p.is_bundle = 1 THEN "Bundle" ELSE "Standard" END as product_type',
                '"Y" as billable',
                'p.barcode as product_barcode',
                'order_items.product_id as product_id',
                'p.sku as sku',
                'order_items.title as title',
                'p.unit_of_issue as unit_of_issue',
                'order_items.fulfilled_qty as quantity',
                'p.custom_sort as sort_id',
                'pg.title as packing_group',
                'p.accounting_class as accounting_class_id',
                'order_items.weight as pounds_per_unit',
                'order_items.weight * order_items.fulfilled_qty as total_pounds',
                'order_items.store_price as retail_price_per_unit',
                'order_items.store_price * order_items.fulfilled_qty as total_retail_price',
                'order_items.unit_price as billed_price_per_unit',
                'order_items.unit_price * order_items.fulfilled_qty as total_billed_price',
                'order_items.store_price - order_items.unit_price as discount_per_unit',
                '(order_items.store_price * order_items.fulfilled_qty) - (order_items.unit_price * order_items.fulfilled_qty) as total_discount',
                'o.confirmed_date as confirmation_date',
                'o.deadline_date as deadline_date',
                'o.pack_deadline_at as pack_date',
                'o.payment_date as payment_date',
                'o.pickup_date as delivery_date',
                'PI.title as location_name',
                'PI.id as location_id',
                's.title as schedule_name',
                's.id as schedule_id',
                'o.customer_id as customer_id',
                'o.customer_first_name as customer_first_name',
                'o.customer_last_name as customer_last_name',
                'o.customer_phone as customer_phone',
                'o.customer_email as customer_email',
                'o.shipping_street as shipping_street',
                'o.shipping_street_2 as shipping_street_2',
                'o.shipping_city as shipping_city',
                'o.shipping_state as shipping_state',
                'o.shipping_zip as shipping_zip',
                '"USA" as shipping_country',
                'u.order_count as customer_order_count',
                'u.notes as profile_notes',
                'o.customer_notes as customer_notes',
                'o.packing_notes as private_notes',
                'o.invoice_notes as invoice_notes',
                'o.payment_notes as payment_notes',
            ]))
            ->where('o.confirmed', true)
            ->where('o.canceled', false)
            ->whereIn('o.status_id', $request->get('order_status', [1, 2, 3]))
            ->when($request->filled('confirmed_date'), function ($q) use ($request) {
                $range = (new static(collect()))->getDateRange($request->get('confirmed_date'));
                if ($start = $range->get('start')) {
                    $q->where('o.confirmed_date', '>=', $start);
                }
                if ($end = $range->get('end')) {
                    $q->where('o.confirmed_date', '<=', $end);
                }
                return $q;
            })
            ->when($request->filled('pickup_date'), function ($q) use ($request) {
                $range = (new static(collect()))->getDateRange($request->get('pickup_date'));
                if ($start = $range->get('start')) {
                    $q->where('o.pickup_date', '>=', $start);
                }
                if ($end = $range->get('end')) {
                    $q->where('o.pickup_date', '<=', $end);
                }
                return $q;
            })
            ->when($request->filled('payment_date'), function ($q) use ($request) {
                $range = (new static(collect()))->getDateRange($request->get('payment_date'));
                if ($start = $range->get('start')) {
                    $q->where('o.payment_date', '>=', $start);
                }
                if ($end = $range->get('end')) {
                    $q->where('o.payment_date', '<=', $end);
                }
                return $q;
            })
            ->when($request->filled('order_type_id'), function ($q) use ($request) {
                return $q->whereIn('o.type_id', (array) $request->get('order_type_id'));
            })
            ->when($request->filled('pickup_id'), function ($q) use ($request) {
                return $q->whereIn('o.pickup_id', $request->get('pickup_id'));
            })
            ->when($request->filled('schedule_id'), function ($q) use ($request) {
                return $q->whereIn('s.id', $request->get('schedule_id'));
            })
            ->when($request->filled('customer'), function ($q) use ($request) {
                $customer = $request->get('customer');
                if (filter_var($customer, FILTER_VALIDATE_INT)) {
                    return $q->where('o.customer_id', $customer);
                } elseif (filter_var($customer, FILTER_VALIDATE_EMAIL)) {
                    return $q->where('o.customer_email', $customer);
                } else {
                    return $q->where(function ($query) use ($customer) {
                        $query->where(DB::raw('CONCAT(o.customer_first_name, " ", o.customer_last_name)'), 'LIKE', '%' . $customer . '%')
                            ->orWhere('o.customer_phone', $customer)
                            ->orWhere('o.accounting_id', $customer);
                    });
                }
            });
    }

    private static function getBundleComponentsQuery(Request $request): Builder
    {
        return OrderItem::query()
            ->join('orders as o', 'o.id', '=', 'order_items.order_id')
            ->join('users as u', 'u.id', '=', 'o.customer_id')
            ->join('products as p', 'p.id', '=', 'order_items.product_id')
            ->join('packing_groups as pg', 'pg.id', '=', 'p.inventory_type')
            ->join('pickups as PI', 'PI.id', '=', 'o.pickup_id')
            ->join('schedules as s', 's.id', '=', 'o.schedule_id')
            ->join('bundle_product as bp', 'bp.bundle_id', '=', 'order_items.product_id')
            ->join('products as bp_product', 'bp_product.id', '=', 'bp.product_id')
            ->join('packing_groups as bp_pg', 'bp_pg.id', '=', 'bp_product.inventory_type')
            ->selectRaw(implode(',', [
                'o.id as order_id',
                'CASE WHEN o.blueprint_id IS NOT NULL THEN "Subscription" ELSE "One Time" END as order_type',
                'order_items.id as order_item_id',
                'CASE WHEN p.is_bundle = 1 THEN "Bundle" ELSE "Standard" END as product_type',
                '"N" as billable',
                'bp_product.barcode as product_barcode',
                'bp.product_id as product_id',
                'bp_product.sku as sku',
                'bp_product.title as title',
                'bp_product.unit_of_issue as unit_of_issue',
                'order_items.fulfilled_qty * bp.qty as quantity',
                'bp_product.custom_sort as sort_id',
                'bp_pg.title as packing_group',
                'bp_product.accounting_class as accounting_class_id',
                '0 as pounds_per_unit',
                '0 as total_pounds',
                '0 as retail_price_per_unit',
                '0 as total_retail_price',
                '0 as billed_price_per_unit',
                '0 as total_billed_price',
                '0 as discount_per_unit',
                '0 as total_discount',
                'o.confirmed_date as confirmation_date',
                'o.deadline_date as deadline_date',
                'o.pack_deadline_at as pack_date',
                'o.payment_date as payment_date',
                'o.pickup_date as delivery_date',
                'PI.title as location_name',
                'PI.id as location_id',
                's.title as schedule_name',
                's.id as schedule_id',
                'o.customer_id as customer_id',
                'o.customer_first_name as customer_first_name',
                'o.customer_last_name as customer_last_name',
                'o.customer_phone as customer_phone',
                'o.customer_email as customer_email',
                'o.shipping_street as shipping_street',
                'o.shipping_street_2 as shipping_street_2',
                'o.shipping_city as shipping_city',
                'o.shipping_state as shipping_state',
                'o.shipping_zip as shipping_zip',
                '"USA" as shipping_country',
                'u.order_count as customer_order_count',
                'u.notes as profile_notes',
                'o.customer_notes as customer_notes',
                'o.packing_notes as private_notes',
                'o.invoice_notes as invoice_notes',
                'o.payment_notes as payment_notes',
            ]))
            ->where('o.confirmed', true)
            ->where('o.canceled', false)
            ->whereIn('o.status_id', $request->get('order_status', [1, 2, 3]))
            ->when($request->filled('confirmed_date'), function ($q) use ($request) {
                $range = (new static(collect()))->getDateRange($request->get('confirmed_date'));
                if ($start = $range->get('start')) {
                    $q->where('o.confirmed_date', '>=', $start);
                }
                if ($end = $range->get('end')) {
                    $q->where('o.confirmed_date', '<=', $end);
                }
                return $q;
            })
            ->when($request->filled('pickup_date'), function ($q) use ($request) {
                $range = (new static(collect()))->getDateRange($request->get('pickup_date'));
                if ($start = $range->get('start')) {
                    $q->where('o.pickup_date', '>=', $start);
                }
                if ($end = $range->get('end')) {
                    $q->where('o.pickup_date', '<=', $end);
                }
                return $q;
            })
            ->when($request->filled('payment_date'), function ($q) use ($request) {
                $range = (new static(collect()))->getDateRange($request->get('payment_date'));
                if ($start = $range->get('start')) {
                    $q->where('o.payment_date', '>=', $start);
                }
                if ($end = $range->get('end')) {
                    $q->where('o.payment_date', '<=', $end);
                }
                return $q;
            })
            ->when($request->filled('order_type_id'), function ($q) use ($request) {
                return $q->whereIn('o.type_id', (array) $request->get('order_type_id'));
            })
            ->when($request->filled('pickup_id'), function ($q) use ($request) {
                return $q->whereIn('o.pickup_id', $request->get('pickup_id'));
            })
            ->when($request->filled('schedule_id'), function ($q) use ($request) {
                return $q->whereIn('s.id', $request->get('schedule_id'));
            })
            ->when($request->filled('customer'), function ($q) use ($request) {
                $customer = $request->get('customer');
                if (filter_var($customer, FILTER_VALIDATE_INT)) {
                    return $q->where('o.customer_id', $customer);
                } elseif (filter_var($customer, FILTER_VALIDATE_EMAIL)) {
                    return $q->where('o.customer_email', $customer);
                } else {
                    return $q->where(function ($query) use ($customer) {
                        $query->where(DB::raw('CONCAT(o.customer_first_name, " ", o.customer_last_name)'), 'LIKE', '%' . $customer . '%')
                            ->orWhere('o.customer_phone', $customer)
                            ->orWhere('o.accounting_id', $customer);
                    });
                }
            });
    }
}
