<?php

namespace App\Repositories\Reports;

use App\Models\Product;
use App\Support\Enums\ProductType;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;

class StockOut
{
    /**
     * @return Builder<Product>
     */
    public function get(Request $request): Builder
    {
        return Product::query()
            ->filter($request->all())
            ->with('vendor')
            ->whereRaw('stock_out_inventory >= inventory')
            ->where('track_inventory', true)
            ->where('type_id','<>', ProductType::GIFT_CARD->value)
            ->orderBy(
                $request->get('orderBy', 'stock_out_inventory'),
                $request->get('sort', 'desc')
            )
            ->orderBy('inventory', 'asc');
    }
}
