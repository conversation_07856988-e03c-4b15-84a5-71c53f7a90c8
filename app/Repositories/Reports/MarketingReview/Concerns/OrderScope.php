<?php

namespace App\Repositories\Reports\MarketingReview\Concerns;

use App\Models\User;
use App\Support\Enums\Channel;
use App\Support\Enums\OrderStatus;
use Illuminate\Contracts\Database\Query\Builder as BuilderContract;

trait OrderScope
{
    public function applyScope(BuilderContract $query): BuilderContract
    {
        $deleted_customer_id = User::deletedCustomer()->id;

        return $query
            ->whereNot('customer_id', $deleted_customer_id)
            ->where(['confirmed' => true, 'canceled' => false])
            ->whereIn('status_id', OrderStatus::ids([OrderStatus::canceled()])->toArray())
            ->whereIn('type_id', [
                Channel::ONLINE_STORE, Channel::BUYING_CLUBS, Channel::HOME_DELIVERY, Channel::SHIPPING
            ]);
    }
}
