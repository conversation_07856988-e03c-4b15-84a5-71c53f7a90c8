<?php

namespace App\Repositories\Reports;

use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Product;
use App\Traits\DateRangeTrait;
use Illuminate\Database\Query\Builder;
use Illuminate\Http\Request;
use Illuminate\Support\Collection;

class Inventory
{
    use DateRangeTrait;

    /**
     * @return Collection<int, Product>
     */
    public function get(Request $request): Collection
    {
        $orderItems = OrderItem::query()
            ->selectRaw('product_id, title, sum(qty) as count_on_order')
            ->whereHas('order', function ($q) use ($request) {
                $payment_date_range = $this->getDateRange($request->input('payment_date', []));

                return $q
                    ->select('id')
                    ->whereIn('status_id', $request->get('order_status'))
                    ->where('confirmed', true)
                    ->where(function ($q) use ($payment_date_range) {
                        $start = $payment_date_range->get('start');
                        if (!is_null($start)) {
                            $q->whereDate('orders.payment_date', '>=', $start);
                        }

                        $end = $payment_date_range->get('end');
                        if (!is_null($end)) {
                            $q->whereDate('orders.payment_date', '<=', $end);
                        }

                        return $q;
                    })
                    ->when($request->filled('order_tags'), function ($q) use ($request) {
                        return $q->whereIn('orders.id', function (Builder $query)  use ($request) {
                            $query->select('order_id')
                                ->from('order_tag')
                                ->whereIn('tag_id', $request->get('order_tags', []));
                        });
                    });
            })
            ->groupBy('product_id')
            ->pluck('count_on_order', 'product_id');

        // Find unconfirmed orders, then find their products
        $upcomingProducts = [];

        $delivery_date_range = $this->getDateRange($request->input('delivery_date', [])); // delivery date range

        $start = $delivery_date_range->get('start');
        $end = $delivery_date_range->get('end');

        if ( ! is_null($start) && ! is_null($end)) {
            $orders = Order::query()
                ->where('is_recurring', true)
                ->where('confirmed', false)
                ->whereBetween('pickup_date', [$start, $end])
                ->get();

            foreach ($orders as $order) {
                $products = OrderItem::query()
                    ->where('order_id', $order->id)
                    ->selectRaw('product_id, sum(qty) as quantity')
                    ->groupBy('product_id')
                    ->pluck('quantity', 'product_id');

                $upcomingProducts[] = $products;
            }
        }

        $products = Product::query()
            ->selectRaw('id, title, sku, inventory, other_inventory, processor_inventory, item_cost, weight, unit_of_issue, 0 as count_on_order, 0 as total_inventory, 0 as upcoming, 0 as subtotal, vendor_id')
            ->with('vendor')
            ->filter($request->all())
            ->groupBy('products.id')
            ->get();

        foreach ($products as $product) {
            if (isset($orderItems[$product->id])) {
                /** @phpstan-ignore-next-line */
                $product->count_on_order = $orderItems[$product->id];
            }

            // loop through array of collections and add sum to product's upcoming total
            foreach ($upcomingProducts as $upcomingProduct) {
                foreach ($upcomingProduct as $product_id => $quantity) {
                    if ($product_id == $product->id) {
                        /** @phpstan-ignore-next-line */
                        $product->upcoming += (int) $quantity;
                    }
                }
            }

            /** @phpstan-ignore-next-line */
            $product->total_inventory = ($product->count_on_order + $product->inventory + $product->other_inventory + $product->processor_inventory);
            /** @phpstan-ignore-next-line */
            $product->subtotal = ($product->cost() / 100) * $product->total_inventory;
        }

        $orderBy = $request->get('orderBy', 'title');

        return $request->get('sort', 'asc') === 'desc'
            ? $products->sortByDesc($orderBy)
            : $products->sortBy($orderBy);
    }
}
