<?php

namespace App\Jobs;

use App\Actions\Order\ApplyTags;
use App\Events\Subscription\RecurringOrderWasConfirmed;
use App\Models\Order;
use App\Models\OrderFee;
use App\Models\OrderItem;
use App\Models\RecurringOrder;
use App\OrderWindow;
use App\Services\SettingsService;
use App\Support\Enums\OrderStatus;
use App\Traits\TenantContextMiddleware;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Carbon;
use Illuminate\Support\Once;

class GenerateSubscriptionOrder implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, TenantContextMiddleware;

    public int $tries = 1;

    public function __construct(
        public int $subscription_id,
        public Carbon $ready_at
    ) {}

    public function handle(): void
    {
        $subscription = RecurringOrder::find($this->subscription_id);

        if (is_null($subscription)) return;

        $subscription->load([
            'customer', 'fulfillment',
            'items.product.price' => fn($q) => $q->where('group_id', $subscription->pricingGroupId())
        ]);

        $pickup_schedule = $subscription->pickupSchedule();

        /** @var OrderWindow $order_window */
        $order_window = $pickup_schedule->dates()
            ->whereDate('pickup_date', '>=', $this->ready_at)
            ->first()
            ->toOrderWindows();

        $order_already_exists = $subscription->orders()
            ->where([
                'deadline_date' => $order_window->deadlineDatetime()->format('Y-m-d'),
                'pickup_date' => $order_window->deliveryDatetime()->format('Y-m-d'),
                'canceled' => 0,
                'skipped_at' => null
            ])
            ->exists();

        if ($order_already_exists) return;

        $orderAttributes = array_merge([
            'is_recurring' => true,
            'type_id' => $subscription->fulfillment->setting('sales_channel', 1),
            'blueprint_id' => $subscription->id,
            'pickup_id' => $subscription->fulfillment_id,
            'status_id' => OrderStatus::confirmed(),
            'deadline_date' => $order_window->deadlineDatetime(),
            'pickup_date' => $order_window->deliveryDatetime(),
            'original_pickup_date' => $order_window->deliveryDatetime(),
            'customer_id' => $subscription->customer->id,
            'date_id' => $order_window->dateId(),
            'schedule_id' => $pickup_schedule->id,
            'payment_id' => $subscription->customer->setting('default_payment_method', 0),
            'payment_source_id' => $subscription->customer->checkout_card_id ?? null,
            'customer_first_name' => $subscription->customer->first_name,
            'customer_last_name' => $subscription->customer->last_name,
            'customer_phone' => $subscription->customer->phone,
            'customer_email' => $subscription->customer->email,
            'customer_email_alt' => $subscription->customer->hasSecondaryEmail()
                ? $subscription->customer->secondaryEmail()
                : null,
            'billing_street' => $subscription->customer->billing_street,
            'billing_street_2' => $subscription->customer->billing_street_2,
            'billing_city' => $subscription->customer->billing_city,
            'billing_state' => $subscription->customer->billing_state,
            'billing_zip' => $subscription->customer->billing_zip ?: '',
            'first_time_order' => false,
            'confirmed' => false, // setting false so calculator can run correctly
            'packed' => false,
            'paid' => false,
            'delivery_rate' => $subscription->fulfillment->delivery_rate,
            'delivery_fee_type' => $subscription->fulfillment->setting('delivery_fee_type', 1),
            'accounting_id' => $subscription->customer->accounting_id,
            'confirmed_date' => today(),
            'created_year' => today()->year,
            'created_month' => today()->month,
            'created_day' => today()->day,
            'containers' => app(SettingsService::class)->defaultParcelCount(),
        ], Order::defaultShippingAttributes($subscription->customer));

        $order = Order::create($orderAttributes);

        foreach ($subscription->items as $recurring_order_item) {
            if (is_null($recurring_order_item->product) || $recurring_order_item->qty <= 0) {
                $order->addTag('Product Error');
                continue;
            }

            $order->addRecurringItem($recurring_order_item);

            if ($recurring_order_item->type === 'addon') {
                $recurring_order_item->delete();
            }
        }

        $order->updateTotals();
        $order->refresh();

        if ($order->isDirty()) {
            $order->save();
        }

        $order->update([
            'confirmed' => true,
            'confirmed_date' => today(),
            'created_year' => today()->year,
            'created_month' => today()->month,
            'created_day' => today()->day,
        ]);

        $this->applyFees($order);
        $this->deductInventory($order);
        $this->updateCustomerAttributes($order);

        $order->updateTotals();
        $order->refresh();

        $this->applyTags($order);

        // reset current order
        Once::flush();

        event(new RecurringOrderWasConfirmed($order));
    }

    protected function applyFees(Order $order): void
    {
        if ($order->customer->exemptFromFees()) {
            return;
        }

        $order->fees()->where('user_id', false)->delete();

        $order->pickup->fees->each(function ($fee) use ($order) {
            OrderFee::create([
                'order_id' => $order->id,
                'title' => $fee->title,
                'qty' => 1,
                'amount' => $fee->amount,
                'taxable' => $fee->taxable,
                'apply_limit' => $fee->apply_limit,
                'threshold' => $fee->threshold,
                'cap' => $fee->cap,
                'subtotal' => $fee->amount,
            ]);
        });
    }

    protected function deductInventory(Order $order): void
    {
        $order->items->each(function (OrderItem $item)  {
            $item->fulfillInventoryWithoutThresholdCheck();
        });
    }

    protected function updateCustomerAttributes(Order $order): Order
    {
        $order->customer->last_purchase = now();
        $order->customer->order_count++;
        $order->customer->recurring_order_count++;

        $order->customer->save();

        return $order;
    }

    protected function applyTags(Order $order): void
    {
        app(ApplyTags::class)->handle($order);
    }

    public function tags(): array
    {
        return ['subscriptions'];
    }
}
