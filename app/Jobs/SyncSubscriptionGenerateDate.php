<?php

namespace App\Jobs;

use App\Models\Date;
use App\Models\RecurringOrder;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class SyncSubscriptionGenerateDate implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public function __construct(public int $subscriptionId)
    {}

    public function handle(): void
    {
        $subscription = RecurringOrder::find($this->subscriptionId);

        if (is_null($subscription)) {
            return;
        }

        $orderWindow = Date::query()
            ->where('active', true)
            ->where('pickup_date', '>=', $subscription->ready_at)
            ->where('schedule_id', $subscription->fulfillment->schedule_id)
            ->first()?->toOrderWindows();

        if (is_null($orderWindow)) {
            return;
        }

         $subscription->update([
             'generate_at' => $orderWindow->generatesAtDatetime(),
         ]);
    }
}
