<?php

namespace App\Jobs;

use App\Models\Order;
use App\Services\SettingsService;
use App\Traits\TenantContextMiddleware;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Collection;

class SyncSubscriptionOrderTagsAtDeadline implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, TenantContextMiddleware;

    public function handle(): void
    {
        $deadline_time = app(SettingsService::class)->deadlineEndTime();

       Order::query()
            ->selectRaw("
                id,
                CAST(concat(orders.deadline_date, ' ', '{$deadline_time}') AS DATETIME) as computed_deadline_at,
                CAST(concat(orders.pickup_date, ' ', '00:00:00') AS DATETIME) AS computed_ready_at
            ")
            ->where('pickup_date', '>=', today())
            ->whereNotNull(['blueprint_id', 'deadline_date', 'pickup_date'])
            ->where('canceled', false)
            ->whereNull('skipped_at')
            ->having('computed_deadline_at', '<=', now()->format('Y-m-d H:i:s'))
            ->chunkById(250, function (Collection $results) {
                /**
                 * @var Collection<Order{
                 *     id: int,
                 *     computed_deadline_at: string,
                 *     computed_ready_at: string
                 * }> $results
                 */
                SyncSubscriptionOrderTags::dispatch(
                    $results->map(fn (object $result) => $result->id)->toArray()
                );
            });
    }

    public function tags(): array
    {
        return ['subscriptions'];
    }
}
