<?php

namespace App\Jobs;

use App\Contracts\Mailer;
use App\Models\Setting;
use App\Models\Template;
use App\Models\User;
use App\Services\Html2Text;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class SendMarketingEmail implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $subject;
    public $message;
    public $pickupIds;
    public $mailer;

    public function __construct(string $subject, string $message, array $pickupIds)
    {
        $this->subject = $subject;
        $this->message = $message;
        $this->pickupIds = $pickupIds;
    }

    public function handle(): void
    {
        $domain = config('app.url');

        $template = Template::defaultCustom();

        $this->mailer = app(Mailer::class)
            ->from(config('mail.marketing_from.address'), $template->getFromName())
            ->replyTo($template->getReplyToEmail());

        // Convert Merge Tags to MailGun merge tags.
        $settings = Setting::pluck('value', 'key');

        $mergeTags = [
            '{customer_first_name}' => '%recipient.first_name%',
            '{customer_last_name}' => '%recipient.last_name%',
            '{customer_name}' => '%recipient.first_name% %recipient.last_name%',
            '{full_name}' => '%recipient.first_name% %recipient.last_name%',
            '{store_credit}' => '%recipient.credit%',
            '{pickup_title}' => '%recipient.pickup_title%',
            '{farm_name}' => $settings['farm_name'],
            '{farm_phone}' => $settings['farm_phone'],
            '{farm_street}' => $settings['farm_street'],
            '{farm_city}' => $settings['farm_city'],
            '{farm_state}' => $settings['farm_state'],
            '{farm_zip}' => $settings['farm_zip'],
            '{farm_postal_code}' => $settings['farm_zip'],
            '{farm_address}' => $settings['farm_street'] . ', ' . $settings['farm_city'] . ', ' . $settings['farm_state'] . ' ' . $settings['farm_zip']
        ];

        $this->message = str_ireplace(
            array_keys($mergeTags),
            array_values($mergeTags),
            $this->message
        );
        // END Convert Merge Tags to MailGun merge tags.

        // Merge message into template.
        $html = str_ireplace(
            ['{custom_message}', '{email_subject}'],
            [$this->message, $this->subject],
            $template->body
        );

        $text = str_ireplace(
            ['{custom_message}', '{email_subject}'],
            [(new Html2Text($this->message))->getText(), $this->subject],
            $template->plain_text
        );
        // END Merge message into template.

        // Compose email
        $html = view('emails.marketing-html')
            ->with([
                'content' => nl2br($html),
                'subject' => $this->subject,
                'styles' => $template->settings,
                'domain' => $domain,
            ])
            ->render();

        $text = view('emails.marketing-text')
            ->with([
                'content' => $text,
                'subject' => $this->subject,
                'domain' => $domain,
            ])
            ->render();
        // END compose email.

        User::whereIn('pickup_point', $this->pickupIds)
            ->where('order_deadline_email_reminder', true)
            ->where('email', '!=', '')
            ->with(['pickup'])
            ->select(['email', 'first_name', 'last_name', 'pickup_point', 'credit', 'order_deadline_email_reminder'])
            ->chunk(100, function ($users) use ($domain, $html, $text) {
                $recipientVariables = [];

                foreach ($users as $user) {
                    $recipientVariables[$user->email] = [
                        'first_name' => $user->first_name,
                        'last_name' => $user->last_name,
                        'pickup_title' => $user->pickup['title'] ?? '',
                        'credit' => money($user->credit),
                        'sender_domain' => $domain
                    ];
                }

                $this->mailer->send([
                    'recipients' => $users->pluck('email')->toArray(),
                    'subject' => $this->subject,
                    'html' => $html,
                    'text' => $text,
                    'recipient_variables' => $recipientVariables,
                    'tags' => ['Marketing'],
                ]);
            });
    }

    public function tags()
    {
        return ['email'];
    }
}
