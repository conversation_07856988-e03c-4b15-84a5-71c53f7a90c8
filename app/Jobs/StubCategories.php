<?php

namespace App\Jobs;

use App\Models\Category;
use App\Models\Product;
use App\Models\Subcollection;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Str;

class StubCategories implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public function handle(): void
    {
        Subcollection::query()
            ->select(['id', 'tag_id', 'title', 'description'])
            ->eachById(function (Subcollection $subcollection) {
                [$category, $subcategory] = explode('|', $subcollection->title);

                $category_name = trim($category);
                $category = Category::firstOrCreate([
                    'category_id' => null,
                    'name' => $category_name,
                ], [
                    'slug' => Str::slug($category_name),
                ]);

                $subcategory_name = trim($subcategory);
                $subcategory = Category::firstOrCreate([
                    'category_id' => $category->id,
                    'name' => $subcategory_name,
                ], [
                    'slug' => Str::slug($subcategory_name),
                    'description' => $subcollection->description,
                ]);

                Product::query()
                    ->withTrashed()
                    ->whereNull('category_id')
                    ->whereHas('tags', fn($query) => $query->where('tag_id', $subcollection->tag_id))
                    ->update(['category_id' => $subcategory->id]);
            });

        Product::query()
            ->withTrashed()
            ->select(['id', 'accounting_class'])
            ->where('accounting_class', '!=', '')
            ->whereNotNull('accounting_class')
            ->where('accounting_class', 'NOT LIKE', '%Gift Card%')
            ->where('accounting_class', 'NOT LIKE', '%Fee Income%')
            ->where('accounting_class', 'NOT LIKE', '%Packaging%')
            ->where('accounting_class', 'NOT LIKE', '%Box%')
            ->groupBy('accounting_class')
            ->orderBy('id')
            ->eachById(function (Product $product) {
                $category_name = trim($product->accounting_class);
                $category = Category::firstOrCreate([
                    'category_id' => null,
                    'name' => $category_name,
                ], [
                    'slug' => Str::slug($category_name),
                ]);

                Product::query()
                    ->whereNull('category_id')
                    ->where('accounting_class', $product->accounting_class)
                    ->update(['category_id' => $category->id]);
            });

    }

    public function tags(): array
    {
        return ['stub-categories'];
    }
}
