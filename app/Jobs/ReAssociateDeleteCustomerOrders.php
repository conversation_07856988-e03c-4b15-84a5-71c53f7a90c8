<?php

namespace App\Jobs;

use App\Models\Order;
use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Database\Query\Builder;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class ReAssociateDeleteCustomerOrders implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public function handle(): void
    {
        Order::query()
            ->whereNotIn('customer_id', fn(Builder $query) =>
                $query->select('id')->from('users')
            )
            ->update(['customer_id' => User::deletedCustomer()->id]);
    }

    public function tags(): array
    {
        return ['re-associated-deleted-customer-orders'];
    }
}
