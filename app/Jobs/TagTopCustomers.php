<?php

namespace App\Jobs;

use App\Models\Order;
use App\Models\Tag;
use App\Models\User;
use App\Support\Enums\Channel;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;

class TagTopCustomers implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public function __construct(
        public int $limit
    ) {}

    public function handle(): void
    {
        $tag = Tag::firstOrCreate([
            'title' => "Top {$this->limit}",
            'type' => Tag::type('user'),
        ]);

        DB::table('tag_user')
            ->where('tag_id', $tag->id)
            ->delete();

        $tag_user_records = Order::query()
            ->select('customer_id', DB::raw('SUM(total) as total_spent'))
            ->where('confirmed', true)
            ->where('canceled', false)
            ->whereIn('type_id', [Channel::BUYING_CLUBS, Channel::HOME_DELIVERY, Channel::SHIPPING])
            ->whereNot('customer_id', User::deletedCustomer()->id)
            ->whereDate('confirmed_date', '>=', today()->subYear())
            ->groupBy('customer_id')
            ->orderByDesc('total_spent')
            ->limit($this->limit)
            ->pluck('customer_id')
            ->map(fn($customer_id) => [
                'user_id' => $customer_id,
                'tag_id' => $tag->id,
            ])
            ->toArray();

        DB::table('tag_user')->insert($tag_user_records);
    }
}
