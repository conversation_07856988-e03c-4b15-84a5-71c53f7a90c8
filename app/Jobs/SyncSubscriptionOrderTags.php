<?php

namespace App\Jobs;

use App\Actions\Order\ApplyTags;
use App\Models\Order;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class SyncSubscriptionOrderTags implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public function __construct(
        public array $order_ids
    ) {}

    public function handle(): void
    {
        Order::query()
            ->whereNotNull('blueprint_id')
            ->where('canceled', 0)
            ->findMany($this->order_ids)
            ->each(function (Order $order) {
                app(ApplyTags::class)->handle($order);
            });
    }

    public function tags(): array
    {
        return ['subscriptions'];
    }
}
