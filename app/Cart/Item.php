<?php

namespace App\Cart;

use Illuminate\Support\Collection;

class Item
{
    public function __construct(
        public $id,
        public \App\Models\Product $product,
        public int $quantity = 1,
        protected ?int $price = null,
        protected ?float $weight = null,
    ) {}

    public function subtotal(): int
    {
        return (int) round($this->price() * $this->quantity);
    }

    public function price(): int
    {
        return ! is_null($this->price)
            ? $this->price
            : $this->product->getPrice($this->quantity);
    }

    public function weight(): float
    {
        if ( ! is_null($this->weight)) return $this->weight;

        return $this->product->weight * $this->quantity;
    }

    public function isTaxable(): bool
    {
        return (bool) $this->product->taxable;
    }

    public function isEligibleForSubscriptionSavings(): bool
    {
        return ! $this->product->isGiftCard();
    }

    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'product' => [
                'id' => $this->product->id,
            ],
            'quantity' => $this->quantity,
            'price' => $this->price,
            'weight' => $this->weight,
        ];
    }

    public function quantitiesByProductId(): Collection
    {
        if ( ! $this->product->tracksInventoryByBundleItem()) {
            return collect([$this->product->id => $this->quantity]);
        }

        return $this->product
            ->bundle
            ->mapWithKeys(fn(\App\Models\Product $product) => [
                $product->id => $this->quantity * $product->getRelationValue('pivot')->qty
            ]);
    }
}
