<?php

namespace App\Cart\Validators;

use App\Contracts\Cartable;

class MeetsDeliveryMethodOrderMinimum
{
    /**
     * @throws CartValidationException
     */
    public function handle(Cartable $cart, \Closure $next): Cartable
    {
        if ( ! $cart->meetsOrderMinimum()) {
            throw new CartValidationException(
                rule: MeetsDeliveryMethodOrderMinimum::class,
                message: 'The subtotal must be at least $' . money($cart->cartLocation()->min_customer_orders). ' before an order can be placed.'
            );
        }

        return $next($cart);
    }
}