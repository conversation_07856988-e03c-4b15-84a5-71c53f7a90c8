<?php

namespace App\Cart\Validators;

use App\Contracts\Cartable;

class IsNotEmpty
{
    /**
     * @throws CartValidationException
     */
    public function handle(Cartable $cart, \Closure $next): Cartable
    {
        if ($cart->cartIsEmpty()) {
            throw new CartValidationException(
                rule: IsNotEmpty::class,
                message: 'You have not added any items to your cart yet.'
            );
        }

        return $next($cart);
    }
}