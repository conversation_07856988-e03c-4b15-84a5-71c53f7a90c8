<?php

namespace App\Cart;

use App\Support\Enums\ProductType;

class Product
{
    public function __construct(
        public $id,
        public int $type_id,
        public string $unit_of_issue,
        public string $title,
        public int $unit_price,
        public float $unit_weight,
        public bool $is_taxable
    ) {}

    public function isPricedByWeight(): bool
    {
        return $this->unit_of_issue === 'weight';
    }

    public function isGiftCard(): bool
    {
        return $this->type_id === ProductType::GIFT_CARD->value;
    }

    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'type_id' => $this->type_id,
            'unit_of_issue' => $this->unit_of_issue,
            'title' => $this->title,
            'unit_price' => $this->unit_price,
            'unit_weight' => $this->unit_weight,
            'is_taxable' => $this->is_taxable
        ];
    }
}
