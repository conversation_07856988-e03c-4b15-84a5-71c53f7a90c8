<?php

namespace App\Support\Enums;

use Illuminate\Support\Arr;
use Illuminate\Support\Collection;

enum Channel: int
{
    case ONLINE_STORE = 1;

    case WHOLESALE = 2;

    case DISTRIBUTION = 3;

    case FARM_STORE = 4;

    case FARMERS_MARKET = 5;

    case AFFILIATE = 6;

    case BUYING_CLUBS = 7;

    case HOME_DELIVERY = 8;

    case SHIPPING = 9;

    public static function ids(): array
    {
        return Channel::all()
            ->keys()
            ->toArray();
    }

    /**
     * @return Collection<int, string>
     */
    public static function all(): Collection
    {
        return collect(Channel::cases())
            ->mapWithKeys(fn (Channel $status): array => Channel::mappedToLabel($status));
    }

    public static function allByDesc(): Collection
    {
        return collect(self::cases())
            ->sortByDesc(function ($channel) {
                return $channel->value;
            })
            ->mapWithKeys(fn (Channel $status): array => Channel::mappedToLabel($status));
    }

    /**
     * @return array<int, string>
     */
    private static function mappedToLabel(Channel $channel): array
    {
        return [$channel->value => $channel->label()];
    }

    public function label(): string
    {
        return match ($this) {
            Channel::ONLINE_STORE => 'Online Store',
            Channel::WHOLESALE => 'Wholesale',
            Channel::DISTRIBUTION => 'Distribution',
            Channel::FARM_STORE => 'Farm Store',
            Channel::FARMERS_MARKET => 'Farmers Market',
            Channel::AFFILIATE => 'Affiliate',
            Channel::BUYING_CLUBS => 'Buying Clubs',
            Channel::HOME_DELIVERY => 'Home Delivery',
            Channel::SHIPPING => 'Shipping',
        };
    }

    public static function get(int $channel_id): ?string
    {
        return Channel::tryFrom($channel_id)?->label();
    }

    /**
     * @return Collection<int, string>
     */
    public static function whereIn(mixed $channel_ids): Collection
    {
        return Channel::all()
            ->filter(fn ($value, $key) => in_array($key, Arr::wrap($channel_ids)));
    }
}
