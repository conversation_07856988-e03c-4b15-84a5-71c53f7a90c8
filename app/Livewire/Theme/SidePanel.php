<?php

namespace App\Livewire\Theme;

use Illuminate\Contracts\View\View as ViewContract;
use Livewire\Component;

class SidePanel extends Component
{
    public bool $open = false;
    public string $title = 'Default Panel';
    public string $component = '';
    public array $params = [];

    protected $listeners = [
        'openPanel',
        'closePanel'
    ];

    public function openPanel(string $title, string $component, array $params = []): void
    {
        $this->open = true;
        $this->title = $title;
        $this->component = $component;
        $this->params = $params;
    }

    public function closePanel(): void
    {
        $this->open = false;
        $this->title = 'Default Panel';
        $this->component = '';
    }

    public function render(): ViewContract
    {
        return view('theme::livewire.side-panel', ['component' => $this->component]);
    }
}
