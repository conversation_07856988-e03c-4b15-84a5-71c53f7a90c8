<?php

namespace App\Livewire\Theme;

use App\Contracts\Cartable;
use App\Models\Pickup;
use App\Services\SubscriptionSettingsService;
use Livewire\Component;

class CartSidePanel extends Component
{
    use FetchesCart;

    public ?Cartable $cart = null;
    public ?Pickup $delivery_method = null;
    public bool $is_subscribing = false;

    protected $listeners = [
        'cartUpdated' => 'refreshCart',
    ];

    public function mount()
    {
        $this->refreshCart();
    }

    public function refreshCart()
    {
        $this->cart = $this->fetchShopperCart(should_stub: false);
        $this->delivery_method = $this->cart?->cartLocation();
        $this->is_subscribing = $this->cart?->isRecurring() ?? false;
    }

    public function render()
    {
        $subscription_settings_service = app(SubscriptionSettingsService::class);

        return view('theme::livewire.cart-side-panel', [
            'subscription_settings_service' => $subscription_settings_service
        ]);
    }

    public function toggleSubscription()
    {
        if ($this->cart->isRecurring()) {
            $this->cart->setCartAsOneTimePurchase();
            $this->is_subscribing = false;
        } else {
            $this->cart->setCartAsSubscriptionPurchase(
                frequency: null,
                product_incentive_id: app(SubscriptionSettingsService::class)->defaultProductIncentiveId()
            );
            $this->is_subscribing = true;

        }

        $this->dispatch('cartUpdated');
    }
}
