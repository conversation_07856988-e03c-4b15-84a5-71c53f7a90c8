<?php

namespace App\Livewire\Theme;

use App\Events\Order\OrderUpdated;
use App\Models\Order;
use Livewire\Component;

class OrderShow extends Component
{
    use FetchesOrder;

    public Order $order;

    protected $listeners = [
        'orderUpdated' => 'refreshOrder',
    ];

    public function mount()
    {
        $this->refreshOrder();
    }

    public function refreshOrder()
    {
        $order = $this->fetchCustomerOrder();

        if (is_null($order)) {
            return $this->redirect(route('customer.orders'));
        }

        $this->order = $order;
    }

    public function render()
    {
        return view('theme::livewire.order-show');
    }

    public function cancel()
    {
        $this->order->cancel();

        event(new OrderUpdated($this->order));

        $this->dispatch('orderUpdated');

        return $this->redirect(route('customer.orders.show', [$this->order]));
    }
}
