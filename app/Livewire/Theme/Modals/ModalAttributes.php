<?php

namespace App\Livewire\Theme\Modals;

use App\Livewire\Admin\SendsAdminNotifications;
use Livewire\Attributes\On;

trait ModalAttributes
{
    use SendsAdminNotifications;
    
    public bool $open = false;

    #[On('modal-escaped')]
    public function handleEscape(): void
    {
        $this->close();
    }

    public function close(): void
    {
        $this->closeModal();
    }

    protected function closeModal(): void
    {
        $this->open = false;
    }

    protected function openModal(): void
    {
        $this->open = true;
    }
}
