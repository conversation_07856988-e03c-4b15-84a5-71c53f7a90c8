<?php

namespace App\Livewire\Theme;

use App\Actions\Billing\SetDefaultCard;
use App\Models\Order;
use App\Models\RecurringOrder;
use Illuminate\Validation\Rule;
use Livewire\Attributes\Locked;
use Livewire\Component;

class SubscriptionPaymentMethod extends Component
{
    use SendsStorefrontNotifications;

    #[Locked]
    public int $subscription_id;

    #[Locked]
    public ?int $current_order_id = null;

    public ?string $payment_source_id = null;

    public function mount()
    {
        if (!is_null($this->current_order_id)) {
            $this->payment_source_id = Order::query()
                ->select(['id', 'payment_source_id'])
                ->with('paymentSource')
                ->find($this->current_order_id)
                ?->paymentSource
                ?->source_id;
        }
    }

    public function render()
    {
        $subscription = RecurringOrder::with('customer')
            ->find($this->subscription_id);

        $current_order = null;

        if (!is_null($this->current_order_id)) {
            $current_order = Order::with('paymentMethod', 'paymentSource')
                ->find($this->current_order_id);
        }

        return view('theme::livewire.subscription-payment-method', compact('subscription', 'current_order'));
    }

    public function updatePaymentSource()
    {
        $validated = $this->validate([
            'payment_source_id' => [
                'required',
                Rule::exists('user_cards', 'source_id')
                    ->where('user_id', auth()->id())
            ],
        ]);

        $payment_souce = auth()->user()
            ->cards()
            ->where('source_id', $validated['payment_source_id'])
            ->with(['user'])
            ->first();

        if (is_null($payment_souce)) {
            return;
        }

        try {
            app(SetDefaultCard::class)->handle($payment_souce);

        } catch (\Exception $exception) {
            $this->sendStorefrontNotification([
                'level' => 'error',
                'title' => 'Error!',
                'message' => 'Unable to update payment method. Please try again.',
            ]);

            return;
        }

        $this->sendStorefrontNotification([
            'level' => 'success',
            'title' => 'Success!',
            'message' => 'Your subscription payment method has been updated!',
        ]);
    }
}
