<?php

namespace App\Livewire\Theme;

use App\Actions\Order\SyncItemToSubscription;
use App\Actions\Subscription\SyncItemToCurrentOrder;
use App\Cart\Item;
use App\Events\Cart\CartUpdated;
use App\Events\Order\OrderUpdated;
use App\Events\Subscription\SubscriptionUpdated;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Product;
use Exception;

trait DecrementsProduct
{
    use FetchesCart, FetchesOrder, FetchesSubscription;

    public Product $product;

    public bool $has_subscription = false;
    public bool $has_order = false;

    public function decrementFromCart(int $product_id, array $metadata = []): void
    {
        $cart = $this->fetchShopperCart(should_stub: false);

        if (is_null($cart)) {
            return;
        }

        /** @var Item|null $item */
        $item = $cart->itemsInCart()
            ->firstWhere(fn(Item $item) => $item->product->id === $this->product->id);

        if (is_null($item)) {
            return;
        }

        try {
            $cart = $cart->decrementCartItemQuantity($item->id);
        } catch (Exception $exception) {
            $this->dispatch('openModal', title: 'Unable to decrement product', message: $exception->getMessage());
            return;
        }

        if ($cart->isRecurring() && ! $cart->hasSubscriptionEligibleItems()) {
            $cart->setCartAsOneTimePurchase();
        }

        $this->dispatch('item-removed-from-cart', item: [
            'quantity' => $item->quantity === 1 ? 0 : $item->quantity - 1,
            'product' => [
                'id' => $item->product->id,
                'title' => $item->product->title,
                'price' => $item->price()
            ]
        ], metadata: $metadata);

        event(new CartUpdated($cart));

        $this->dispatch('cartUpdated');
    }

    public function decrementFromOrder(int $product_id): void
    {
        /** @var Order|null $order */
        $order = $this->fetchCustomerOrder();

        if (is_null($order)) {
            return;
        }

        if (! $order->canBeModified()) {
            $this->dispatch('openModal', title: 'Unable to update product', message: 'The order is processing and is no longer editable.');
            return;
        }

        /** @var OrderItem|null $item */
        $item = $order->items()
            ->where('product_id' , $this->product->id)
            ->first();

        if (is_null($item)) return;

        if ($item->qty - 1 <= 0) {
            $this->removeItem($item->id);
            return;
        }

        try {
            $updated_order_item = $order->updateItemQuantity($item, $item->qty - 1);
        } catch (\Exception $exception) {
            $this->dispatch('openModal', title: 'Unable to update quantity', message: $exception->getMessage());
            return;
        }

        if ( ! is_null($order->blueprint) && $updated_order_item->type !== 'addon') {
            app(SyncItemToSubscription::class)->handle($updated_order_item);
        }

        event(new OrderUpdated($order));
        if ($order->isFromBlueprint()) {
            event(new SubscriptionUpdated($order->blueprint));
        }

        $this->dispatch('orderUpdated');
    }

    public function removeItem(int $id)
    {
        /** @var Order|null $order */
        $order = $this->fetchCustomerOrder();

        if (is_null($order)) {
            return;
        }

        if ($order->deadlineHasPassed()) {
            $this->dispatch('openModal', title: 'Unable to remove item', message: 'The order modification deadline has passed.');
            return;
        }

        $item = $order->items()->find($id);

        if (is_null($item)) return;

        try {
            $order->updateItemQuantity($item, 0);
        } catch (\Exception $exception) {
            $this->dispatch('openModal', title: 'Unable to remove item', message: $exception->getMessage());
            return;
        }

        if ( ! is_null($order->blueprint)) {
            $order->blueprint
                ->items()
                ->where([
                    'product_id' => $item->product_id,
                    'type' => $item->type === 'standard' ? 'recurring' : $item->type
                ])
                ->delete();
        }

        event(new OrderUpdated($order));

        if ($order->isFromBlueprint()) {
            event(new SubscriptionUpdated($order->blueprint));
        }

        $this->dispatch('orderUpdated');
    }

    public function decrementFromSubscription(int $product_id): void
    {
        $subscription = $this->fetchCustomerSubscription();

        $current_order = $subscription?->currentOrder;

        if ( ! is_null($current_order) && ! $current_order->deadlineHasPassed() && ! $current_order->isNew()) {
            $this->dispatch('openModal', title: 'Unable to add product', message: 'The order is processing and is no longer editable.');
            return;
        }

        $product = $this->fetchProduct($product_id, $subscription->pricingGroupId());

        if (is_null($product)) {
            $this->dispatch('openModal', title: 'Unable to add product', message: 'The product is not longer available');
            return;
        }

        // Validate that product meets order minimum.
        $order_minimum = $product->setting('order_minimum', 0);

        if ($subscription->subtotal() < formatCurrencyForDB($order_minimum)) {
            $this->dispatch('openModal', title: 'Unable to add product', message: 'Your order total must be at least $' . money(formatCurrencyForDB($order_minimum)) . ' to purchase ' . $product->title);
            return;
        }

        try {
            $subscription_item = $subscription->addItem($product);
        } catch (Exception $e) {
            $this->dispatch('openModal', title: 'Unable to add product', message: $e->getMessage());
            return;
        }

        event(new SubscriptionUpdated($subscription));

        if ( ! is_null($current_order) && ! $current_order->deadlineHasPassed()) {
            app(SyncItemToCurrentOrder::class)->handle($subscription_item);
        } else {
            $subscription_item->unit_price_override = $product->getUnitPrice();
            $subscription_item->save();
        }

        $this->dispatch('subscriptionUpdated');
        $this->dispatch('openPanel', title: 'Subscription', component: 'theme.subscription-side-panel');
    }

}
