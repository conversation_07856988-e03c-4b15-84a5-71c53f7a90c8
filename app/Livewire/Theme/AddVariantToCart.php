<?php

namespace App\Livewire\Theme;

use App\Models\Product;
use Illuminate\Contracts\View\View as ViewContract;
use Livewire\Component;

class AddVariantToCart extends Component
{
    use AddsProduct;

    public ?int $selected_variant_id = null;

    public ?string $cta_label = null;
    
    public ?string $cta_classes = null;

    public function mount()
    {
        $this->selected_variant_id = $this->product->id;

        if ($this->product->isOutOfStock()) {
            $this->selected_variant_id = $this->product
                ->variants
                ->first(fn(Product $variant) => ! $variant->isOutOfStock())
                ?->id;
        }
    }

    public function render(): ViewContract
    {
        return view('theme::livewire.add-variant-to-cart');
    }

    public function add()
    {
        if (is_null($this->selected_variant_id)) return;

        match(true) {
            $this->has_subscription => $this->addToSubscription($this->selected_variant_id),
            $this->has_order => $this->addToOrder($this->selected_variant_id),
            default => $this->addToCart($this->selected_variant_id),
        };

        $this->refreshProduct();
    }
}
