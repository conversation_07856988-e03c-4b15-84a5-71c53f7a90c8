<?php

namespace App\Livewire\Theme;

use App\Contracts\Cartable;
use App\Events\Cart\CartEmptied;
use App\Events\Cart\CartUpdated;
use App\Services\SubscriptionSettingsService;
use Livewire\Component;

class CartItems extends Component
{
    use FetchesCart;

    public Cartable $cart;

    protected $listeners = [
        'cartUpdated' => 'refreshCart',
    ];

    public function render()
    {
        return view('theme::livewire.cart-items');
    }

    public function removeItem(string $id)
    {
        try {
            $this->cart->removeCartItem($id);
        } catch (\Exception $exception) {
            $this->dispatch('openModal', title: 'Unable to remove item', message: $exception->getMessage());
            return;
        }

        if ($this->cart->isRecurring() && ! $this->cart->hasSubscriptionEligibleItems()) {
            $this->cart->setCartAsOneTimePurchase();
        }

        if ($this->cart->cartIsEmpty()) {
            CartEmptied::dispatch($this->cart);
        }

        event(new CartUpdated($this->cart));

        $this->dispatch('cartUpdated');
    }

    public function incrementItemQuantity(string $id)
    {
        try {
            $this->cart->incrementCartItemQuantity($id);
        } catch (\Exception $exception) {
            $this->dispatch('openModal', title: 'Unable to update quantity', message: $exception->getMessage());
            return;
        }

        event(new CartUpdated($this->cart));

        $this->dispatch('cartUpdated');
    }

    public function decrementItemQuantity(string $id)
    {
        try {
            $this->cart->decrementCartItemQuantity($id);
        } catch (\Exception $exception) {
            $this->dispatch('openModal', title: 'Unable to update quantity', message: $exception->getMessage());
            return;
        }

        if ($this->cart->isRecurring() && ! $this->cart->hasSubscriptionEligibleItems()) {
            $this->cart->setCartAsOneTimePurchase();
        }

        event(new CartUpdated($this->cart));

        $this->dispatch('cartUpdated');
    }

    public function toggleSubscription()
    {
        if ($this->cart->isRecurring()) {
            $this->cart->setCartAsOneTimePurchase();
        } else {
            $this->cart->setCartAsSubscriptionPurchase(
                frequency: null,
                product_incentive_id: app(SubscriptionSettingsService::class)->defaultProductIncentiveId()
            );
        }

        event(new CartUpdated($this->cart));

        $this->dispatch('cartUpdated');
    }

    public function refreshCart()
    {
        $this->cart = $this->fetchShopperCart();
    }
}
