<?php

namespace App\Livewire\Theme;

use App\Services\StoreService;
use Livewire\Component;

class SubscriptionItemCount extends Component
{
    public int $count;

    protected $listeners = [
        'subscriptionUpdated' => 'refreshCount',
    ];

    public function mount()
    {
       $this->refreshCount();
    }

    public function refreshCount()
    {
        $this->count = app(StoreService::class)->subscriptionItemCount();
    }

    public function render()
    {
        return <<<'blade'
            <span>{{ $count }}</span>
        blade;
    }
}
