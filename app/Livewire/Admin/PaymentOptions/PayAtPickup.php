<?php

namespace App\Livewire\Admin\PaymentOptions;

use App\Models\Payment;
use Livewire\Component;

class PayAtPickup extends Component
{
    public Payment $payment_option;

    public bool $enabled;
    public string $title;
    public string $instructions;

    protected $rules = [
        'enabled' => ['required', 'boolean'],
        'title' => ['required', 'string'],
        'instructions' => ['nullable', 'string', 'max:1500'],
    ];

    protected $messages = [
        'title.required' => 'The label field is required.',
    ];

    public function mount(Payment $payment_option): void
    {
        $this->enabled = (bool) $payment_option->enabled;
        $this->title = $payment_option->title;
        $this->instructions = $payment_option->instructions;
    }

    public function render()
    {
        return view('livewire.payment-options.pay-at-pickup');
    }

    public function toggle()
    {
        $this->enabled = ! $this->enabled;
        $this->save();
    }

    public function save()
    {
        $validated = $this->validate();

        $this->payment_option->update($validated);
    }
}
