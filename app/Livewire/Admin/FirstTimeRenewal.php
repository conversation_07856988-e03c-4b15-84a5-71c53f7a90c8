<?php

namespace App\Livewire\Admin;

use App\Exports\FirstTimeRenewalExport;
use App\Models\RecurringOrder;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Livewire\Component;
use Livewire\WithPagination;
use Maatwebsite\Excel\Facades\Excel;
use Symfony\Component\HttpFoundation\BinaryFileResponse;

class FirstTimeRenewal extends Component
{
    use WithPagination;

    public string $event_start_date = '';

    public string $event_end_date = '';

    protected $queryString = [
        'event_start_date' => ['except' => ''],
        'event_end_date' => ['except' => ''],
    ];

    public function mount()
    {
        if (empty($this->event_start_date)) {
            $this->event_start_date = today()->subDays(14)->format('Y-m-d');
        }

        if (empty($this->event_end_date)) {
            $this->event_end_date = today()->subDays(7)->format('Y-m-d');
        }
    }

    public function render()
    {
        return view('livewire.first-time-renewal', [
            'subscriptions' => $this->query()->paginate(25),
        ]);
    }

    private function query()
    {
        return RecurringOrder::query()
            ->whereHas('orders', function (Builder $query) {
                return $query->where('canceled', false);
            }, '=', 1)
            ->where(function ($q) {
                $starts_at = ! empty($this->event_start_date)
                    ? Carbon::parse($this->event_start_date)->startOfDay()
                    : today()->startOfDay();
                $ends_at = ! empty($this->event_end_date)
                    ? Carbon::parse($this->event_end_date)->endOfDay()
                    : today()->addWeek()->endOfDay();

                return $q->whereBetween('recurring_orders.ready_at', [$starts_at, $ends_at]);
            })
            ->with([
                'customer' => fn ($query) => $query->select(['id', 'first_name', 'last_name', 'phone', 'email']),
                'fulfillment'  => fn ($query) => $query->select(['id', 'schedule_id', 'title']),
                'fulfillment.schedule'  => fn ($query) => $query->select(['id', 'title']),
            ])
            ->orderBy('recurring_orders.ready_at');
    }

    public function export(): BinaryFileResponse
    {
        return Excel::download(
            new FirstTimeRenewalExport($this->query()->get()),
            'first_time_renewal_'.now()->format('Y_m_d_H_i_s').'.csv'
        );
    }
}
