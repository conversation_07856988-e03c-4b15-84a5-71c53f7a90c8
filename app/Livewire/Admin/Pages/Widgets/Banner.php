<?php

namespace App\Livewire\Admin\Pages\Widgets;

use Illuminate\Validation\Rule;
use Livewire\Component;

class Banner extends Component
{
    use HasPageWidgetSettings;
    use HasIdentifiers;
    use HasSizing;
    use HasBackground;

    public string $heading_image = '';
    public string $heading_max_width = '';
    public string $subheading_content = '';
    public string $cta_label = '';
    public string $cta_url = '';

    public function mount(int $page_id, array $widget)
    {
        $this->page_id = $page_id;
        $this->widget = $widget;

        $this->initializeWidget($widget);
    }

    protected function initializeWidget(array $widget): void
    {
        $this->initializeIdentifiers($widget['settings']);
        $this->initializeSizing($widget['settings']);
        $this->initializeBackground($widget['settings']);

        $this->heading_image = $widget['settings']['heading']['image'] ?? '';
        $this->heading_max_width = $widget['settings']['heading']['max_width'] ?? false;
        $this->subheading_content = $widget['settings']['subheading']['content'] ?? '';
        $this->cta_label = $widget['settings']['cta']['label'] ?? '';
        $this->cta_url = $widget['settings']['cta']['url'] ?? '';
    }

    public function render()
    {
        return view('livewire.pages.widgets.banner');
    }

    public function save()
    {
        $this->validate($this->rules());

        $this->savePageWidgetSettings([
            'name' => $this->name,
            'html_id' => $this->html_id,
            'max_width' => $this->max_width,
            'padding' => [
                'top' => $this->padding_top,
                'bottom' => $this->padding_bottom,
            ],
            'background' => [
                'color' => $this->background_color,
                'image' => $this->background_image,
                'attachment' => $this->background_attachment,
                'vertical_position' => $this->background_vertical_position,
                'overlay' => $this->background_overlay,
            ],
            'heading' => [
                'image' => $this->heading_image,
                'max_width' => $this->heading_max_width,
            ],
            'subheading' => [
                'content' => $this->subheading_content,
            ],
            'cta' => [
                'label' => $this->cta_label,
                'url' => $this->cta_url,
            ],
        ]);

        $this->dispatch('widget-updated');
    }

    protected function rules()
    {
        return array_merge(
            $this->identiferRules(),
            $this->sizingRules(),
            $this->backgroundRules(),
            [
                'heading_image' => ['nullable', 'string'],
                'heading_max_width' => ['nullable', Rule::in(['none', 'sm', 'md', 'lg', 'xl'])],
                'subheading_content' => ['nullable', 'string'],
                'cta_label' => ['nullable', 'string', 'max:255'],
                'cta_url' => ['nullable', 'string'],
            ]
        );
    }
}
