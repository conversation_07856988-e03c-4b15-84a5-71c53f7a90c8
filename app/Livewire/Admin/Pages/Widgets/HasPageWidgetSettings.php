<?php

namespace App\Livewire\Admin\Pages\Widgets;

use App\Livewire\Admin\SendsAdminNotifications;
use App\Models\Page;
use Livewire\Attributes\Locked;

trait HasPageWidgetSettings
{
    use SendsAdminNotifications;

    #[Locked]
    public int $page_id;

    #[Locked]
    public array $widget;

    public function preview()
    {
        $this->dispatch('open-modal-preview-widget', $this->widget);
    }

    public function cancel(): void
    {
        $this->dispatch('close-modal-edit-widget');
    }

    public function deleteWidget(string $widget_id): void
    {
        /** @var Page $page */
        $page = Page::findOrFail($this->page_id);

        $content = collect($page->settings->content ?? []);

        $page->settings->content = $content
            ->reject(fn($widget) => $widget['id'] === $widget_id)
            ->toArray();

        $page->save();

        $this->sendAdminNotification([
            'level' => 'success',
            'title' => 'Widget deleted!',
            'message' => 'The page has been updated.',
        ]);

        $this->dispatch('widget-deleted');
    }

    abstract protected function initializeWidget(array $widget): void;

    protected function savePageWidgetSettings(array $settings): void
    {
        /** @var Page $page */
        $page = Page::findOrFail($this->page_id);

        $content = collect($page->settings->content ?? []);

        $widget_index = $content->search(fn($widget) => $widget['id'] === $this->widget['id']);

        $widget = $content[$widget_index];
        $widget['settings'] = $settings;

        $page->settings->content = $content
            ->replace([$widget_index => $widget])
            ->toArray();

        $page->save();

        $this->sendAdminNotification([
            'level' => 'success',
            'title' => 'Widget updated!',
            'message' => 'The widget settings have been successfully updated.',
        ]);
    }
}
