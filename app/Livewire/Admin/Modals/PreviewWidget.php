<?php

namespace App\Livewire\Admin\Modals;

use App\Livewire\Theme\Modals\ModalAttributes;
use Livewire\Attributes\Locked;
use Livewire\Attributes\On;
use Livewire\Component;

class PreviewWidget extends Component
{
    use ModalAttributes;

    #[Locked]
    public int $page_id;

    #[Locked]
    public ?array $widget = null;

    public function render()
    {
        return view('livewire.modals.preview-widget');
    }

    #[On('close-modal-preview-widget')]
    public function close(): void
    {
        $this->widget = null;
        $this->closeModal();
    }

    #[On('open-modal-preview-widget')]
    public function open(array $widget): void
    {
        $this->widget = $widget;
        $this->openModal();
    }
}
