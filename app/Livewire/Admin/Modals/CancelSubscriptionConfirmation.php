<?php

namespace App\Livewire\Admin\Modals;

use App\Events\Subscription\SubscriptionUpdated;
use App\Livewire\Theme\Modals\ModalAttributes;
use App\Models\RecurringOrder;
use Livewire\Attributes\On;
use Livewire\Component;

class CancelSubscriptionConfirmation extends Component
{
    use ModalAttributes;

    public ?int $subscription_id = null;

    public function render()
    {
        return view('livewire.modals.cancel-subscription-confirmation');
    }

    public function submit()
    {
        $subscription = RecurringOrder::find($this->subscription_id);

        $subscription?->cancel();

        if (!is_null($subscription)) {
            event(new SubscriptionUpdated($subscription));
        }

        event(SubscriptionUpdated::class);

        $this->dispatch('subscriptionUpdated');

        $this->close();

        return $this->redirect(route('admin.subscriptions.edit', [
            'subscription' => $this->subscription_id
        ]));
    }

    #[On('close-modal-cancel-subscription-confirmation')]
    public function close(): void
    {
        $this->closeModal();
    }

    #[On('open-modal-cancel-subscription-confirmation')]
    public function open(int $subscription_id): void
    {
        $this->subscription_id = $subscription_id;
        $this->openModal();
    }
}
