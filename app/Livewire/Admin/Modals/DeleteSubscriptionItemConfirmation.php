<?php

namespace App\Livewire\Admin\Modals;

use App\Events\Subscription\SubscriptionUpdated;
use App\Livewire\Theme\Modals\ModalAttributes;
use App\Models\RecurringOrderItem;
use Livewire\Attributes\On;
use Livewire\Component;

class DeleteSubscriptionItemConfirmation extends Component
{
    use ModalAttributes;

    public ?int $item_id = null;

    public function render()
    {
        return view('livewire.modals.delete-subscription-item-confirmation');
    }

    public function submit()
    {
        $item = RecurringOrderItem::find($this->item_id);

        $subscription = $item?->order;

        $item?->delete();

        if (!is_null($subscription)) {
            event(new SubscriptionUpdated($subscription));
        }

        $this->dispatch('subscriptionUpdated');
        $this->close();
    }

    #[On('close-modal-delete-subscription-item-confirmation')]
    public function close(): void
    {
        $this->closeModal();
    }

    #[On('open-modal-delete-subscription-item-confirmation')]
    public function open(int $item_id): void
    {
        $this->item_id = $item_id;
        $this->openModal();
    }
}
