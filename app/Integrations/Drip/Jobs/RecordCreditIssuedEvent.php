<?php

namespace App\Integrations\Drip\Jobs;

use App\Integrations\Drip\DripIntegration;
use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class RecordCreditIssuedEvent implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $userId;

    public $params;

    public function __construct(int $userId, array $params = [])
    {
        $this->userId = $userId;
        $this->params = $params;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $drip = DripIntegration::getInstance();

        if (is_null($drip)) return;

        $user = User::findOrFail($this->userId);

        $drip->recordEvent($user->email, 'Credit issued', $this->params);
    }

    public function tags()
    {
        return ['integration', 'drip'];
    }
}
