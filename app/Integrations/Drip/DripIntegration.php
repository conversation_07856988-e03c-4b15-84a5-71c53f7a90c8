<?php

namespace App\Integrations\Drip;

use App\Events\Cart\CartCreated;
use App\Events\Cart\CartEmptied;
use App\Events\Checkout\CheckoutInitiated;
use App\Events\Order\OrderStatusWasUpdated;
use App\Events\Order\OrderWasCanceled;
use App\Events\Order\OrderWasConfirmed;
use App\Events\Subscription\RecurringOrderCanceled;
use App\Events\Subscription\RecurringOrderCreated;
use App\Events\Subscription\SubscriptionFrequencyWasUpdated;
use App\Events\Subscription\SubscriptionWasSkipped;
use App\Events\User\CreditIssued;
use App\Events\User\LeadCreated;
use App\Events\User\UserSubscribedToNewsletter;
use App\Events\User\UserUnsubscribedFromNewsletter;
use App\Events\User\UserUpdated;
use App\Events\User\UserWasDeleted;
use App\Events\User\UserWasRegistered;
use App\Integrations\Drip\Listeners\CreateUser;
use App\Integrations\Drip\Listeners\CreditIssuedListener;
use App\Integrations\Drip\Listeners\ListenForRecurringOrderCanceled;
use App\Integrations\Drip\Listeners\ListenForRecurringOrderCreated;
use App\Integrations\Drip\Listeners\ListenForSubscriptionFrequencyWasUpdated;
use App\Integrations\Drip\Listeners\RecordCanceledOrder;
use App\Integrations\Drip\Listeners\RecordCartCreated;
use App\Integrations\Drip\Listeners\RecordCartEmptied;
use App\Integrations\Drip\Listeners\RecordFirstOrderReceived;
use App\Integrations\Drip\Listeners\RecordInitiatedCheckout;
use App\Integrations\Drip\Listeners\RecordPurchase;
use App\Integrations\Drip\Listeners\RecordSubscriptionWasSkipped;
use App\Integrations\Drip\Listeners\SubscribeLead;
use App\Integrations\Drip\Listeners\SubscribeUser;
use App\Integrations\Drip\Listeners\UnsubscribeUser;
use App\Integrations\Drip\Listeners\UpdateSubscriber;
use App\Integrations\Drip\Listeners\UserDeleted;
use App\Models\Integration;
use Illuminate\Support\Facades\Event;

class DripIntegration
{
    public function __construct(
        protected Integration $integration
    ) {}

    public static function getInstance(): ?Drip
    {
        if (is_null($integration = Integration::where('name', 'drip')->enabled()->first())) {
            return null;
        }

        return app(Drip::class)->configure(
            $integration->setting('api_key'),
            $integration->setting('account_id')
        );
    }

    public function boot(): void
    {
        if ($this->apiKeyIsValid()) {
            Event::listen(LeadCreated::class, SubscribeLead::class);
            Event::listen(UserUpdated::class, UpdateSubscriber::class);
            Event::listen(UserWasRegistered::class, CreateUser::class);
            Event::listen(UserWasDeleted::class, UserDeleted::class);
            Event::listen(CreditIssued::class, CreditIssuedListener::class);
            Event::listen(OrderWasConfirmed::class, RecordPurchase::class);
            Event::listen(OrderWasCanceled::class, RecordCanceledOrder::class);
            Event::listen(CheckoutInitiated::class, RecordInitiatedCheckout::class);
            Event::listen(UserSubscribedToNewsletter::class, SubscribeUser::class);
            Event::listen(UserUnsubscribedFromNewsletter::class, UnsubscribeUser::class);
            Event::listen(OrderStatusWasUpdated::class, RecordFirstOrderReceived::class);
            Event::listen(CartCreated::class, RecordCartCreated::class);
            Event::listen(CartEmptied::class, RecordCartEmptied::class);

            Event::listen(RecurringOrderCreated::class, ListenForRecurringOrderCreated::class);
            Event::listen(SubscriptionWasSkipped::class, RecordSubscriptionWasSkipped::class);
            Event::listen(RecurringOrderCanceled::class, ListenForRecurringOrderCanceled::class);
            Event::listen(SubscriptionFrequencyWasUpdated::class, ListenForSubscriptionFrequencyWasUpdated::class);
        }
    }

    public function apiKeyIsValid(): bool
    {
        $api_token = $this->integration->setting('api_key');
        $account_id = $this->integration->setting('account_id');

        if ( ! $api_token || !preg_match('#^[\w-]+$#si', $api_token) || ! $account_id) {
            return false;
        }

        return true;
    }
}
