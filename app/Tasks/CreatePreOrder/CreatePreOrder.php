<?php

namespace App\Tasks\CreatePreOrder;

use App\Models\Order;
use App\Models\Product;
use App\Models\User;
use Exception;
use Illuminate\Support\Collection;

class CreatePreOrder
{
    /**
     * @param  User  $user
     * @param  Product  $product
     * @param  Collection<string, mixed>  $params
     * @return Order
     * @throws Exception
     */
    public function handle(User $user, Product $product, Collection $params): Order
    {
        $order = new Order;

        app(AddOrderMetadata::class)->handle($user, $order, $product, $params);

        return $order;
    }
}
