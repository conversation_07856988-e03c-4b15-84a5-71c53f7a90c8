<?php

namespace App\Presenters;

use App\Models\Proposal;
use Laracasts\Presenter\Presenter;

class ProposalPresenter extends Presenter
{
    public function proposal(): Proposal
    {
        return $this->entity;
    }

    public function starRating(): string
    {
        $ratings = [
            '1' => '&#9733;&#9734;&#9734;&#9734;&#9734;',
            '2' => '&#9733;&#9733;&#9734;&#9734;&#9734;',
            '3' => '&#9733;&#9733;&#9733;&#9734;&#9734;',
            '4' => '&#9733;&#9733;&#9733;&#9733;&#9734;',
            '5' => '&#9733;&#9733;&#9733;&#9733;&#9733;'
        ];

        return $ratings[$this->proposal()->rating] ?? '';
    }
}
