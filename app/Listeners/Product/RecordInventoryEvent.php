<?php

namespace App\Listeners\Product;

use App\Events\Product\InventoryEvent;
use App\Models\Product;
use Illuminate\Contracts\Queue\ShouldQueue;

class RecordInventoryEvent
{
    public function handle(InventoryEvent $event): void
    {
        Product::find($event->product_id)
            ?->events()
            ->create([
                'user_id' => $event->user_id,
                'event_id' => $event::class,
                'description' => $event->description(),
                'metadata' => $event->attributes,
            ]);
    }
}
