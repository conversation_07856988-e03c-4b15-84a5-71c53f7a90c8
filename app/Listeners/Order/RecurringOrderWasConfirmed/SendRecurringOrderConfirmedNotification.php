<?php

namespace App\Listeners\Order\RecurringOrderWasConfirmed;

use App\Events\Subscription\RecurringOrderWasConfirmed;
use App\Notifications\RecurringOrderConfirmed;

class SendRecurringOrderConfirmedNotification
{
    public function handle(RecurringOrderWasConfirmed $event): void
    {
        $sends_at = now();

        if ($this->sendsViaSms() && $this->isQuietHours()) {
            $sends_at = $sends_at->next('08:00');
        }

        $event->order->notify(
            (new RecurringOrderConfirmed)->delay($sends_at)
        );
    }

    private function sendsViaSms(): bool
    {
        return (bool) setting('recurring_orders_reorder_sms_enabled', false);
    }

    private function isQuietHours(): bool
    {
        $now = now();

        return $now->hour >= 22 || $now->hour < 8;
    }
}
