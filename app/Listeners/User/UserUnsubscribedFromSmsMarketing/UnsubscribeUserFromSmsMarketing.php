<?php

namespace App\Listeners\User\UserUnsubscribedFromSmsMarketing;

use App\Events\User\UserUnsubscribedFromSmsMarketing;
use App\Integrations\Drip\Drip;
use App\Models\Integration;
use Bugsnag\BugsnagLaravel\Facades\Bugsnag;
use Exception;

class UnsubscribeUserFromSmsMarketing
{
    public function handle(UserUnsubscribedFromSmsMarketing $event): void
    {
        if (is_null($smsSubscriptionIntegration = (new Integration)->smsSubscriptionIntegration())) {
            return;
        }

        try {
            app(Drip::class)
                ->configure(
                    $smsSubscriptionIntegration->setting('api_key', ''),
                    $smsSubscriptionIntegration->setting('account_id', '')
                )
                ->subscribe($event->user, true);
        } catch (Exception $exception) {
            // silently fail, send error to bugsnag
            Bugsnag::notifyException($exception);
        }
    }
}
