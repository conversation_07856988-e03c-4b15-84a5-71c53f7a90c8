<?php

namespace App\Billing\Gateway;

use Illuminate\Contracts\Support\Arrayable;

class PaymentMethod implements Arrayable
{
    public function __construct(
        public string $id,
        public string $customer_id,
        public ?string $customer_name,
        public string $exp_month,
        public string $exp_year,
        public ?string $brand,
        public ?string $last_four,
    ) {}

    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'customer_id' => $this->customer_id,
            'customer_name' => $this->customer_name,
            'exp_month' => $this->exp_month,
            'exp_year' => $this->exp_year,
            'brand' => $this->brand,
            'last_four' => $this->last_four,
        ];
    }
}
