<?php

namespace App\Actions\Cart;

use App\Cart\Coupon;
use App\Cart\Item;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Product;
use App\Models\Setting;

class ConfirmDatabaseCartWithCart
{
    public function handle(\App\Models\Cart $db_cart, \App\Cart\Cart $cart): Order
    {
        $order = app(ConfirmOrderWithCart::class)->handle(
            $this->createFromDatabaseCart($db_cart),
            $cart
        );

        $db_cart->delete();

        return $order;
    }

    private function createFromDatabaseCart(\App\Models\Cart $database_cart): Order
    {
        $delivery_method = $database_cart->cartLocation();
        $user = $database_cart->cartCustomer();

        $date_id = null;
        $deadline_date = null;
        $pickup_date = null;

        if ($order_window = $delivery_method->activeOrderWindow()) {
            $date_id = $order_window->dateId();
            $deadline_date = $order_window->deadlineDatetime();
            $pickup_date = $order_window->deliveryDatetime();
        }

        $orderAttributes = array_merge([
            'is_recurring' => $database_cart->isRecurring(),
            'type_id' => $delivery_method?->setting('sales_channel', 1) ?? 1,
            'payment_id' => $user?->setting('default_payment_method', 0) ?? 0,
            'payment_source_id' => $user->checkout_card_id ?? null,
            'schedule_id' => $user->pickup->schedule_id ?: 0,
            'pickup_id' => $delivery_method?->id,
            'date_id' => $date_id,
            'deadline_date' => $deadline_date,
            'pickup_date' => $pickup_date,
            'original_pickup_date' => $pickup_date,
            'customer_id' => $user->id,
            'customer_first_name' => $user->first_name,
            'customer_last_name' => $user->last_name,
            'customer_phone' => $user->phone,
            'customer_email' => $user->email,
            'billing_street' => $user->billing_street,
            'billing_street_2' => $user->billing_street_2,
            'billing_city' => $user->billing_city,
            'billing_state' => $user->billing_state,
            'billing_zip' => $user->billing_zip ?: '',
            'first_time_order' => $user->order_count == 0,
            'confirmed' => false,
            'packed' => false,
            'paid' => false,
            'status_id' => 1
        ], Order::defaultShippingAttributes($user));

        /** @var Order $order */
        $order = Order::create($orderAttributes);

        foreach ($database_cart->itemsInCart() as $item) {
            /** @var Item $item */
            if ($item->product->isGiftCard()) {
                $this->addGiftCards($order, $item->product, $item->quantity);
            } else {
                $this->addItem($order, $item->product, $item->quantity, 'standard');
            }
        }

        if ($database_cart->isRecurring()) {
            session(['subscription_frequency' => $database_cart->cartSubscription()?->frequency]);
        }

        if ($promo = $database_cart->cartSubscriptionProductIncentive()) {
            $this->addItem($order, $promo, 1, 'promo');
        }

        foreach ($database_cart->cartCoupons() as $cart_coupon) {
            /** @var Coupon $cart_coupon */
            $coupon = \App\Models\Coupon::firstWhere(['code' => $cart_coupon->code]);
            if (!is_null($coupon)) {
                try {
                    $order->applyCoupon($coupon);
                } catch (\Exception) { }
            }
        }

        return $order->fresh();
    }

    private function addGiftCards(Order $order, $product, $qty): void
    {
        foreach (range(1, $qty) as $index) {
            $this->addItem($order, $product, 1, 'standard');
        }
    }

    private function addItem(Order $order, Product $product, $qty, $type): void
    {
        if ($order->isRecurring()) {
            $addOnItems = collect(Setting::retrieveJson('recurring_orders_excluded_products'));
            if ($addOnItems->contains($product->id)) {
                $type = 'addon';
            }
        }

        $today = today();

        $item = new OrderItem([
            'order_id' => $order->id,
            'product_id' => $product->id,
            'user_id' => $order->customer_id,
            'title' => $product->title,
            'type' => $type,
            'unit_price' => $product->getUnitPrice(),
            'original_unit_price' => $product->getUnitPrice(),
            'qty' => $qty,
            'original_qty' => $qty,
            'stock_status' => 'full',
            'fulfilled_qty' => $qty,
            'weight' => $product->weight * $qty,
            'original_weight' => $product->weight * $qty,
            'store_price' => $product->getPrice(),
            'unit_of_issue' => $product->unit_of_issue,
            'taxable' => $product->taxable,
            'created_year' => $today->year,
            'created_month' => $today->month,
            'created_day' => $today->day,
        ]);

        $item->updateSubtotal();

        $item->save();

    }
}
