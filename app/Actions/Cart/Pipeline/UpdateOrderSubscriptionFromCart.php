<?php

namespace App\Actions\Cart\Pipeline;

use App\Actions\CreateRecurringOrderBlueprint;
use App\Cart\Cart;
use App\Models\Order;
use App\Models\Product;
use Closure;

class UpdateOrderSubscriptionFromCart
{
    public function handle(array $passable, Closure $next): Order
    {
        /**
         * @var Order $order
         * @var Cart $cart
         */
        list($order, $cart) = $passable;

        if (
            ! $order->cartIsEligibleForSubscription()
            || is_null($order->cartSubscription())
        ) {
            $order->is_recurring = null;
            $order->blueprint_id = null;

            return $next([$order, $cart]);
        }

        $subscription = $order->cartSubscription();

        $blueprint = app(CreateRecurringOrderBlueprint::class)
            ->execute($order, $subscription->frequency, $subscription->product_incentive_id);

        $order->is_recurring = true;
        $order->blueprint_id = $blueprint->id;

        if ( ! is_null($subscription->product_incentive_id)) {
            $promo_product = Product::find($subscription->product_incentive_id);

            // we need to sync promo item as it may already exist in some cases
            $order->items()
                ->where([
                    'type' => 'promo',
                    'order_items.product_id' => $promo_product->id
                ])
                ->delete();

            $order->addPromoItem($promo_product);
        }

        return $next([$order, $cart]);
    }
}
