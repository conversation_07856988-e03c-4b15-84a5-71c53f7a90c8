<?php

namespace App\Actions;

use Carbon\Carbon;
use Carbon\CarbonInterval;
use Exception;

class CreateRepeatingDates
{
    public function execute(
        Carbon $startDate,
        Carbon $endDate,
        Carbon $deliveryDate,
        Carbon $stopDate = null,
        int $scheduleId,
        int $increments = 7,
        $format = 'Y-m-d',
        $hasSharedStartDate = false
    ): array {
        if ($endDate < $startDate) {
            throw new Exception('The start date must come before the end date.'); //ToDo: Revise the names
        }

        if ($deliveryDate < $endDate) {
            throw new Exception('The delivery date must come after the end date.');
        }

        if ($increments < 7) {
            $increments = 7;
        }

        if (is_null($stopDate)) {
            $stopDate = $deliveryDate->copy()->addMonths(6);
        }

        $dates = [];

        $interval = CarbonInterval::days($increments);

        $end_period = $interval->copy()->toPeriod($endDate, $stopDate);
        $pickup_period = $interval->copy()->toPeriod($deliveryDate, $stopDate);

        foreach ($pickup_period as $pickup_date) {
            $dates[] = [
                'pickup_date' => $pickup_date->format($format),
                'order_start_date' => $startDate->format($format),
                'order_end_date' => $end_period->current()->format($format),
                'schedule_id' => $scheduleId
            ];

            if ( ! $hasSharedStartDate) {
                $startDate = $end_period->current()->copy();
            }

            $end_period->next();
        }

        return $dates;
    }
}
