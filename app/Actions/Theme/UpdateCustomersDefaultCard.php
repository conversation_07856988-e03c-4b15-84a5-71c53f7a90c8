<?php

namespace App\Actions\Theme;

use App\Actions\Billing\SetDefaultCard;
use App\Models\Card;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;

class UpdateCustomersDefaultCard
{
    public function execute(Request $request)
    {
        if (auth()->guest()) {
            return back();
        }

        $validated = $request->validate([
            'card_id' => [
                'required', 'integer', Rule::exists(Card::class, 'id')->where('user_id', auth()->id())
            ]
        ]);

        app(SetDefaultCard::class)->handle(Card::find($validated['card_id']));

        return back();
    }
}
