<?php

namespace App\Actions\Subscription;

use App\Models\RecurringOrder;
use Carbon\Carbon;

class SyncSubscriptionDatetimes
{
    public function handle(
        RecurringOrder $subscription,
        Carbon $new_delivery_date
    ): RecurringOrder
    {
        $order_window = $subscription
            ->pickupSchedule()
            ->closestAvailableOrderWindow($new_delivery_date);

        $subscription->ready_at = $order_window->originalDate()->pickup_date;
        $subscription->generate_at = $order_window->generatesAtDatetime();

        $subscription->save();

        return $subscription;
    }
}
