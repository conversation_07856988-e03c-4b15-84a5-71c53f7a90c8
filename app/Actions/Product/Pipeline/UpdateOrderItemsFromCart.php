<?php

namespace App\Actions\Product\Pipeline;

use App\Models\Order;
use App\Models\Product;
use Closure;

class UpdateOrderItemsFromCart
{
    public function handle(array $passable, Closure $next): Order
    {
        /**
         * @var Order $order
         * @var array $params
         */
        list($order, $params) = $passable;

        $items = $params['items'];

        $price_group_id = 0;

        if ($order->customer->pricing_group_id) {
            $price_group_id = $order->customer->pricing_group_id;
        } elseif ($order->pickup->pricing_group_id) {
            $price_group_id = $order->pickup->pricing_group_id;
        }

        foreach ($items as $item) {
            $order->addItem(
                Product::query()
                    ->with(['price' => fn($q) => $q->where('group_id', $price_group_id)])
                    ->find($item['product_id']),
                $item['quantity']
            );
        }


        return $next([$order, $params]);
    }
}