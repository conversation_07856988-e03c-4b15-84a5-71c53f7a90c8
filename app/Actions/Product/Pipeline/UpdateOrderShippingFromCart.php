<?php

namespace App\Actions\Product\Pipeline;

use App\Models\Order;
use Closure;

class UpdateOrderShippingFromCart
{
    public function handle(array $passable, Closure $next): Order
    {
        /**
         * @var Order $order
         * @var array $params
         */
        list($order, $params) = $passable;

        $shipping = $params['shipping'];

        $order->shipping_street = $shipping['street'] ?? '';
        $order->shipping_street_2 = $shipping['street_2'] ?? '';
        $order->shipping_city = $shipping['city'] ?? '';
        $order->shipping_state = $shipping['state'] ?? '';
        $order->shipping_zip = $shipping['zip'] ?? '';
        $order->shipping_country = $shipping['country'] ?? '';

        if ($shipping['save_for_later'] && ! empty($shipping['street'])) {
            $order->customer->street = $shipping['street'];
            $order->customer->street_2 = $shipping['street_2'];
            $order->customer->city = $shipping['city'];
            $order->customer->state = $shipping['state'];
            $order->customer->zip = $shipping['zip'];
            $order->customer->country = $shipping['country'];

            if ($order->customer->isDirty()) {
                $order->customer->save();
            }
        }

        return $next([$order, $params]);
    }
}