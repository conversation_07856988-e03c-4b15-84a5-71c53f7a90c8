<?php

namespace App\Actions\Order;

use App\Events\Order\ItemWasAddedToOrder;
use App\Exceptions\BackOrderException;
use App\Exceptions\ExclusivityException;
use App\Models\Event;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Product;
use App\Services\SubscriptionSettingsService;
use Exception;

class AddItem
{
    /**
     * @throws BackOrderException
     * @throws ExclusivityException
     * @throws Exception
     */
    public function handle(Order $order, Product $product, $quantity, string $type = 'standard', bool $force = false): OrderItem
    {
        if ( ! $force) {
            if ($order->is_paid) {
                throw new BackOrderException('A product cannot added because this order has already been paid.');
            }

            if ($order->pickup?->excludesProduct($product)) {
                throw new ExclusivityException($order->pickup, [$product]);
            }

            if ($product->hasLimitPerCustomer() && $quantity > $product->limitPerCustomer()) {
                throw new BackOrderException("There is a limit of {$product->limitPerCustomer()} per customer for this product.");
            }

            if ($order->isConfirmed() && $order->subtotal < $product->orderMinimum()) {
                throw new Exception('The subtotal must be at least $' . money($product->orderMinimum()) . ' to purchase ' . $product->title);
            }

            if ( ! $product->canAddAmountToCart($quantity, $order)) {
                throw new BackOrderException($product->getOutOfStockMessage());
            }
        }

        if ($product->isGiftCard()) {
            return $this->addGiftCards($order, $product, $quantity, $type);
        }

        $existing_item = $this->find($order, $product->id, $type);

        if ($existing_item && $type !== 'promo') {
            return $order->updateItemQuantity($existing_item, $quantity + $existing_item->qty, $force);
        }

        return $this->addItem($order, $product, $quantity, $type);
    }

    private function addGiftCards(Order $order, $product, $quantity, $type): ?OrderItem
    {
        $item = null;

        foreach (range(1, $quantity) as $index) {
            $item = $this->addItem($order, $product, 1, $type);
        }

        return $item;
    }

    private function addItem(Order $order, Product $product, $quantity, $type): OrderItem
    {
        $subscription_settings = app(SubscriptionSettingsService::class);

        if (
            ($order->isRecurring() || $order->isFromBlueprint())
            && $subscription_settings->excludedProductIds()->contains($product->id)
        ) {
            $type = 'addon';
        }

        $today = today();

        $item = new OrderItem([
            'order_id' => $order->id,
            'product_id' => $product->id,
            'user_id' => $order->customer_id,
            'title' => $product->title,
            'type' => $type,
            'unit_price' => $product->getUnitPrice($quantity),
            'original_unit_price' => $product->getUnitPrice($quantity),
            'qty' => $quantity,
            'original_qty' => $quantity,
            'stock_status' => 'full',
            'fulfilled_qty' => $quantity,
            'weight' => $product->weight * $quantity,
            'original_weight' => $product->weight * $quantity,
            'store_price' => $product->getPrice($quantity),
            'unit_of_issue' => $product->unit_of_issue,
            'taxable' => $product->taxable,
            'tax' => 0,
            'discount' => 0,
            'created_year' => $today->year,
            'created_month' => $today->month,
            'created_day' => $today->day,
        ]);

        $item->updateSubtotal();

        $item->save();

        // Only manipulate inventory if order has already been confirmed
        if ($order->isConfirmed()) {
            $item->consumeProductInventory();
            $this->recordEvent($item, $quantity);
        }

        ItemWasAddedToOrder::dispatch($item);

        return $item;
    }

    /**
     * Record an event of the item being added.
     */
    private function recordEvent(OrderItem $item, $quantity): void
    {
        Event::create([
            'model_type' => Order::class,
            'model_id' => $item->order_id,
            'description' => "({$quantity}) {$item->title} was added to order",
            'event_id' => 'order_item_added',
            'user_id' => auth()->id() ?? $item->order->customer_id, // So we can know who did this...
            'created_at' => now(),
            'metadata' => json_encode([
                'item_id' => $item->id,
                'qty' => $quantity
            ])
        ]);
    }

    private function find(Order $order, int $product_id, string $type): ?OrderItem
    {
        return $order->items
            ->first(fn($item) => $item->product_id === $product_id && $item->type === $type);
    }
}
