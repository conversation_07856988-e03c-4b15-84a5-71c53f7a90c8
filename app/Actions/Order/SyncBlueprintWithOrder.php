<?php

namespace App\Actions\Order;

use App\Models\Order;
use App\Models\OrderItem;
use App\Models\RecurringOrder;
use App\Models\RecurringOrderItem;

class SyncBlueprintWithOrder
{
    public function handle(RecurringOrder $blueprint, Order $order): RecurringOrder
    {
        // leaves promotional item
        RecurringOrderItem::where(['order_id' => $blueprint->id, 'type' => 'recurring'])->delete();

        $order->items
            ->filter(fn(OrderItem $item) => $item->type === 'standard')
            ->each(function (OrderItem $item) use ($blueprint) {
                RecurringOrderItem::create([
                    'order_id' => $blueprint->id,
                    'customer_id' => $blueprint->customer_id,
                    'product_id' => $item->product_id,
                    'qty' => $item->qty,
                    'type' => 'recurring'
                ]);
            });

        return $blueprint->refresh();
    }
}