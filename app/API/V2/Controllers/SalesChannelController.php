<?php

namespace App\API\V2\Controllers;

use App\Http\Controllers\Controller;
use App\Support\Enums\Channel;
use Illuminate\Http\JsonResponse;

class SalesChannelController extends Controller
{
    public function index(): JsonResponse
    {
        return response()->json([
            'data' => Channel::all()
                ->map(fn ($item, $key) => ['id' => $key, 'title' => $item])
                ->values()
        ]);
    }
}
