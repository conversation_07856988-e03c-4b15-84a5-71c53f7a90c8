<?php

namespace App\API\V2\Controllers;

use App\Http\Controllers\Controller;
use App\Models\Webhook;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class WebhookController extends Controller
{
    public function store(Request $request): JsonResponse
    {
        $webhook = Webhook::create(
            $request->validate([
                'target_url' => ['required', 'url'],
                'topic' => ['required'],
                'settings' => ['array'],
                'settings.filters' => ['array']
            ])
        );

        return response()->json(['id' => $webhook->id]);
    }

    public function destroy(Webhook $webhook): Response
    {
        $webhook->delete();

        return response()->noContent();
    }
}