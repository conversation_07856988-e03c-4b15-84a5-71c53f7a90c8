<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasOne;

/**
 * App\Models\BundleProduct
 *
 * @property int $id
 * @property int $bundle_id
 * @property int $product_id
 * @property int $qty
 * @property int $sort
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Product|null $product
 * @method static \Illuminate\Database\Eloquent\Builder|BundleProduct newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|BundleProduct newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|BundleProduct query()
 * @method static \Illuminate\Database\Eloquent\Builder|BundleProduct whereBundleId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BundleProduct whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BundleProduct whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BundleProduct whereProductId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BundleProduct whereQty($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BundleProduct whereSort($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BundleProduct whereUpdatedAt($value)
 * @method static \Database\Factories\BundleProductFactory factory($count = null, $state = [])
 * @mixin \Eloquent
 */
class BundleProduct extends Model
{
    use HasFactory;

    protected $table = 'bundle_product';

    public function product(): HasOne
    {
        return $this->hasOne(Product::class, 'id', 'product_id');
    }
}
