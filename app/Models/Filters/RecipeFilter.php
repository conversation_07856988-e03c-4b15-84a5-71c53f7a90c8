<?php 

namespace App\Models\Filters;

use EloquentFilter\ModelFilter;
use Illuminate\Database\Eloquent\Builder;

class RecipeFilter extends ModelFilter
{
    /**
    * Related Models that have ModelFilters as well as the method on the ModelFilter
    * As [relationMethod => [input_key1, input_key2]].
    *
    * @var array
    */
    public $relations = [];

    public function sort($direction): void
    {
        $this->when( ! empty($this->input('orderBy')), function ($query) use ($direction) {
            return $query->orderBy(
                column: $this->input('orderBy'),
                direction: in_array($direction, ['asc', 'desc']) ? $direction : 'asc'
            );
        })
            ->orderBy('created_at', 'desc');
    }

    public function recipes(string $query): void
    {
        $this->where('title', 'LIKE', '%' . $query . '%');
    }

    public function user($id): void
    {
        $this->where('user_id', $id);
    }

    public function publishedDate(string $dateRange): void
    {
        $dates = explode(' - ', $dateRange);
        $start = $dates[0] . ' 00:00:00';
        $end = $dates[1] . ' 23:59:59';

        $this->whereBetween('published_at', [$start, $end]);
    }
}
