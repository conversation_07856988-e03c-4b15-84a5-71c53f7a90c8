<?php 

namespace App\Models\Filters;

use EloquentFilter\ModelFilter;

class ProposalFilter extends ModelFilter
{
    /**
    * Related Models that have ModelFilters as well as the method on the ModelFilter
    * As [relationMethod => [input_key1, input_key2]].
    *
    * @var array
    */
    public $relations = [];

    public function sort($direction): void
    {
        $this->when( ! empty($this->input('orderBy')), function ($query) use ($direction) {
            return $query->orderBy(
                column: $this->input('orderBy'),
                direction: in_array($direction, ['asc', 'desc']) ? $direction : 'asc'
            );
        })
            ->orderBy('created_at', 'desc');
    }

    public function proposals(string $query): void
    {
        $this
            ->whereRaw('CONCAT(first_name, " ", last_name) LIKE  \'%' . addslashes($query) . '%\'')
            ->orWhere('email', addslashes($query))
            ->orWhere('phone', addslashes($query))
            ->orWhere('city', addslashes($query))
            ->orWhere('zip', addslashes($query));
    }

    public function proposalStatus(string $status): void
    {
        $this->where('status', $status);
    }

    public function rating($rating): void
    {
        $this->where('rating', $rating);
    }

    public function state(string $state): void
    {
        $this->where('state', $state);
    }
}
