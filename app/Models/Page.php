<?php

namespace App\Models;

use App\Traits\SeoTrait;
use C<PERSON>brock\EloquentSluggable\Sluggable;
use EloquentFilter\Filterable;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Cache;
use Spatie\SchemalessAttributes\Casts\SchemalessAttributes;

/**
 * App\Models\Page
 *
 * @property int $id
 * @property string $title
 * @property string $subtitle
 * @property string $slug
 * @property string $page_title
 * @property string $description
 * @property string $body
 * @property string $path
 * @property string|null $layout
 * @property \Spatie\SchemalessAttributes\SchemalessAttributes $settings
 * @property int $visible
 * @property int $needs_published
 * @property int $seo_visibility
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property mixed $custom_fields
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Models\Widget[] $widgets
 * @property-read int|null $widgets_count
 * @method static \Illuminate\Database\Eloquent\Builder|Page custom(string $slug, bool $is_public)
 * @method static \Illuminate\Database\Eloquent\Builder|Page findSimilarSlugs(string $attribute, array $config, string $slug)
 * @method static \Illuminate\Database\Eloquent\Builder|Page home()
 * @method static \Illuminate\Database\Eloquent\Builder|Page newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Page newQuery()
 * @method static \Illuminate\Database\Query\Builder|Page onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|Page query()
 * @method static \Illuminate\Database\Eloquent\Builder|Page whereBody($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Page whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Page whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Page whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Page whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Page whereLayout($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Page whereNeedsPublished($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Page wherePageTitle($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Page wherePath($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Page whereSeoVisibility($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Page whereSettings($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Page whereSlug($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Page whereSubtitle($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Page whereTitle($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Page whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Page whereVisible($value)
 * @method static \Illuminate\Database\Query\Builder|Page withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|Page withUniqueSlugConstraints(\Illuminate\Database\Eloquent\Model $model, string $attribute, array $config, string $slug)
 * @method static \Illuminate\Database\Query\Builder|Page withoutTrashed()
 * @method static \Database\Factories\PageFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder|Page filter(array $input = [], $filter = null)
 * @method static \Illuminate\Database\Eloquent\Builder|Page paginateFilter($perPage = null, $columns = [], $pageName = 'page', $page = null)
 * @method static \Illuminate\Database\Eloquent\Builder|Page simplePaginateFilter($perPage = null, $columns = [], $pageName = 'page', $page = null)
 * @method static \Illuminate\Database\Eloquent\Builder|Page whereBeginsWith($column, $value, $boolean = 'and')
 * @method static \Illuminate\Database\Eloquent\Builder|Page whereEndsWith($column, $value, $boolean = 'and')
 * @method static \Illuminate\Database\Eloquent\Builder|Page whereLike($column, $value, $boolean = 'and')
 * @mixin \Eloquent
 */
class Page extends Model
{
    use HasFactory, Sluggable, Filterable, SoftDeletes, SeoTrait;

    public $casts = [
        'settings' => SchemalessAttributes::class,
    ];

    protected $guarded = ['id'];

    public function scopeWithSettings(): Builder
    {
        return $this->settings->modelScope();
    }

    /**
     * Return the sluggable configuration array for this model.
     */
    public function sluggable(): array
    {
        return [
            'slug' => [
                'source' => 'title'
            ]
        ];
    }

    public function insertWidget(Widget $widget): Page
    {
        $widgets = $this->widgets()
            ->orderBy('sort')
            ->select(['sort', 'id'])
            ->get()
            ->toArray();

        array_splice($widgets, $widget->sort, 0, [
            ['sort' => $widget->sort, 'id' => $widget->id]
        ]);

        foreach ($widgets as $sort => $widget) {
            Widget::where('id', $widget['id'])->update(['sort' => $sort]);
        }

        return $this;
    }

    public function widgets(): HasMany
    {
        return $this->hasMany(Widget::class, 'page_id', 'id')
            ->whereNull('container_id')
            ->orderBy('sort');
    }

    public function scopeCustom($query, $slug, bool $is_public)
    {
        return $query->select(['id', 'title', 'page_title', 'description', 'body', 'layout', 'settings', 'updated_at', 'seo_visibility'])
            ->where('slug', $slug)
            ->when($is_public, fn ($q) => $q->where('visible', true));
    }

    public function isHomepage(): bool
    {
        return $this->slug === 'homepage';
    }

    public function renderHTML(bool $authenticated): string
    {
        return resolve('ThemeBuilder')->renderPageHTML($this, $authenticated);
    }

    public function getStyles(): string
    {
        return resolve('ThemeBuilder')->renderPageStyles($this);
    }

    public function flushCache(): void
    {
        Cache::forget($this->cacheKey(is_public: true));
        Cache::forget($this->htmlCacheKey(is_public: true));
        Cache::forget($this->cacheKey(is_public: false));
        Cache::forget($this->htmlCacheKey(is_public: false));
    }

    public function cacheKey(bool $is_public): string
    {
        return static::cacheKeyForSlug(slug: $this->slug, is_public: $is_public);
    }

    public static function cacheKeyForSlug(string $slug, bool $is_public): string
    {
        $suffix = $is_public ? 'public' : 'private';

        return "pages.{$slug}.{$suffix}";
    }

    public function htmlCacheKey(bool $is_public): string
    {
        return static::htmlCacheKeyForSlug(slug: $this->slug, is_public: $is_public);
    }

    public static function htmlCacheKeyForSlug(string $slug, bool $is_public): string
    {
        $suffix = $is_public ? 'public' : 'private';
        return "pages.{$slug}.html.{$suffix}";
    }
}
