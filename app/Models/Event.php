<?php

namespace App\Models;

use App\Events\Product\InventoryDecreasedToThresholdOrBelow;
use App\Events\Product\InventoryDecreasedToZeroOrBelow;
use App\Events\Product\InventoryIncreasedAboveThreshold;
use App\Events\Product\InventoryIncreasedAboveZero;
use Carbon\Carbon;
use EloquentFilter\Filterable;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Spatie\SchemalessAttributes\Casts\SchemalessAttributes;

/**
 * App\Models\Event
 *
 * @property int $id
 * @property int $user_id
 * @property int|null $model_id
 * @property string|null $model_type
 * @property string|null $event_id
 * @property string|null $description
 * @property \Spatie\SchemalessAttributes\SchemalessAttributes $metadata
 * @property \Illuminate\Support\Carbon $created_at
 * @property-read \App\Models\User $user
 * @method static \Illuminate\Database\Eloquent\Builder|Event newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Event newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Event query()
 * @method static \Illuminate\Database\Eloquent\Builder|Event whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Event whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Event whereEventId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Event whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Event whereMetadata($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Event whereModelId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Event whereModelType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Event whereUserId($value)
 * @method static \Database\Factories\EventFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder|Event stockEvents()
 * @method static \Illuminate\Database\Eloquent\Builder|Event creditAppliedEvents()
 *
 * @mixin \Eloquent
 */
class Event extends Model
{
    use HasFactory;
    use Filterable;

    public $timestamps = false;

    protected $guarded = [];

    protected $cachedMetadata = null;

    public function update(array $attributes = [], array $options = []): bool
    {
        return false;
    }

    public function delete(): bool
    {
        return false;
    }

    public function forceDelete(): bool
    {
        return false;
    }

    public function insert(): bool
    {
        return false;
    }

    public function touch($attribute = null): bool
    {
        return false;
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    public function eventable(): MorphTo
    {
        return $this->morphTo('model');
    }

    /**
     * @param Builder<Event> $query
     * @return Builder<Event>
     */
    public function scopeStockEvents(Builder $query): Builder
    {
        return $query->whereIn('event_id', [
            InventoryDecreasedToZeroOrBelow::class,
            InventoryIncreasedAboveZero::class,
            InventoryDecreasedToThresholdOrBelow::class,
            InventoryIncreasedAboveThreshold::class
        ]);
    }

    public function scopeCreditAppliedEvents(Builder $query): Builder
    {
        return $query->where('event_id', 'credit_applied')
            ->where('model_type', User::class);
    }

    /**
     *
     * @param  Builder<Event>  $query
     * @param  Carbon|null  $starts_at
     * @param  Carbon|null  $ends_at
     * @return Builder<Event>
     */
    public function scopeEventDateBetween(Builder $query, ?Carbon $starts_at, ?Carbon $ends_at): Builder
    {
        if (is_null($starts_at)) {
            $starts_at = today()->subDays(90)->startOfDay();
        }

        if (is_null($ends_at)) {
            $ends_at = today()->endOfDay();
        }

        return $query->whereBetween('created_at', [$starts_at, $ends_at]);
    }

    protected function casts(): array
    {
        return [
            'created_at' => 'datetime',
            'metadata' => SchemalessAttributes::class,
        ];
    }
}
