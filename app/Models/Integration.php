<?php

namespace App\Models;

use App\Traits\SettingsTrait;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Arr;

/**
 * App\Models\Integration
 *
 * @property int $id
 * @property string $name
 * @property string|null $category
 * @property bool $enabled
 * @property int $autoload
 * @property mixed $settings
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property mixed $custom_fields
 * @method static Builder|Integration enabled()
 * @method static Builder|Integration newModelQuery()
 * @method static Builder|Integration newQuery()
 * @method static Builder|Integration query()
 * @method static Builder|Integration whereAutoload($value)
 * @method static Builder|Integration whereCategory($value)
 * @method static Builder|Integration whereCreatedAt($value)
 * @method static Builder|Integration whereEnabled($value)
 * @method static Builder|Integration whereId($value)
 * @method static Builder|Integration whereName($value)
 * @method static Builder|Integration whereSettings($value)
 * @method static Builder|Integration whereUpdatedAt($value)
 * @method static \Database\Factories\IntegrationFactory factory($count = null, $state = [])
 * @method static Builder|Integration service(string $value)
 * @mixin \Eloquent
 */
class Integration extends Model
{
    use HasFactory;

    use SettingsTrait;

    protected $guarded = [];

    public static function initializeAll()
    {
        foreach (self::where('enabled', true)->where('autoload', true)->get() as $integration) {
            self::bootIntegration($integration);
        }
    }

    private static function bootIntegration(Integration $integration)
    {
        if ($integration->enabled) {
            $class = config('integrations.' . $integration->name)['setup_class'] ?? null;
            if ($class !== null) {
                (new $class($integration))->boot();
            }
        }
    }

    public static function initialize($integrationName)
    {
        if ($integration = self::where('name', $integrationName)->where('enabled', true)->where('autoload', true)->first()) {
            self::bootIntegration($integration);
        }
    }

    public function title()
    {
        return config('integrations.' . $this->name . '.title');
    }

    public function logo()
    {
        return url(config('integrations.' . $this->name . '.logo'));
    }

    public function scopeEnabled(Builder $query)
    {
        $query->where('enabled', true);
    }

    public function scopeService(Builder $query, string $value)
    {
        $query->where('name', $value);
    }

    public function smsOptInSettings($opt_in_location): array
    {
        $smsSubscriptionIntegration = $this->smsSubscriptionIntegration();

        $opt_in_locations = Arr::get(
            json_decode($smsSubscriptionIntegration?->getRawOriginal('settings'), true),
            'opt_in_locations',
            $default = []
        );

        $checkout = collect($opt_in_locations)
            ->first(fn($location) => $location['name'] === $opt_in_location && $location['enabled']);

        return collect(Arr::get($checkout, 'settings', []))
            ->flatMap(fn($setting) => [$setting['name'] => $setting['value']])
            ->toArray();
    }

    public function smsSubscriptionIntegration(): ?Integration
    {
        return $this->where(['name' => 'drip', 'enabled' => true])->first();
    }

    public function hasSubscriptionEnabled($subscription): bool
    {
        return ! is_null(collect($this->setting('subscriptions'))
            ->first(function ($subscription_setting) use ($subscription) {
                return $subscription_setting->name === $subscription
                    && $subscription_setting->enabled;
            }));
    }

    public function isEnabled(): bool
    {
        return match($this->name) {
            'drip' => $this->enabled && $this->setting('api_key'),
            default => $this->enabled
        };
    }

    protected function casts(): array
    {
        return [
            'enabled' => 'boolean',
        ];
    }
}
