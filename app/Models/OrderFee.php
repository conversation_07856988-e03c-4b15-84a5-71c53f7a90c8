<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * App\Models\OrderFee
 *
 * @property int $id
 * @property int $order_id
 * @property int $user_id
 * @property string $title
 * @property int $amount
 * @property int $qty
 * @property string $note
 * @property int $subtotal
 * @property int $taxable
 * @property int $tax
 * @property int $discount
 * @property string $discount_type
 * @property int $apply_limit
 * @property int $threshold
 * @property int $cap
 * @property string $type
 * @property int $created_year
 * @property int $created_month
 * @property int $created_day
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read string $amount_formatted
 * @property-read string $subtotal_formatted
 * @property-read string $type_formatted
 * @property-read \App\Models\Order $order
 * @method static \Illuminate\Database\Eloquent\Builder|OrderFee newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|OrderFee newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|OrderFee query()
 * @method static \Illuminate\Database\Eloquent\Builder|OrderFee whereAmount($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrderFee whereApplyLimit($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrderFee whereCap($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrderFee whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrderFee whereCreatedDay($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrderFee whereCreatedMonth($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrderFee whereCreatedYear($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrderFee whereDiscount($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrderFee whereDiscountType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrderFee whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrderFee whereNote($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrderFee whereOrderId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrderFee whereQty($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrderFee whereSubtotal($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrderFee whereTax($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrderFee whereTaxable($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrderFee whereThreshold($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrderFee whereTitle($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrderFee whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrderFee whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrderFee whereUserId($value)
 * @method static \Database\Factories\OrderFeeFactory factory($count = null, $state = [])
 * @mixin \Eloquent
 */
class OrderFee extends Model
{
    use HasFactory;

    protected $guarded = [];

    protected $appends = ['subtotal_formatted', 'amount_formatted', 'type_formatted'];

    /**
     * Return the order the fee belongs to.
     */
    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class);
    }

    public function getSubtotalFormattedAttribute(): string
    {
        return number_format($this->subtotal / 100, 2);
    }

    public function getAmountFormattedAttribute(): string
    {
        return number_format($this->amount / 100, 2);
    }

    public function getTypeFormattedAttribute(): string
    {
        return match ($this->type) {
            'weight' => '/' . __("messages.uom." . setting('weight_uom', 'pounds')) . '.',
            default => '',
        };
    }
}
