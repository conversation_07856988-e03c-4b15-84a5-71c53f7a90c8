<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use App\OrderWindow;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * App\Models\Date
 *
 * @property int $id
 * @property int $schedule_id
 * @property int|null $user_id
 * @property \Illuminate\Support\Carbon $order_start_date
 * @property \Illuminate\Support\Carbon $order_end_date
 * @property \Illuminate\Support\Carbon $pickup_date
 * @property string $notes
 * @property int $active
 * @property string|null $scheduled_at
 * @property string|null $secondary_scheduled_at
 * @property int $reminder_sent
 * @property int $secondary_reminder_sent
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read string $delivery_date
 * @property-read \App\Models\Schedule $schedule
 * @method static Builder|Date newModelQuery()
 * @method static Builder|Date newQuery()
 * @method static Builder|Date open(int $scheduleId)
 * @method static Builder|Date query()
 * @method static Builder|Date whereActive($value)
 * @method static Builder|Date whereCreatedAt($value)
 * @method static Builder|Date whereId($value)
 * @method static Builder|Date whereNotes($value)
 * @method static Builder|Date whereOrderEndDate($value)
 * @method static Builder|Date whereOrderStartDate($value)
 * @method static Builder|Date wherePickupDate($value)
 * @method static Builder|Date whereReminderSent($value)
 * @method static Builder|Date whereScheduleId($value)
 * @method static Builder|Date whereScheduledAt($value)
 * @method static Builder|Date whereSecondaryReminderSent($value)
 * @method static Builder|Date whereSecondaryScheduledAt($value)
 * @method static Builder|Date whereUpdatedAt($value)
 * @method static Builder|Date whereUserId($value)
 * @method static \Database\Factories\DateFactory factory($count = null, $state = [])
 * @mixin \Eloquent
 */
class Date extends Model
{
    use HasFactory;

    protected $guarded = [];

    protected $appends = ['delivery_date'];

    protected function casts(): array
    {
        return [
            'order_start_date' => 'datetime',
            'order_end_date' => 'datetime',
            'pickup_date' => 'datetime',
        ];
    }

    public function scopeOpen(Builder $q, int $scheduleId)
    {
        $today = today();

        return $q->select(['id', 'order_end_date', 'order_start_date', 'pickup_date', 'active'])
            ->where('schedule_id', $scheduleId)
            ->where('order_start_date', '<=', $today)
            ->where('order_end_date', '>=', $today)
            ->where('active', true);
    }

    public function deadlineHasPassed()
    {
        return $this->order_end_date < today();
    }

    public function schedule(): BelongsTo
    {
        return $this->belongsTo(Schedule::class);
    }

    public function getDeliveryDateAttribute(): string
    {
        return $this->pickup_date->format('D, M jS');
    }

    public function toOrderWindows(): OrderWindow
    {
        return new OrderWindow($this);
    }
}
