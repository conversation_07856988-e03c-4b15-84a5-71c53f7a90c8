<?php

namespace App\Models;

use App\Support\FilenameGenerator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Support\Str;
use Image;
use Intervention\Image\Exception\NotSupportedException;
use Storage;
use Symfony\Component\HttpFoundation\File\UploadedFile;

/**
 * App\Models\Media
 *
 * @property int $id
 * @property string $title
 * @property string $path
 * @property string $thumbnail_path
 * @property int $height
 * @property int $width
 * @property int $size
 * @property string $layout
 * @property string $caption
 * @property string $type
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read string $created_at_formatted
 * @property-read string $file_size_formatted
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Models\Tag[] $tags
 * @property-read int|null $tags_count
 * @method static Builder|Media documents()
 * @method static Builder|Media images()
 * @method static Builder|Media newModelQuery()
 * @method static Builder|Media newQuery()
 * @method static Builder|Media query()
 * @method static Builder|Media video()
 * @method static Builder|Media whereCaption($value)
 * @method static Builder|Media whereCreatedAt($value)
 * @method static Builder|Media whereHeight($value)
 * @method static Builder|Media whereId($value)
 * @method static Builder|Media whereLayout($value)
 * @method static Builder|Media wherePath($value)
 * @method static Builder|Media whereSize($value)
 * @method static Builder|Media whereThumbnailPath($value)
 * @method static Builder|Media whereTitle($value)
 * @method static Builder|Media whereType($value)
 * @method static Builder|Media whereUpdatedAt($value)
 * @method static Builder|Media whereWidth($value)
 * @method static \Database\Factories\MediaFactory factory($count = null, $state = [])
 * @mixin \Eloquent
 */
class Media extends Model
{
    use HasFactory;

    protected $guarded = [];

    protected $appends = ['created_at_formatted', 'file_size_formatted'];

    public static function upload(UploadedFile $file, array $tags = []): Media
    {
        return match($file->getClientMimeType()) {
            'image/jpeg', 'image/pjpeg', 'image/png', 'image/webp' => (new static)->addPhoto($file, $tags),
            'application/pdf' => (new static)->addDocument($file, $tags),
            default => throw new NotSupportedException()
        };
    }

    private function addPhoto(UploadedFile $file, array $tags = []): Media
    {
        $extension = $file->getClientOriginalExtension();
        $image = Image::make($file)->orientate();

        $image_height = $image->height();
        $image_width = $image->width();
        $image_filesize = $image->filesize();

        $paths = $this->getUploadPaths($this->generateFilename($file));

        $full_size = (string) $image->encode($extension);
        Storage::disk('s3')->put($paths['upload_path'], $full_size, 'public');

        $thumbnail = (string) $image->resize(355, null, function ($constraint) {
            $constraint->aspectRatio();
            $constraint->upsize();
        })->encode($extension, 100);

        Storage::disk('s3')->put($paths['thumbnail_upload_path'], $thumbnail, 'public');

        $photo = static::create([
            'title' => str_replace(' ', '-', $file->getClientOriginalName()),
            'path' => $paths['full_path'],
            'thumbnail_path' => $paths['thumbnail_path'],
            'height' => $image_height,
            'width' => $image_width,
            'size' => $image_filesize,
            'layout' => $image_width >= $image_height ? 'landscape' : 'portrait',
            'caption' => '',
            'type' => 'image'
        ]);

        if ($tags) {
            return $photo->assignTags($tags);
        }

        return $photo;
    }

    private function getUploadPaths(string $name, string $folder = 'images'): array
    {
        return [
            'full_path' => self::s3UploadRootPath() . "/{$folder}/{$name}",
            'thumbnail_path' => self::s3UploadRootPath() . "/{$folder}/th_{$name}",
            'upload_path' => config('filesystems.file_upload_prefix') . "/{$folder}/{$name}",
            'thumbnail_upload_path' => config('filesystems.file_upload_prefix') . "/{$folder}/th_{$name}",
        ];
    }

    public static function s3UploadRootPath(): string
    {
        return 'https://s3.amazonaws.com/' . config('filesystems.disks.s3.bucket') . '/' . config('filesystems.file_upload_prefix');
    }

    private function generateFilename(UploadedFile $file): string
    {
        return app(FilenameGenerator::class)->generate($file);
    }

    public function assignTags(array $tags): Media
    {
        foreach ($tags as $title) {
            $tag = Tag::firstOrCreate([
                'title' => $title,
                'type' => Tag::type('media')
            ]);

            $this->tags()->attach($tag->id);
        }

        return $this;
    }

    protected function cloudfrontPath(): Attribute
    {
        return Attribute::make(
            get: fn () => Media::s3ToCloudfront($this->path),
        );
    }

    protected function cloudfrontThumbnailPath(): Attribute
    {
        return Attribute::make(
            get: fn () => Media::s3ToCloudfront($this->thumbnail_path),
        );
    }

    public static function s3ToCloudfront(?string $path): ?string
    {
        $cloudfrontUrl = config('services.cloudfront.url');

        if (is_null($cloudfrontUrl) || is_null($path)) return $path;

        return str($path)
            ->replace(
                search: 'https://s3.amazonaws.com/'.config('filesystems.disks.s3.bucket').'/',
                replace: Str::finish($cloudfrontUrl, '/')
            )
            ->value();
    }

    public function tags(): BelongsToMany
    {
        return $this->belongsToMany(Tag::class, 'media_tag')->withTimestamps();
    }

    private function addDocument(UploadedFile $file, array $tags = []): Media
    {
        $size = $file->getSize();
        $path = $this->getUploadPaths($this->generateFilename($file), 'documents');

        $document = $this->create([
            'title' => str_replace(' ', '-', $file->getClientOriginalName()),
            'path' => $path['full_path'],
            'thumbnail_path' => '/images/pdf.png',
            'height' => '',
            'width' => '',
            'size' => $size,
            'layout' => '',
            'caption' => '',
            'type' => 'document'
        ]);

        // Save full size
        Storage::disk('s3')->put($path['upload_path'], file_get_contents($file), 'public');

        if ($tags) {
            return $document->assignTags($tags);
        }

        return $document;
    }

    public function scopeImages(Builder $query): Builder
    {
        return $query->where('type', 'image');
    }

    public function scopeVideo(Builder $query): Builder
    {
        return $query->where('type', 'video');
    }

    public function scopeDocuments(Builder $query): Builder
    {
        return $query->where('type', 'document');
    }

    public function getFileSizeFormattedAttribute(): string
    {
        if ($this->size < 1048576) {
            return number_format($this->size / 1024, 2) . 'KB';
        }
        return number_format($this->size / 1048576, 2) . 'MB';
    }

    public function getCreatedAtFormattedAttribute(): string
    {
        return $this->created_at->format('M dS, Y @ g:m A');
    }
}
