<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\Payment
 *
 * @property int $id
 * @property string $key
 * @property string $title
 * @property string $instructions
 * @property string $agreement
 * @property int $enabled
 * @method static \Illuminate\Database\Eloquent\Builder|Payment enabled()
 * @method static \Illuminate\Database\Eloquent\Builder|Payment newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Payment newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Payment query()
 * @method static \Illuminate\Database\Eloquent\Builder|Payment whereAgreement($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Payment whereEnabled($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Payment whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Payment whereInstructions($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Payment whereKey($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Payment whereTitle($value)
 * @method static \Database\Factories\PaymentFactory factory($count = null, $state = [])
 * @mixin \Eloquent
 */
class Payment extends Model
{
    use HasFactory;

    protected $guarded = ['id', 'key'];

    public $timestamps = false;

    public function scopeEnabled($q)
    {
        return $q->where('enabled', true);
    }

    public function isCard(): bool
    {
        return $this->key === 'card';
    }

    public function isDisabled(): bool
    {
        return ! $this->enabled;
    }

    public function icon()
    {
        switch ($this->key) {
            case 'card':
                return '<i class="fas fa-credit-card fa-fw"></i>';
            case 'invoice':
                return '<i class="fas fa-file-alt fa-fw"></i>';
            case 'pickup':
                return '<i class="fas fa-map-marker-alt fa-fw"></i>';
        }
    }
}
