<?php

namespace App\Models;

use C<PERSON>brock\EloquentSluggable\Sluggable;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

/**
 * App\Models\Tag
 *
 * @property int $id
 * @property string $title
 * @property string $slug
 * @property int $type
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Models\Post[] $posts
 * @property-read int|null $posts_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Models\Product[] $products
 * @property-read int|null $products_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Models\Recipe[] $recipes
 * @property-read int|null $recipes_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Models\User[] $users
 * @property-read int|null $users_count
 * @method static \Illuminate\Database\Eloquent\Builder|Tag findSimilarSlugs(string $attribute, array $config, string $slug)
 * @method static \Illuminate\Database\Eloquent\Builder|Tag forProducts($productIds = [])
 * @method static \Illuminate\Database\Eloquent\Builder|Tag newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Tag newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Tag orders()
 * @method static \Illuminate\Database\Eloquent\Builder|Tag query()
 * @method static \Illuminate\Database\Eloquent\Builder|Tag whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Tag whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Tag whereSlug($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Tag whereTitle($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Tag whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Tag whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Tag withUniqueSlugConstraints(\Illuminate\Database\Eloquent\Model $model, string $attribute, array $config, string $slug)
 * @method static \Database\Factories\TagFactory factory($count = null, $state = [])
 * @mixin \Eloquent
 */
class Tag extends Model
{
    use HasFactory, Sluggable;

    const INVENTORY_ERROR = 'Inventory Error';
    const ADDRESS_ERROR = 'Address Error';
    const MINIMUM_ERROR = 'Minimum Error';
    const EXCLUSIVITY_ERROR = 'Exclusivity Error';

    protected static array $types = [
        'product' => 1,
        'order' => 2,
        'post' => 3,
        'recipe' => 4,
        'user' => 5,
        'media' => 6
    ];
    protected $guarded = [];

    public static function type(string $type): int
    {
        return collect(self::$types)->get($type);
    }

    /**
     * Return the sluggable configuration array for this model.
     */
    public function sluggable(): array
    {
        return [
            'slug' => [
                'source' => 'title'
            ]
        ];
    }

    /**
     * @param  Builder<Tag>  $query
     * @return Builder<Tag>
     */
    public function scopeOrders($query): Builder
    {
        return $query->where('type', self::$types['order']);
    }

    /**
     * @param  Builder<Tag>  $query
     * @return Builder<Tag>
     */
    public function scopePosts(Builder $query): Builder
    {
        return $query->where('type', self::$types['post']);
    }

    /**
     * @param  Builder<Tag>  $query
     * @param  array  $productIds
     * @return Builder<Tag>
     */
    public function scopeForProducts(Builder $query, array $productIds = []): Builder
    {
        return $query->whereHas(
            'products',
            fn($products) => $products->whereIn('product_id', $productIds)
        );
    }

    public function storeUrl(): string
    {
        // Is under a Vendor or Protocol
        if (request()->segment(2) == 'vendor' || request()->segment(2) == 'protocol') {
            return '/' . request()->segment(1) . '/' . request()->segment(2) . '/' . request()->segment(3) . '/' . $this->slug;
        }

        // Is under a collection.
        if (request()->segment(3)) {
            return '/' . request()->segment(1) . '/' . request()->segment(2) . '/' . $this->slug;
        }

        if (is_null(request()->segment(2))) {
            return '/' . request()->segment(1) . '?tag=' . $this->slug;
        }

        return request()->url() . '/' . $this->slug;
    }

    /**
     * @return BelongsToMany<Product, $this>
     */
    public function products(): BelongsToMany
    {
        return $this->belongsToMany(Product::class)->withTimestamps();
    }

    /**
     * @return BelongsToMany<Recipe, $this>
     */
    public function recipes(): BelongsToMany
    {
        return $this->belongsToMany(Recipe::class)
            ->withTimestamps();
    }

    /**
     * @return BelongsToMany<Post, $this>
     */
    public function posts(): BelongsToMany
    {
        return $this->belongsToMany(Post::class)
            ->withTimestamps();
    }

    /**
     * @return BelongsToMany<User, $this>
     */
    public function users(): BelongsToMany
    {
        return $this->belongsToMany(User::class)
            ->withTimestamps();
    }
}
