<?php

namespace App\Exceptions;

use App\Models\Pickup;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;

class ExclusivityException extends Exception
{
    protected $pickup;
    protected $excludedProducts = [];

    public function __construct(Pickup $pickup, iterable $products = [], string $message = 'This product is currently not available at your location.')
    {
        $this->pickup = $pickup;
        $this->excludedProducts = $products;
        $this->message = $message;
        parent::__construct();
    }

    /**
     * @return JsonResponse|RedirectResponse
     */
    public function render(Request $request)
    {
        if ($request->ajax()) {
            return response()->json($this->getMessage(), 400);
        }

        error($this->getMessage());

        return back();
    }

    public function defaultMessage(): string
    {
        return 'These products are not available at ' . $this->getPickup()->title . ':' . $this->buildProductList() . '<a href="/cart">Please remove these items</a> to change locations.';
    }

    public function buildProductList(): string
    {
        $output = '<ul>';
        foreach ($this->excludedProducts as $product) {
            $output .= "<li>{$product->title}</li>";
        }
        $output .= '</ul>';
        return $output;
    }

    public function getPickup(): Pickup
    {
        return $this->pickup;
    }

    public function getExcludedProducts(): iterable
    {
        return $this->excludedProducts;
    }
}
