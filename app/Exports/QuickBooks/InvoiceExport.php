<?php

namespace App\Exports\QuickBooks;

use App\Models\Order;
use App\Support\Enums\Channel;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Str;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithCustomChunkSize;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;

class InvoiceExport implements FromQuery, WithHeadings, WithMapping, ShouldAutoSize, WithCustomChunkSize, WithColumnFormatting
{
    use Exportable;

    public array $salesChannels = [];

    public function __construct(public Builder $orderQuery)
    {
        $this->salesChannels = Channel::all()->toArray();
    }

    public function columnFormats(): array
    {
        return [
            'AE' => NumberFormat::FORMAT_NUMBER_00,
            'AG' => NumberFormat::FORMAT_NUMBER_00,
            'AH' => NumberFormat::FORMAT_NUMBER_00,
            'AI' => NumberFormat::FORMAT_NUMBER_00,
            'AJ' => NumberFormat::FORMAT_NUMBER_00,
            'AL' => NumberFormat::FORMAT_NUMBER_00,
            'AT' => NumberFormat::FORMAT_NUMBER_00,
            'AU' => NumberFormat::FORMAT_NUMBER_00,
        ];
    }

    public function chunkSize(): int
    {
        return 500;
    }

    public function query(): Builder
    {
        return $this->orderQuery
            ->with(['customer', 'fees', 'items.product', 'discounts', 'payments', 'paymentMethod', 'pickup']);
    }

    public function headings(): array
    {
        return array_merge($this->getOrderHeaders(), $this->getProductHeaders());
    }

    public function map($order): array
    {
        $rows = [];
        $orderDetails = $this->getOrderDetails($order);

        // Add line items
        foreach ($order->items as $item) {
            $rows[] = array_merge($orderDetails, array_combine($this->getProductHeaders(), [
                $item->product->title, // Product/Service
                $item->product->getSku(), // Product/Service SKU
                addslashes($item->title), // Product/Service Description
                $item->isPricedByWeight() ? $item->weight : $item->fulfilledQuantity(), // Product/Service Quantity'
                $item->fulfilledQuantity(), // 'Product/Service Packages',
                money($item->unit_price, ''), // Product/Service Rate
                money($item->subtotal, ''), // Product/Service Amount
                $item->product->taxable ? 'TRUE' : 'FALSE',  // Product/Service Taxable
                $this->salesChannels[$order->type_id] ?? null, // Product/Service Class
                $item->product->accounting_class ?? null, // Product/Service Class 2
            ]));
        }

        // Add discounts
        foreach ($order->discounts as $discount) {
            $rows[] = array_merge($orderDetails, array_combine($this->getProductHeaders(), [
                trim($discount->code), // Product/Service
                trim($discount->code), // Product/Service SKU
                addslashes($discount->description . ' (' . $discount->code . ')'), // Product/Service Description
                1, // Product/Service Quantity'
                1, // Product/Service Packages'
                money(-$discount->getRelationValue('pivot')->savings, ''), // Product/Service Rate
                money(-$discount->getRelationValue('pivot')->savings, ''), // Product/Service Amount
                'FALSE',  // Product/Service Taxable
                $this->salesChannels[$order->type_id] ?? null, // Product/Service Class
                null, // Product/Service Class 2
            ]));
        }

        // Add fees
        foreach ($order->fees as $fee) {
            $rows[] = array_merge($orderDetails, array_combine($this->getProductHeaders(), [
                $fee->title, // Product/Service
                Str::slug($fee->title), // Product/Service SKU
                addslashes($fee->title), // Product/Service Description
                $fee->qty, // Product/Service Quantity'
                $fee->qty, // Product/Service Packages'
                money($fee->amount, ''), // Product/Service Rate
                money($fee->subtotal, ''), // Product/Service Amount
                $fee->taxable ? 'TRUE' : 'FALSE',  // Product/Service Taxable
                $this->salesChannels[$order->type_id] ?? null, // Product/Service Class
                null, // Product/Service Class 2
            ]));
        }

        // Add credit applied
        $rows[] = array_merge($orderDetails, array_combine($this->getProductHeaders(), [
            'Credit Applied', // Product/Service
            'credit-applied', // Product/Service SKU
            'Credit Applied', // Product/Service Description
            1, // Product/Service Quantity'
            1, // Product/Service Packages'
            $order->credit_applied ? money(-$order->credit_applied, '') : '0', // Product/Service Rate
            $order->credit_applied ? money(-$order->credit_applied, '') : '0', // Product/Service Amount
            'FALSE',  // Product/Service Taxable
            $this->salesChannels[$order->type_id] ?? null, // Product/Service Class
            null, // Product/Service Class 2
        ]));

        // Add delivery fee
        $rows[] = array_merge($orderDetails, array_combine($this->getProductHeaders(), [
            'Delivery Fee', // Product/Service
            'delivery-fee', // Product/Service SKU
            'Delivery Fee', // Product/Service Description
            1, // Product/Service Quantity'
            1, // Product/Service Packages'
            money($order->delivery_fee, ''), // Product/Service Rate
            money($order->delivery_fee, ''), // Product/Service Amount
            'FALSE',  // Product/Service Taxable
            $this->salesChannels[$order->type_id] ?? null, // Product/Service Class
            null, // Product/Service Class 2
        ]));

        return $rows;
    }

    private function getOrderHeaders(): array
    {
        return [
            'Invoice No',
            'Customer',
            'Company',
            'Invoice Date',
            'Due Date',
            'Shipping Date',
            'Ship Via',
            'Tracking No',
            'Terms',
            'Billing Address Line 1',
            'Billing Address Line 2',
            'Billing Address City',
            'Billing Address Postal Code',
            'Billing Address Country',
            'Billing Address State',
            'Shipping Address Line 1',
            'Shipping Address Line 2',
            'Shipping Address City',
            'Shipping Address Postal Code',
            'Shipping Address Country',
            'Shipping Address State',
            'Memo',
            'Message displayed on sales receipt',
            'Email',
            'Email CC',
            'Email BCC',
            'Customer First Name',
            'Customer Last Name',
            'Print Status',
            'Email Status',
            'Shipping',
            'Sales Tax Code',
            'Sales Tax Amount',
            'Discount Amount',
            'Discount Percent',
            'Subscription Savings',
            'Apply Tax after Discount',
            'Deposit',
            'Location',
            'Enable ACH Payment',
        ];
    }

    private function getProductHeaders(): array
    {
        return [
            'Product/Service',
            'Product/Service SKU',
            'Product/Service Description',
            'Product/Service Quantity',
            'Product/Service Packages',
            'Product/Service Rate',
            'Product/Service Amount',
            'Product/Service Taxable',
            'Product/Service Class',
            'Product/Service Class 2',
        ];
    }

    private function getOrderDetails(Order $order): array
    {
        return array_combine($this->getOrderHeaders(), [
            $this->getDocumentId($order), // Invoice No
            $this->getCustomer($order), // Customer
            $this->getCompany($order), // Company
            $order->pickup_date ? $order->pickup_date->format('m/d/Y') : null, // Invoice Date
            $order->due_date ? $order->due_date->format('m/d/Y') : null, // Due Date.
            $order->pickup_date ? $order->pickup_date->format('m/d/Y') : null, // Shipping Date.
            null, // Ship Via
            $order->tracking_id ? substr($order->tracking_id, 0, 30) : null, // Tracking No
            $order->payment_terms, // Terms
            $order->billing_street, // Billing Address Line 1
            $order->billing_street_2, // Billing Address Line 2
            $order->billing_city, // Billing City
            $order->billing_zip, // Billing Address Postal Code
            $order->billing_country ?? 'USA', // Billing Address Country
            $order->billing_state, // Billing Address State
            $order->shipping_street, // Shipping Address Line 1
            $order->shipping_street_2, // Shipping Address Line 2
            $order->shipping_city, // Shipping Address City
            $order->shipping_zip, // Shipping Address Postal Code
            $order->shipping_country ?? 'USA', // Shipping Address Country
            $order->shipping_state, // Shipping Address State
            $order->packing_notes ? substr($order->packing_notes, 0, 4000) : null, // Memo
            $order->invoice_notes ? substr($order->invoice_notes, 0, 1000) : null, // Message displayed on sales receipt
            $order->customer_email, // Email
            $order->customer_email_alt, // CC Email
            null, // BCC Email
            $order->customer_first_name,
            $order->customer_last_name,
            'FALSE', // Print status
            'FALSE', // Email status
            money(0.00,''), // Shipping
            $order->pickup ? $order->pickup->state : null, // Sales Tax Code
            money($order->tax, ''), // Sales Tax Amount
            money($order->order_discount, ''), // Discount Amount
            null, // Discount Percent
            money($order->subscription_savings ?? 0, ''), // Subscription Savings
            'FALSE', // Apply Tax after Discount (default is false)
            money(0.00,''), // Deposit
            $order->pickup ? addslashes($order->pickup->title) : null, // Location
            'TRUE', // Enable ACH Payment
        ]);
    }

    private function getDocumentId(Order $order): string
    {
        return substr('GC-' . $order->id, 0, 21);
    }

    private function getCustomer(Order $order): string
    {
        $firstName = !empty($order->customer_first_name) ? $order->customer_first_name : 'N/A';
        $lastName = !empty($order->customer_last_name) ? $order->customer_last_name : 'N/A';
        $email = !empty($order->customer_email) ? ' (' . $order->customer_email . ')' : '';

        if ($order->customer) {
            $firstName = !empty($order->customer->first_name) ? $order->customer->first_name : $firstName;
            $lastName = !empty($order->customer->last_name) ? $order->customer->last_name : $lastName;
            $email = !empty($order->customer->email) ? ' (' . $order->customer->email . ')' : $email;
        }

        if (!empty($order->customer->accounting_id)) {
            return $order->customer->accounting_id;
        }

        return "{$firstName} {$lastName}{$email}";
    }

    private function getCompany(Order $order): string
    {
        if (empty($order->customer) || empty($order->customer->company_name)) {
            return $order->customer_first_name . ' ' . $order->customer_last_name;
        }

        return $order->customer->company_name;
    }
}
