<?php

namespace App\Exports;

use App\Models\User;
use App\Support\Enums\UserRole;
use Illuminate\Database\Eloquent\Builder;

class UsersExportForDrip
{
    protected $chunkSize = 500;
    protected $fileName = 'customers_for_drip.csv';

    public function export(Builder $builder)
    {
        $rowHeaders = [
            'first_name',
            'last_name',
            'email',
            'city',
            'state',
            'postal_code',
            'country',
            'fulfillment_name',
            'fulfillment_display_name',
            'fulfillment_id',
            'schedule_id',
            'store_credit',
            'last_purchase',
            'last_purchase_timestamp',
            'order_count',
            'is_active',
            'newsletter_recipient',
            'notifications_recipient',
            'role',
            'referral_code'
        ];

        return response()->stream(function () use ($rowHeaders, $builder) {
            $export = fopen('php://output', 'w');
            if(ob_get_length() > 0) ob_clean();
            fputcsv($export, $rowHeaders);
            $builder->chunk($this->chunkSize, function ($resourceChunk) use ($export) {
                foreach ($resourceChunk as $resource) {
                    /** @var User $resource */

                    fputcsv($export, [
                        $resource->first_name,
                        $resource->last_name,
                        $resource->email,
                        $resource->city,
                        $resource->state,
                        $resource->zip,
                        $resource->country,
                        $resource->pickup['title'] ?? '',
                        $resource->pickup['display_name'] ?? null,
                        $resource->pickup_point,
                        $resource->pickup['schedule_id'] ?? null,
                        money($resource->credit),
                        $resource->last_purchase ? $resource->last_purchase->format('m/d/Y') : '',
                        $resource->last_purchase ? $resource->last_purchase->timestamp : '',
                        $resource->order_count,
                        $resource->active ? 'Yes' : 'No',
                        $resource->newsletter ? 'Yes' : 'No',
                        $resource->order_deadline_email_reminder ? 'Yes' : 'No',
                        UserRole::get($resource->role_id ?? 4),
                        $resource->referral_code,
                    ]);
                }
            });
            fclose($export);
        }, 200, [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $this->fileName . '"',
        ]);
    }
}
