<?php

namespace App\Exports;

use App\Models\User;
use App\Support\Enums\UserRole;
use Illuminate\Database\Eloquent\Builder;

class UsersExport
{
    protected $chunkSize = 500;
    protected $fileName = 'customers.csv';

    public function export(Builder $builder)
    {
        $rowHeaders = [
            'id',
            'first_name',
            'last_name',
            'email',
            'registration_date',
            'phone',
            'shipping_street',
            'shipping_street_2',
            'shipping_city',
            'shipping_state',
            'shipping_postal_code',
            'shipping_country',
            'fulfillment_location',
            'fulfillment_location_id',
            'credit',
            'last_purchase',
            'orders',
            'active',
            'newsletter',
            'deadline_reminders',
            'role',
            'ref_code',
            'company_name',
            'notes'
        ];

        return response()->stream(function () use ($rowHeaders, $builder) {
            $export = fopen('php://output', 'w');
            ob_end_clean();
            fputcsv($export, $rowHeaders);
            $builder->chunk($this->chunkSize, function ($resourceChunk) use ($export) {
                foreach ($resourceChunk as $resource) {
                    /** @var User $resource */

                    fputcsv($export, [
                        $resource->id,
                        $resource->first_name,
                        $resource->last_name,
                        $resource->email,
                        $resource->created_at->format('m/d/y'),
                        $resource->phone,
                        $resource->street,
                        $resource->street_2,
                        $resource->city,
                        $resource->state,
                        $resource->zip,
                        $resource->country,
                        $resource->pickup['title'] ?? '' ,
                        $resource->pickup_point,
                        money($resource->credit),
                        $resource->last_purchase ? $resource->last_purchase->format('m/d/Y') : '',
                        $resource->order_count,
                        $resource->active ? 'Yes' : 'No',
                        $resource->newsletter ? 'Yes' : 'No',
                        $resource->order_deadline_email_reminder ? 'Yes' : 'No',
                        UserRole::get($resource->role_id ?? 4),
                        $resource->referral_code,
                        $resource->company_name,
                        $resource->notes
                    ]);
                }
            });
            fclose($export);
        }, 200, [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $this->fileName . '"',
        ]);
    }
}
