<?php

namespace App\Exports;

use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;

class TenantStripePlanExport implements FromCollection, WithHeadings
{
    public function __construct(
        private Collection $export
    ) {}

    public function collection(): Collection
    {
        return $this->export;
    }

    public function headings(): array
    {
        return [
            'tenant_id',
            'tenant_tenant_id',
            'tenant_name',
            'name',
            'email',
            'role',
            'stripe_price'
        ];
    }
}