<?php

namespace App\Exports;

class HarvestReportExport
{
    protected $chunkSize = 500;
    protected $fileName = 'bundle_sales_report.csv';
    protected $bundleSales;

    public function export(array $bundleSales)
    {
        $this->bundleSales = $bundleSales;

        $rowHeaders = [
            'title',
            'sku',
            'qty',
            'on_site_inventory',
            'reorder_threshold',
            'sort',
            'barcode',
            'weight_lbs'
        ];

        return response()->stream(function () use ($rowHeaders) {
            $export = fopen('php://output', 'w');
            fputcsv($export, $rowHeaders);
            foreach ($this->bundleSales as $resource) {
                fputcsv($export, [
                    $resource['title'] ?? '',
                    $resource['sku'] ?? '',
                    $resource['count_on_order'] ?? '',
                    $resource['inventory'] ?? '',
                    $resource['stock_out_inventory'] ?? 0,
                    $resource['sort'] ?? '',
                    $resource['barcode'] ?? '',
                    round($resource['weight'] ?? 0, 3),
                ]);
            }
            fclose($export);
        }, 200, [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $this->fileName . '"',
        ]);
    }
}
