<?php

namespace App\Http\Responses;

use Illuminate\Contracts\Support\Responsable;

abstract class PrintResponse implements Responsable
{
    protected $template;
    protected $viewPath;
    protected $cssPath;
    protected $style = 'default';
    protected $view;

    protected function getView()
    {
        if (view()->exists('print_templates.' . $this->template . '/' . $this->style . '.index')) {
            $this->view = view('print_templates.' . $this->template . '/' . $this->style . '.index');
        } else {
            $this->view = view('print_templates.' . $this->template . '/default.index');
        }

        $this->viewPath = implode('/', explode('/', $this->view->getPath(), -1));
        $this->cssPath = $this->viewPath . '/style.css';
    }
}
