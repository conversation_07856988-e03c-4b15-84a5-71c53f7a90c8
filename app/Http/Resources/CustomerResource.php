<?php

namespace App\Http\Resources;

use App\Models\User;
use Illuminate\Http\Resources\Json\JsonResource;

class CustomerResource extends JsonResource
{
    public function toArray($request): array
    {
        /** @var User $user */
        $user = $this->resource;

        return [
            'id' => $user->id,
            'first_name' => $user->first_name,
            'last_name' => $user->last_name,
            'phone_number' => $user->phone,
            'email' => $user->email,
            'delivery_method' => new DeliveryMethodResource($user->pickup),
            'shipping_address' => $user->street ? [
                'street' => $user->street,
                'street_2' => $user->street_2,
                'city' => $user->city,
                'state' => $user->state,
                'postal_code' => $user->zip,
                'country' => $user->country
            ] : null,
            'order_count' => $user->order_count,
            'is_subscriber' => ! is_null($user->recurringOrder),
            'created_at' => $user->created_at->toIso8601String(),
            'updated_at' => $user->updated_at->toIso8601String(),
        ];
    }
}
