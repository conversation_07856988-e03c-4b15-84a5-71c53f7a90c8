<?php

namespace App\Http\Resources;

use App\Models\RecurringOrderItem;
use Illuminate\Http\Resources\Json\JsonResource;

class SubscriptionItemResource extends JsonResource
{
    public function toArray($request): array
    {
        /** @var RecurringOrderItem $item */
        $item = $this->resource;

        return [
            'id' => $item->id,
            'type' => $item->type,
            'quantity' => $item->qty,
            'product_id' => $item->product_id,
            'product' => new ProductResource($item->product),
            'created_at' => $item->created_at->toIso8601String(),
            'updated_at' => $item->updated_at->toIso8601String(),
        ];
    }
}
