<?php

namespace App\Http\Controllers\API;

use Illuminate\Http\JsonResponse;
use App\Contracts\CartService;
use App\Exceptions\CouponInvalidException;
use App\Http\Controllers\Controller;
use App\Models\Coupon;
use App\Models\Order;
use Illuminate\Validation\Rule;

class CartPromotionController extends Controller
{
    public function __invoke(CartService $cart_service): JsonResponse
    {
        request()->validate([
            'code' => ['required', Rule::exists(Coupon::class)]
        ]);

        $shopper = request()->shopper();

        $cart = $cart_service->find(shopper_type: $shopper['type'], shopper_id: $shopper['id']);

        if (is_null($cart)) {
            return response()->json([
                'amount' => 0,
                'message' => 'Coupon could not be applied to cart.'
            ], 409);
        }

        $coupon = Coupon::where('code', request('code'))->first();

        try {
            $coupon->validateForCart($cart);
        } catch (CouponInvalidException $e) {
            return response()->json([
                'amount' => 0,
                'message' => $e->getMessage()
            ], 409);
        }

        $cart->applyCouponToCart($coupon);

        return response()->json([
            'code' => $coupon->code,
            'name' => $coupon->description,
            'amount' => $coupon->valueForCart($cart),
            'message' => 'success'
        ], 201);
    }
}