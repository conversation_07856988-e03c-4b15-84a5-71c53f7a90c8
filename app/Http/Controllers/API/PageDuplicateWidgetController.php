<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\Page;
use App\Models\Widget;
use Illuminate\Http\JsonResponse;

class PageDuplicateWidgetController extends Controller
{
    public function store(Page $page, Widget $widget): JsonResponse
    {
        $duplicate_widget = $widget
            ->replicate(['title'])
            ->fill(['title' => $widget->title . ' (copy)']);

        $duplicate_widget->save();

        $page->insertWidget($duplicate_widget);

        return response()->json($duplicate_widget, 201);
    }
}
