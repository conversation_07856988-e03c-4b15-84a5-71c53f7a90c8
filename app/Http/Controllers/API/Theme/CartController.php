<?php

namespace App\Http\Controllers\API\Theme;

use Illuminate\Http\JsonResponse;
use App\Contracts\CartService;
use App\Events\User\UserUpdated;
use App\Http\Controllers\Controller;
use App\Models\Pickup;
use Illuminate\Http\Request;

class CartController extends Controller
{
    public function __invoke(Request $request, CartService $cart_service): JsonResponse
    {
        /** @var array $shopper */
        $shopper = $request->shopper();

        return response()->json([
            'data' => $cart_service->find(
                shopper_type: $shopper['type'],
                shopper_id: $shopper['id']
            )?->toCartArray()
        ]);
    }
}
