<?php

namespace App\Http\Controllers\Theme;

use App\Contracts\CartService;
use App\Events\Cart\CartCreated;
use App\Http\Controllers\Controller;
use App\Models\Product;
use Exception;

class CartItemController extends Controller
{
    public function store(CartService $cart_service)
    {
        $validated = request()->validate([
            'product_id' => ['required', 'exists:products,id'],
        ]);

        $shopper = request()->shopper();

        $cart = $cart_service->find(shopper_type: $shopper['type'], shopper_id: $shopper['id']);

        if (is_null($cart)) {
            $cart = $cart_service->create(shopper_type: $shopper['type'], shopper_id: $shopper['id']);
        }

        /** @var Product $product */
        $product = Product::query()
            ->with(['price' => fn($query) => $query->where('group_id', $cart->cartPricingGroupId() ?? 0)])
            ->find($validated['product_id']);

        $cart_was_empty = $cart->cartIsEmpty();

        try {
            $item = $cart->addProduct($product);
        } catch (Exception $exception) {
            if (request()->expectsJson()) {
                return response()->json($exception->getMessage());
            }

            error($exception->getMessage());
            return back();
        }

        if ($cart_was_empty) {
            CartCreated::dispatch($cart);
        }

        if (request()->expectsJson()) {
            return response()->json(compact('item'));
        }

        return back();
    }

    public function update($id, CartService $cart_service)
    {
        $validated = request()->validate([
            'quantity' => ['required', 'integer', 'min:0']
        ]);

        if ($validated['quantity'] === 0) {
            return $this->destroy($id, $cart_service);
        }

        $shopper = request()->shopper();

        $cart = $cart_service->find(shopper_type: $shopper['type'], shopper_id: $shopper['id']);

        $item = null;
        
        if ( ! is_null($cart)) {
            $item = $cart->updateCartItemQuantity($id, $validated['quantity']);
        }

        if (request()->expectsJson()) {
            return response()->json(compact('item'));
        }

        return back();
    }

    public function destroy($id, CartService $cart_service)
    {
        $shopper = request()->shopper();

        $cart = $cart_service->find(shopper_type: $shopper['type'], shopper_id: $shopper['id']);

        if (!is_null($cart)) {
            $cart->removeCartItem($id);
        }

        if (request()->expectsJson()) {
            return response()->json($cart->toCartArray(), 204);
        }

        return back();
    }
}
