<?php

namespace App\Http\Controllers\Theme;

use App\Events\User\UserSubscribedToSmsMarketing;
use App\Http\Controllers\Controller;
use App\Rules\ValidPhoneNumber;
use Illuminate\Http\RedirectResponse;

class SmsMarketingController extends Controller
{
    public function store(): RedirectResponse
    {
        request()->validate([
            'phone_number' => ['required', 'string', 'max:16', new ValidPhoneNumber],
            'opt_in_location' => ['in:after_registration,on_order_confirmation']
        ]);

        $user = auth()->user();

        $user->cell_phone = request('phone_number');
        $user->subscribed_to_sms_marketing_at = now();

        $user->save();

        UserSubscribedToSmsMarketing::dispatch($user, request('opt_in_location') ?? 'after_registration');

        $redirect_to = route('store.index');

        if (request('opt_in_location') === 'after_registration') {
            $redirect_to = url(setting('user_registration_redirect', '/store'));
        }

        flash('You have successfully subscribed to SMS messages.');

        return redirect($redirect_to);
    }

    public function destroy(): RedirectResponse
    {
        auth()->user()
            ->fill(['subscribed_to_sms_marketing_at' => null])
            ->save();

        flash('You have successfully unsubscribed to SMS messages.');

        return redirect(route('store.index'));
    }
}
