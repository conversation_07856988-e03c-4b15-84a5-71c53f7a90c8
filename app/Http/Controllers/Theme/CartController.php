<?php

namespace App\Http\Controllers\Theme;

use Illuminate\Http\JsonResponse;
use App\Contracts\CartService;
use App\Http\Controllers\Controller;
use App\Models\Cart;
use App\Services\SubscriptionSettingsService;
use Illuminate\Validation\Rule;

class CartController extends Controller
{
    public function show(CartService $cart_service)
    {
        $shopper = request()->shopper();

        if ( ! is_null($order = auth()->user()?->queryOpenOrder())) {
            return redirect()->route('customer.orders.show', compact('order'));
        }

        $cart = $cart_service->find(shopper_type: $shopper['type'], shopper_id: $shopper['id'])
            ?? Cart::stub(auth()->user());

        return view('theme::cart.show')
            ->with(compact('cart'));
    }

    public function update(CartService $cart_service): JsonResponse
    {
        $shopper = request()->shopper();

        $cart = $cart_service->find(shopper_type: $shopper['type'], shopper_id: $shopper['id']);

        abort_if(is_null($cart), 404);

        $validated = request()->validate([
            'purchase_type' => ['required', 'string', Rule::in(['one_time_purchase', 'recurring'])]
        ]);

        match($validated['purchase_type']) {
            'recurring' => $cart->setCartAsSubscriptionPurchase(
                frequency: null,
                product_incentive_id: app(SubscriptionSettingsService::class)->defaultProductIncentiveId()
            ),
            default => $cart->setCartAsOneTimePurchase(),
        };

        if (request()->expectsJson()) {
            return response()->json(['data' => $cart->toCartArray()]);
        }

        return new JsonResponse($cart->toCartArray(), 200);
    }
}
