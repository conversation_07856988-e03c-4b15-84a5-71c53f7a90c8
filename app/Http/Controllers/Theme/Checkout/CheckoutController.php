<?php

namespace App\Http\Controllers\Theme\Checkout;

use App\Http\Controllers\Controller;
use App\Livewire\Theme\FetchesCart;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;

class CheckoutController extends Controller
{
    use FetchesCart;

    public function index(Request $request): RedirectResponse
    {
        $cart = $this->fetchShopperCart(should_stub: false);

        if (is_null($cart)) {
            return redirect(route('store.index'));
        }

        return redirect(route('checkout.offers.show'));
    }
}
