<?php

namespace App\Http\Controllers\Theme\Checkout;

use Illuminate\View\View;
use App\Http\Controllers\Controller;
use App\Models\Order;

class GiftCardConfirmationController extends Controller
{
    public function __invoke(Order $order): View
    {
        abort_if($order->customer_id !== auth()->id(), 404);

        return view('theme::checkout.gift-card.complete')
            ->with(compact('order'));
    }
}
