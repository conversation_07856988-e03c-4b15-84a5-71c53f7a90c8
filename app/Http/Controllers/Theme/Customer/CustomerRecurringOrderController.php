<?php

namespace App\Http\Controllers\Theme\Customer;

use App\Actions\Subscription\SyncItemToCurrentOrder;
use App\Actions\Subscription\SyncSubscriptionDatetimes;
use App\Events\Subscription\SubscriptionFrequencyWasUpdated;
use App\Events\Subscription\SubscriptionWasExpedited;
use App\Http\Controllers\Controller;
use App\Models\Order;
use App\Models\Product;
use App\Models\RecurringOrder;
use App\Models\User;
use App\Notifications\RecurringOrderCanceledFeedback;
use App\Services\StoreService;
use App\Services\SubscriptionSettingsService;
use Carbon\Carbon;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Notification;
use Illuminate\Validation\Rule;

class CustomerRecurringOrderController extends Controller
{
    public function edit()
    {
        $subscription = app(StoreService::class)->mostRecentSubscription();

        if (is_null($subscription)) {
            flash("Shop the store and select Subscribe & Save at checkout.");
            return redirect(route('store.index'));
        }

        return view('theme::customers.recurring-orders', compact('subscription'));
    }

    public function updatePromoItem(Request $request)
    {
        $validated = $request->validate([
            'promo_item' => [
                'required', 'integer', Rule::in(app(SubscriptionSettingsService::class)->productIncentiveIds())
            ]
        ]);

        $product = Product::findOrFail($validated['promo_item']);

        /** @var RecurringOrder|null $subscription */
        $subscription = auth()->user()->recurringOrder;

        // used at legacy checkout item selection
        if (is_null($subscription)) {
            $cart = $this->shopperCart();

            if (is_null($cart)) {
                error('There is no active subscription.');
                return back();
            }

            $cart->setCartAsSubscriptionPurchase(null, $product->id);
        } else {
            // used on subscription management page
            $item = $subscription->items()->updateOrCreate(['type' => 'promo'], [
                'customer_id' => $subscription->customer_id,
                'product_id' => $product->id,
                'qty' => 1,
            ]);

            app(SyncItemToCurrentOrder::class)->handle($item);
        }

        return back();
    }

    public function skip(Order $order)
    {
        if ($order->customer_id !== auth()->id()) {
            return back();
        }

        $validated = request()->validate([
            'week_frequency' => ['required', 'in:1,2,3,4,custom'],
            'custom_date' => ['required_if:week_frequency,custom', 'date', 'date_format:Y-m-d']
        ]);

        if ( ! $order->isFromBlueprint()) {
            error('This order cannot be skipped.');
            return back();
        }

        if ($order->deadlineHasPassed()) {
            error('The deadline for making changes to this order has already passed.');
            return back();
        }

        if ( ! is_null($order->skipped_at)) {
            error('The order has already been skipped.');
            return back();
        }

        if ($validated['week_frequency'] === 'custom') {
            $updated_subscription = $order->rescheduleTo(Carbon::parse($validated['custom_date']));
        } else {
            $updated_subscription = $order->skipDays($validated['week_frequency'] * 7);
        }

        if ($updated_subscription->generate_at < now()) {
            $updated_subscription->generateNextOrder(synchonous: true);
        }

        flash('The order has been skipped and the schedule has been updated.');

        return back();
    }

    public function cancel(): RedirectResponse
    {
        request()->validate([
            'cancellation_feedback' => ['required', 'string']
        ]);

        /** @var User $user */
        $user = auth()->user();

        if ( ! $user->recurringOrder) {
            error('Your subscription has already been canceled.');
            return redirect(route('store.index'));
        }

        $user->recurringOrder->cancel();

        Notification::route('mail', setting('email_general'))
            ->notify(new RecurringOrderCanceledFeedback($user->recurringOrder, request('cancellation_feedback')));

        flash('Your subscription has been canceled.');
        return redirect(route('store.index'));
    }

    public function getItSooner(Request $request)
    {
        $validated = $request->validate([
            'order_id' => ['required', 'integer', Rule::exists('orders', 'id')->where('customer_id', auth()->id())],
        ]);

        $subscription = auth()->user()->recurringOrder;

        if (is_null($subscription)) {
            error('You not have any active recurring orders.');
            return back();
        }

        $order = Order::find($validated['order_id']);

        $old_delivery_date = $order->pickup_date->copy();

        /** @var Order $order */
        $next_order_window = $order->pickup->activeOrderWindow();

        $order->update([
            'deadline_date' => $next_order_window->deadlineDatetime()->format('Y-m-d'),
            'pickup_date' => $next_order_window->deliveryDatetime()->format('Y-m-d'),
        ]);

        $updated_subscription = app(SyncSubscriptionDatetimes::class)->handle(
            subscription: $subscription,
            new_delivery_date: $next_order_window->originalDate()->pickup_date->copy()->addDays($subscription->reorder_frequency)
        );

        event(new SubscriptionWasExpedited(
            subscription: $subscription,
            old_delivery_date: $old_delivery_date,
            new_delivery_date: $updated_subscription->ready_at
        ));

        flash('Your subscription has been updated.');
        return back();
    }

    public function updateReorderFrequency(Request $request)
    {
        /** @var RecurringOrder|null $subscription */
        $subscription = auth()->user()->recurringOrder;

        if (is_null($subscription)) {
            error('Subscription not found.');
            return back();
        }

        $validated = $request->validate([
            'reorder_frequency' => ['required', 'integer', Rule::in($subscription->reorderOptions())],
        ]);

        $subscription->reorder_frequency = $validated['reorder_frequency'];

        event(new SubscriptionFrequencyWasUpdated(
            subscription: $subscription,
            old_frequency: $subscription->getOriginal('reorder_frequency'),
            new_frequency: $validated['reorder_frequency']
        ));

        $subscription->save();

        flash('Your subscription frequency has been updated.');

        return back();
    }
}
