<?php

namespace App\Http\Controllers\Theme;

use Illuminate\View\View;
use App\Http\Controllers\Controller;
use App\Models\Post;
use App\Models\Tag;
use App\Services\SettingsService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class BlogController extends Controller
{
    public function index(Request $request): View
    {
        $posts = Post::query()
            ->select(['id', 'user_id', 'slug', 'title', 'summary', 'body', 'cover_photo', 'published_at'])
            ->where('status', 'published')
            ->with(['author'])
            ->when($request->filled('tag'), function ($post_query) use ($request) {
                $post_query->whereHas('tags', fn($tag_query) => $tag_query->where('slug', $request->get('tag')));
            })
            ->when($request->filled('q'), function ($post_query) use ($request) {
                $post_query->where('title', 'LIKE', '%' . $request->get('q') . '%');
            })
            ->orderBy('published_at', 'desc')
            ->orderBy('created_at', 'desc')
            ->paginate(app(SettingsService::class)->blogPostsPerPage());

        $tags = Tag::whereIn('id', DB::table('post_tag')
            ->pluck('tag_id'))
            ->get();

        return view('theme::blog.index')
            ->with(compact('tags', 'posts'));
    }

    public function show(Post $post)
    {
        if ($post->isDraft() && (auth()->user()?->isCustomer() ?? true)) {
            error('The item(s) you are requesting could not be found.');
            return redirect()->route('blog.index');
        }

        $post->load(['author', 'tags']);

        $recent = Post::recent($post)
            ->with(['author', 'tags' => fn($query) => $query->limit(3)])
            ->get(['id', 'user_id', 'slug', 'title', 'summary', 'body', 'cover_photo', 'published_at']);

        return view('theme::blog.show')
            ->with(compact('post', 'recent'));
    }
}
