<?php

namespace App\Http\Controllers\Theme\Subscription;

use App\Http\Controllers\Controller;
use App\Models\RecurringOrder;
use Carbon\Carbon;

class SkipSubscriptionController extends Controller
{
    public function __invoke(RecurringOrder $subscription)
    {
        $validated = request()->validate([
            'week_frequency' => ['required', 'in:1,2,3,4,custom'],
            'custom_date' => ['required_if:week_frequency,custom', 'date', 'date_format:Y-m-d']
        ]);

        if (
            $subscription->nextPickupDate()->copy()
                ->addDays($subscription->reorder_frequency)
                ->gte(now()->addMonths(6))
        ) {
            error('The next delivery date cannot be more than six months from now.');
            return back();
        }

        if ($validated['week_frequency'] === 'custom') {
            $updated_subscription = $subscription->rescheduleTo(Carbon::parse($validated['custom_date']));
        } else {
            $updated_subscription = $subscription->skipDays($validated['week_frequency'] * 7);
        }

        if ($updated_subscription->generate_at < now()) {
            $updated_subscription->generateNextOrder(synchonous: true);
        }

        flash('Your subscription has been updated.');

        return back();
    }
}
