<?php

namespace App\Http\Controllers\Theme\Store;

use App\Events\Product\ProductWasViewed;
use App\Http\Controllers\Controller;
use App\Http\Controllers\Theme\Concerns\FetchesCustomPage;
use App\Repositories\StoreRepository;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\Request;

class ProductController extends Controller
{
    use FetchesCustomPage;

    public function __invoke(Request $request, string $slug)
    {
        try {
            $product = (new StoreRepository($request))
                ->getIndividualProduct($slug)
                ->firstOrFail();

        } catch (ModelNotFoundException $e) {
            error('The product you are looking for could not be found.');
            return back(301);
        }

        if ($user = auth()->user()) {
            ProductWasViewed::dispatch($user, $product);
        }

        return view('theme::store.products.show')
            ->with(compact('product'));
    }
}
