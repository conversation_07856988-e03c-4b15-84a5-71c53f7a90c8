<?php

namespace App\Http\Controllers\Admin\Product;

use App\Http\Controllers\Controller;
use App\Models\Product;
use Illuminate\Http\Request;

class ProductDuplicateController extends Controller
{
    public function __invoke(Request $request, Product $product)
    {
        $validated = $request->validate(['title' => 'required']);

        $duplicated = $product->duplicate($validated['title']);

        return to_route('admin.products.edit', [
            'product' => $duplicated->id,
            'tab' => 'description',
        ]);
    }
}
