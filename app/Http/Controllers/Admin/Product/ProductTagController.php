<?php

namespace App\Http\Controllers\Admin\Product;

use App\Http\Controllers\Controller;
use App\Models\Product;
use App\Models\Tag;
use Cache;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class ProductTagController extends Controller
{
    public function index(int $productId)
    {
        $productTags = Product::findOrFail($productId)->tags()->pluck('id')->toArray();
        $tags = Tag::select(['id', 'title', 'slug'])->get();
        foreach ($tags as $index => $tag) {
            if (in_array($tag->id, $productTags)) {
                $tags[$index]['active'] = true;
            } else {
                $tags[$index]['active'] = false;
            }
        }

        return $tags->toJson();
    }

    public function store(Request $request, int $productId)
    {
        // If tag already exists then return false.
        if (! is_null(Tag::where(['title' => $request->get('title')])->first())) {
            return response()->json(false);
        }

        $tag = Tag::firstOrCreate([
            'title' => $request->get('title'),
            'type' => Tag::type('product'),
        ]);
        $product = Product::findOrFail($productId);
        $product->tags()->attach($tag->id);

        return response()->json([
            'id' => $tag->id,
            'title' => $tag->title,
            'slug' => $tag->slug,
            'active' => true,
        ]);
    }

    public function update(int $productId, int $tagId): JsonResponse
    {
        $product = Product::findOrFail($productId);
        $product->tags()->attach($tagId);
        Cache::tags('tag')->flush();

        return response()->json(['responseText' => 'Tag assigned to product']);
    }

    public function destroy(int $productId, int $tagId): JsonResponse
    {
        $product = Product::findOrFail($productId);
        $product->tags()->detach($tagId);
        Cache::tags('tag')->flush();

        return response()->json(['responseText' => 'Tag has been dissociated from product']);
    }
}
