<?php

namespace App\Http\Controllers\Admin\Product;

use App\Events\InventoryAdjusted;
use App\Exports\ProductsExport;
use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\CreateProductRequest;
use App\Http\Requests\Admin\UpdateProductRequest;
use App\Models\Filter;
use App\Models\Pickup;
use App\Models\Product;
use App\Models\Vendor;
use App\Support\Enums\ProductType;
use Illuminate\Http\Request;

class ProductController extends Controller
{
    public function index(Request $request)
    {
        if ($request->has('field') && ! $request->filled('field')) {
            $request['field'] = 'inventory';
        }

        session(['products-filtered' => $request->all()]);

        $request->mergeIfMissing(['orderBy' => 'title', 'sort' => 'asc']);

        $products = Product::filter($request->all())
            ->join('prices', function ($join) {
                $join->on('products.id', '=', 'prices.product_id')->where('prices.quantity', 1);
            })
            ->with(['vendor', 'defaultPrice'])
            ->where('type_id', '<>', ProductType::GIFT_CARD->value);

        if ($request->get('export')) {
            return (new ProductsExport)->export($products);
        }

        $filters = Filter::where('type', 'product')->get();

        return view('products.index')
            ->with([
                'savedFilters' => $filters,
                'appliedFilters' => $request->appliedFilters(),
                'appliedFilter' => $filters->where('id', $request->get('filter_id'))->first(),
                'products' => $products->paginate(100),
            ]);
    }

    public function store(CreateProductRequest $request)
    {
        // Check to see if there is already a deleted product.
        if ($trashed = Product::onlyTrashed()->where('title', $request['title'])->first()) {
            $trashed->restore();
            error("There was a product named {$trashed['title']} that you had deleted.
            Since you attempted to create a new product with the same name the old product has been restored. You can now edit it to fit your needs.");

            return to_route('admin.products.edit', [
                'product' => $trashed->id,
                'tab' => 'description',
            ]);
        }

        $product = Product::create($request->safe()->all());

        return to_route('admin.products.edit', [
            'product' => $product->id,
            'tab' => 'description',
        ]);
    }

    public function update(UpdateProductRequest $request, int $id)
    {
        $product = Product::findOrFail($id);

        $product->fill($request->except(['protocols', 'pickups', 'vendor_id']));

        match ($product->is_bundle) {
            true => $product->fill([
                'is_bundle' => true,
                'track_inventory' => $product->track_inventory == 'no' ? 'no' : 'bundle',
                'back_order' => false,
            ]),
            false => $product->fill([
                'is_bundle' => false,
                'track_inventory' => $product->track_inventory == 'no' ? 'no' : 'yes',
            ])
        };

        if ($product->track_inventory !== 'yes') {
            $product->inventory = 0;
        }

        if ($request->exists('vendor_id')) {
            $vendor = Vendor::find($request->input('vendor_id'));
            $product->vendor_id = $vendor ? $vendor->id : null;
        }

        if ($product->isDirty('inventory')) {
            event(new InventoryAdjusted(
                $product->id,
                $product->getOriginal('inventory'),
                $product->inventory
            ));
        }

        if ($product->isDirty('fulfillment_id')) {
            $product->schedule_id = Pickup::find($product->fulfillment_id)?->schedule?->id;
        }

        $product->save();

        if ($request->filled('protocols')) {
            $payload = is_array($request->get('protocols')) ? $request->get('protocols') : [];
            $product->protocols()->sync($payload);
        }

        if ($request->filled('pickups')) {
            $payload = is_array($request->get('pickups')) ? $request->get('pickups') : [];
            $product->pickups()->sync($payload);
        }

        if ($request->ajax()) {
            return response()->json('Success');
        }

        return back();
    }

    public function show(Request $request, Product $product)
    {
        return to_route('admin.products.edit', $product);
    }

    public function edit(Request $request, int $id)
    {
        $product = Product::with('bundle', 'protocols', 'pickups')->findOrFail($id);

        if ($product->isGiftCard()) {
            return to_route('admin.gift-cards.edit', $product);
        }

        return view('products.edit')
            ->with(compact('product'));
    }

    public function destroy(int $id)
    {
        Product::findOrFail($id)->delete();

        return to_route('admin.products.index');
    }
}
