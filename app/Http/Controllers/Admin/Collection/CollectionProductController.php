<?php

namespace App\Http\Controllers\Admin\Collection;

use App\Http\Controllers\Controller;
use App\Models\Collection;
use App\Models\Product;
use Cache;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class CollectionProductController extends Controller
{
    public function index(Collection $collection)
    {
        $orderBy = 'title';
        $sort = 'asc';

        if ($collection->order) {
            /** @var array<int, string> $ordering */
            $ordering = explode('-', $collection->order);

            $orderBy = $ordering[0] ?? 'title';
            $sort = $ordering[1] ?? 'asc';
        }

        $products = Product::selectRaw('products.id, title, collection_product.sort_order as custom_sort, visible')
            ->join('collection_product', 'collection_product.product_id', '=', 'products.id')
            ->where('collection_id', $collection->id)
            ->orderBy($orderBy, $sort)
            ->get();

        $collection['products'] = $products;

        return $collection;
    }

    public function store(Request $request, Collection $collection): JsonResponse
    {
        $validated = $request->validate([
            'id' => ['required', 'exists:products,id'],
            'sort' => ['nullable', 'int'],
        ]);

        $sort_order = $validated['sort'] ?? 0;

        $collection->products()->where('products.id', $validated['id'])->exists()
            ? $collection->products()->updateExistingPivot($validated['id'], compact('sort_order'))
            : $collection->products()->attach($validated['id'], compact('sort_order'));

        Cache::tags(['store', 'collection', 'product'])->flush();

        return response()->json('Product added to collection.');
    }

    public function update(Request $request, int $collectionId): JsonResponse
    {
        $validated = $request->validate([
            'sort' => ['nullable', 'array'],
        ]);

        foreach ($validated['sort'] ?? [] as $product_id => $sort_order) {
            DB::table('collection_product')
                ->where(['collection_id' => $collectionId, 'product_id' => $product_id])
                ->update(compact('sort_order'));
        }

        Cache::tags(['store', 'collection', 'product'])->flush();

        return response()->json('Products have been sorted.');
    }

    public function destroy(Collection $collection, int $productId): JsonResponse
    {
        $collection->products()->detach($productId);

        Cache::tags(['store', 'collection', 'product'])->flush();

        return response()->json('Product removed from collection.');
    }
}
