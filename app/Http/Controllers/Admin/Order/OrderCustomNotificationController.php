<?php

namespace App\Http\Controllers\Admin\Order;

use App\Http\Controllers\Controller;
use App\Mail\CustomMessage;
use App\Models\Order;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\RateLimiter;

class OrderCustomNotificationController extends Controller
{
    public function store(Request $request, Order $order): JsonResponse
    {
        $validated = $request->validate([
            'subject' => ['required'],
            'message' => ['required'],
        ]);

        if (is_null($order->customer)) {
            return response()->json("The notification cannot be sent because order's customer has been deleted.", 409);
        }

        $user_id = auth()->id();

        $executed = RateLimiter::attempt(
            "send-order-email:{$user_id}:{$order->id}",
            $perMinute = 2,
            function () use ($order, $validated) {
                Mail::to($order->customer_email)
                    ->send(new CustomMessage($order->id, [
                        'subject' => $validated['subject'],
                        'content' => $validated['message'],
                        'template_id' => 'customMessage',
                    ]));
            }
        );

        if (! $executed) {
            return response()->json("Too many emails have been sent to {$order->getCustomerName()}.", 429);
        }

        return response()->json(["Your custom notification has been sent to {$order->getCustomerName()}."]);
    }
}
