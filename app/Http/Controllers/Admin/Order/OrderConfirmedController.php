<?php

namespace App\Http\Controllers\Admin\Order;

use App\Events\Order\OrderWasConfirmed;
use App\Exceptions\ExclusivityException;
use App\Http\Controllers\Controller;
use App\Models\Order;
use App\Support\Enums\Channel;
use Illuminate\Validation\Rule;

class OrderConfirmedController extends Controller
{
    public function store(Order $order)
    {
        $validated = request()->validate([
            'customer_first_name' => ['required', 'string'],
            'customer_last_name' => ['required', 'string'],
            'customer_phone' => ['required', 'string'],
            'customer_email' => ['required', 'email'],
            'shipping_street' => ['nullable', 'string'],
            'shipping_street_2' => ['nullable', 'string'],
            'shipping_city' => ['nullable', 'string'],
            'shipping_state' => ['nullable', 'string'],
            'shipping_zip' => ['nullable', 'string'],
            'country' => ['nullable', 'string'],
            'save_for_later' => ['in:on,off'],
            'payment_id' => ['required', 'integer', 'exists:payments,id'],
            'type_id' => ['required', 'integer', Rule::in(Channel::ids())],
            'send_confirmation_email' => ['in:on,off'],
            'confirm_below_minimum' => ['in:on,off'],
        ]);

        if ($order->isConfirmed()) {
            error('The order has already been confirmed.');

            return back();
        }

        $order->load('items.product', 'fees', 'pickup', 'customer', 'paymentMethod');

        if (is_null($order->pickup)) {
            error('The order can not be confirmed, it must have an active location assigned to it.');

            return back();
        }

        if (! $order->meetsOrderMinimum() && ! request()->has('confirm_below_minimum')) {
            error('The order subtotal must be at least $'.money($order->getOrderMinimum()).' before it can be confirmed.');

            return back();
        }

        try {
            $order->pickup->checkForExcludedProducts($order->items);
        } catch (ExclusivityException $exception) {
            error('These products are not available at '.$exception->getPickup()->title.': '.collect($exception->getExcludedProducts())->pluck('title')->implode(','));

            return back();
        }

        $unavailable = $order->cartItemsWithoutAvailableInventory();

        if ($unavailable->isNotEmpty()) {
            error($unavailable->first()['item']->product->getOutOfStockMessage());

            return back();
        }

        $pickupDate = $order->pickup_date;
        $deadlineDate = $order->deadline_date;

        $order->confirm($validated);

        // Assign the original dates.
        // TODO: find a way where we dont have to override here
        $order->pickup_date = $pickupDate;
        $order->deadline_date = $deadlineDate;

        $order->save();

        if (request('save_for_later') === 'on') {
            $this->updateCustomer($order);
        }

        if (request('send_confirmation_email') === 'on') {
            event(new OrderWasConfirmed($order));
        }

        flash('The order has been successfully confirmed.');

        return back();
    }

    protected function updateCustomer(Order $order)
    {
        $customer = $order->customer;

        $customer->first_name = request('customer_first_name');
        $customer->last_name = request('customer_last_name');
        $customer->phone = request('customer_phone');
        $customer->street = request()->filled('shipping_street') ? request('shipping_street') : $customer->street;
        $customer->street_2 = request()->filled('shipping_street_2') ? request('shipping_street_2') : $customer->street_2;
        $customer->city = request()->filled('shipping_city') ? request('shipping_city') : $customer->city;
        $customer->state = request()->filled('shipping_state') ? request('shipping_state') : $customer->state;
        $customer->zip = request()->filled('shipping_zip') ? request('shipping_zip') : $customer->zip;
        $customer->country = request()->filled('country') ? request('country') : $customer->country;

        $customer->save();

        return $customer;
    }
}
