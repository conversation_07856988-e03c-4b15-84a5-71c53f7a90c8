<?php

namespace App\Http\Controllers\Admin\Order;

use App\Http\Controllers\Controller;
use App\Mail\CustomMessage;
use App\Models\Order;
use Bugsnag\BugsnagLaravel\Facades\Bugsnag;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Support\Str;

class OrderBulkMessageController extends Controller
{
    protected int $count = 0;

    public function update(Request $request)
    {
        $request->validate([
            'orders' => ['required'],
            'subject' => ['required_if:template_id,customMessage'],
            'body' => ['required_if:template_id,customMessage'],
        ], [
            'orders.required' => 'Select some orders to update first.',
            'subject.required_if' => 'The subject field is required.',
            'body.required_if' => 'The body field is required.',
        ]);

        if (RateLimiter::tooManyAttempts('send-bulk-message:'.auth()->id(), $perMinute = 5)) {
            error('Too many bulk message requests have been made. Try again shortly.');

            return back();
        }

        Order::query()
            ->whereHas('customer')
            ->whereIn('id', $request->input('orders'))
            ->where('customer_email', '!=', '')
            ->chunk(300, function ($orders) use ($request) {
                $this->count += $orders->count();

                foreach ($orders as $order) {
                    try {
                        Mail::to($order->customer_email)
                            ->send(new CustomMessage($order->id, [
                                'subject' => $request->get('subject') ?? '',
                                'content' => $request->get('body') ?? '',
                                'template_id' => $request->get('template_id'),
                            ]));
                    } catch (Exception $exception) {
                        Bugsnag::notifyException($exception);
                    }
                }
            });

        flash('Notifications sent to '.$this->count.' '.Str::plural('customer', $this->count));

        return back();
    }
}
