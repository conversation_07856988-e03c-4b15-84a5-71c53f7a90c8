<?php

namespace App\Http\Controllers\Admin\Order;

use App\Actions\Subscription\SyncSubscriptionDatetimes;
use App\Http\Controllers\Controller;
use App\Jobs\CancelOrders;
use App\Models\Order;
use Illuminate\Http\Request;

class BulkCancelOrderController extends Controller
{
    public function __invoke(Request $request)
    {
        $validated = request()->validate($this->rules(), $this->messages());

        $orderIds = Order::whereIn('id', $validated['orders'])
            ->pluck('id')
            ->toArray();

        CancelOrders::dispatch($orderIds);

        flash(count($orderIds) . " orders are now in the process of being canceled.");

        return to_route('admin.orders.index');
    }

    private function rules(): array
    {
        return [
            'orders' => ['bail', 'required', 'array', function ($attribute, $value, $fail) {
                $canceled_order_ids = Order::query()
                    ->whereNotNull('canceled_at')
                    ->whereIn('id', $value)
                    ->pluck('id');

                if ($canceled_order_ids->isNotEmpty()) {
                    $fail("Orders have already been canceled: {$canceled_order_ids->implode(', ')}");
                }
            }],
        ];
    }

    private function messages()
    {
        return [
            'orders.required' => 'Please select at least one order.',
        ];
    }
}
