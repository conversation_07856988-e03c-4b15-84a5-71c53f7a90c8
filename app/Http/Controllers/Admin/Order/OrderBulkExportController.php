<?php

namespace App\Http\Controllers\Admin\Order;

use App\Exports\Orders\LocationManifestExport;
use App\Exports\Orders\NowCourierExport;
use App\Exports\QuickBooks\InvoiceExport;
use App\Exports\QuickBooks\SalesReceiptExport;
use App\Exports\TransactionProExport;
use App\Http\Controllers\Controller;
use App\Models\Order;
use App\Repositories\OrderIndexRepository;
use Maatwebsite\Excel\Excel;

class OrderBulkExportController extends Controller
{
    public function update(string $type)
    {
        $validated = request()->validate([
            'type' => ['nullable', 'in:all,page'],
            'orders' => ['required_unless:type,all', 'array'],
        ], [
            'orders.required_unless' => 'At least one order must be selected.',
        ]);

        return match ($type) {
            'sales_receipt' => (new SalesReceiptExport($this->orderQuery($validated)))
                ->download('sales_receipts_for_quickbooks_'.now()->format('m_d_y').'.csv', Excel::CSV),
            'invoice' => (new InvoiceExport($this->orderQuery($validated)))
                ->download('invoices_for_quickbooks_'.now()->format('m_d_y').'.csv', Excel::CSV, ['Content-Type' => 'text/csv']),
            'manifest' => (new LocationManifestExport($this->orderQuery($validated)))->export(),
            'now_courier' => (new NowCourierExport($this->orderQuery($validated)))->export(),
            'tap' => (new TransactionProExport($this->orderQuery($validated)))
                ->download('orders.csv', Excel::CSV),
            default => redirect()->back()
        };
    }

    private function orderQuery(array $params)
    {
        request()
            ->replace(session('orders-filtered') ?? [])
            ->mergeIfMissing([
                'confirmed' => true,
                'orderBy' => 'orders.confirmed_date',
                'sort' => 'desc',
            ]);

        return match ($params['type']) {
            'all' => (new OrderIndexRepository)->query(collect(request()->all())),
            default => Order::whereIn('id', $params['orders']),
        };
    }
}
