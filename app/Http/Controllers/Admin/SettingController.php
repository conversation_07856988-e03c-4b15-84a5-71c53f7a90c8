<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\UpdateSettingsRequest;
use App\Models\Setting;
use Illuminate\Support\Facades\Cache;
use Illuminate\View\View;

class SettingController extends Controller
{
    public function index(): View
    {
        return view('settings.index');
    }

    public function edit(string $setting): View
    {
        if (! view()->exists('settings.'.$setting)) {
            abort(404);
        }

        return view('settings.'.$setting);
    }

    public function update(UpdateSettingsRequest $request)
    {
        $settings = $request->get('settings');

        if ($request->filled('terms')) {
            $settings['terms_of_service'] = $request->get('terms');
        }

        if (array_key_exists('map_center_latitude', $settings)) {
            if (! $settings['map_center_latitude'] || ! $settings['map_center_longitude']) {
                $settings['map_center'] = null;
            } else {
                $settings['map_center'] = trim($settings['map_center_latitude']).', '.trim($settings['map_center_longitude']);
            }
        }

        foreach ($settings as $key => $value) {
            Setting::updateOrCreate(compact('key'), compact('value'));

            if ($key == 'store_products_per_page') {
                Cache::tags('store')->flush();
            }

            if ($key == 'farm_name') {
                Cache::tags('widgets')->flush();
            }
        }

        Cache::tags('setting')->flush();

        if ($request->ajax()) {
            return response()->json('Settings updated.');
        }

        return back();
    }
}
