<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Media;
use App\Models\Tag;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;

class PhotoTagController extends Controller
{
    public function index(int $id)
    {
        $mediaTags = Media::findOrFail($id)->tags()->pluck('id')->toArray();
        $tags = Tag::select(['id', 'title', 'slug'])->get();
        foreach ($tags as $index => $tag) {
            if (in_array($tag->id, $mediaTags)) {
                $tags[$index]['active'] = true;
            } else {
                $tags[$index]['active'] = false;
            }
        }

        return $tags->toJson();
    }

    public function store(Request $request, int $mediaId)
    {
        // If tag already exists then return false.
        if (! is_null(Tag::where(['title' => $request->get('title')])->first())) {
            return response()->json(false);
        }

        $tag = Tag::firstOrCreate([
            'title' => $request->get('title'),
            'type' => Tag::type('media'),
        ]);
        $media = Media::findOrFail($mediaId);
        $media->tags()->attach($tag->id);

        return response()->json([
            'id' => $tag->id,
            'title' => $tag->title,
            'slug' => $tag->slug,
            'active' => true,
        ]);
    }

    public function update(int $mediaId, int $tagId): JsonResponse
    {
        $product = Media::findOrFail($mediaId);
        $product->tags()->attach($tagId);
        Cache::tags('tag')->flush();

        return response()->json(['responseText' => 'Tag assigned to media.']);
    }

    public function destroy(int $mediaId, int $tagId): JsonResponse
    {
        $product = Media::findOrFail($mediaId);
        $product->tags()->detach($tagId);
        Cache::tags('tag')->flush();

        return response()->json(['responseText' => 'Tag has been dissociated from media.']);
    }
}
