<?php

namespace App\Http\Controllers\Admin\Recipe;

use App\Http\Controllers\Controller;
use App\Models\Recipe;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class RecipePhotoController extends Controller
{
    public function update(Request $request, int $recipeId): JsonResponse
    {
        $recipe = Recipe::findOrFail($recipeId);
        $recipe->cover_photo = $request->get('cover_photo');
        $recipe->save();

        return response()->json([
            'responseText' => 'Cover photo set.',
        ]);
    }
}
