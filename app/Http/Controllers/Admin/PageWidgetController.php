<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\Widget\CreateWidgetRequest;
use App\Http\Requests\Admin\Widget\UpdateWidgetRequest;
use App\Models\Page;
use App\Models\Widget;
use Illuminate\Http\JsonResponse;

class PageWidgetController extends Controller
{
    public function index(Page $page)
    {
        return $page->widgets()->orderBy('sort')->get();
    }

    public function store(CreateWidgetRequest $request, Page $page): JsonResponse
    {
        $page->widgets()->create($request->all());

        return response()->json(['id' => null]);
    }

    public function update(UpdateWidgetRequest $request, Page $page, Widget $widget): JsonResponse
    {
        $settings = (array) $widget->settings;

        foreach ($request->get('settings', []) as $key => $setting) {
            $settings[$key] = $setting;
        }

        $request['settings'] = $settings;

        $widget->update($request->all());

        return response()->json('Widget updated');
    }

    public function destroy(Page $page, Widget $widget): JsonResponse
    {
        $widget->delete();

        return response()->json('Widget deleted');
    }
}
