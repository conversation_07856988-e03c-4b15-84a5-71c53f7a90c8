<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Message;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\View\View;

class MessageController extends Controller
{
    public function index(): View
    {
        return view('messages.index');
    }

    public function update(Request $request): RedirectResponse
    {
        foreach ($request->get('messages') as $messageId => $messageContent) {
            Message::updateOrCreate(
                ['id' => $messageId],
                ['content' => $messageContent]
            );
        }

        Cache::tags('message')->flush();

        return back();
    }
}
