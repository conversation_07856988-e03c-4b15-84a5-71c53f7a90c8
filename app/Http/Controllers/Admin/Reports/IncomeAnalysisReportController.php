<?php

namespace App\Http\Controllers\Admin\Reports;

use App\Exports\IncomeAnalysisExport;
use App\Http\Controllers\Controller;
use App\Models\Filter;
use App\Repositories\Reports\IncomeAnalysisReport;
use App\Support\Enums\OrderStatus;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Maatwebsite\Excel\Excel;

class IncomeAnalysisReportController extends Controller
{
    public function index(Request $request)
    {
        $unfiltered = empty($request->all());

        $request->mergeIfMissing([
            'date_type' => 'payment_date',
            'date_range.start' => $request->get('date_range')['start'] ?? today()->subMonth()->format('M jS Y'),
            'date_range.end' => $request->get('date_range')['end'] ?? today()->format('M jS Y'),
        ]);

        if ($unfiltered) {
            $request->merge([
                'paid' => 1,
                'order_status' => OrderStatus::ids([OrderStatus::canceled()])->toArray(),
            ]);
        }

        $request->validate([
            'date_range' => ['required'],
            'date_range.start' => ['required', 'date'],
            'date_range.end' => ['required', 'date', 'after_or_equal:date_range.start', function ($attribute, $value, $fail) use ($request) {
                if (Carbon::parse($value)->diffInDays(Carbon::parse($request->get('date_range')['start'])) > 31) {
                    $fail('The date range cannot be more than 31 days.');
                }
            }],
        ]);

        $is_export = $request->has('export');

        [$line_item_revenue, $fee_revenue, $deductions] = (new IncomeAnalysisReport)->handle($request->all(), $is_export);

        if ($is_export) {
            return (new IncomeAnalysisExport($line_item_revenue, $fee_revenue, $deductions))
                ->download('income_analysis.csv', Excel::CSV);
        }

        $filters = Filter::where('type', 'income_analysis_report')->get();

        return view('reports.income-analysis.index')
            ->with([
                'savedFilters' => $filters,
                'appliedFilters' => $request->appliedFilters(),
                'appliedFilter' => $filters->where('id', $request->get('filter_id'))->first(),
                'line_item_revenue' => $line_item_revenue,
                'fee_revenue' => $fee_revenue,
                'deductions' => $deductions,
            ]);
    }
}
