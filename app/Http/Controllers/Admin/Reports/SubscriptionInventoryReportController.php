<?php

namespace App\Http\Controllers\Admin\Reports;

use App\Exports\SubscriptionInventoryExport;
use App\Models\Collection;
use App\Models\Filter;
use App\Models\Tag;
use App\Models\Vendor;
use App\Repositories\Reports\SubscriptionInventoryReport;
use Illuminate\Validation\Rule;
use Maatwebsite\Excel\Excel;

class SubscriptionInventoryReportController
{
    public function __invoke()
    {
        request()->mergeIfMissing([
            'date_type' => 'generate_at',
            'date_range.start' => request()->get('date_range')['start'] ?? today()->format('M jS Y'),
            'date_range.end' => request()->get('date_range')['end'] ?? today()->addDays(30)->format('M jS Y'),
        ]);


        $validated = request()->validate([
            'date_type' => ['nullable', 'in:generate_at,ready_at'],
            'date_range' => ['nullable', 'array'],
            'date_range.start' => ['nullable', 'date', 'after_or_equal:today'],
            'date_range.end' => ['nullable', 'date', 'after_or_equal:date_range.start'],
            'delivery_methods' => ['nullable', 'array'],
            'vendor_id' => ['nullable', Rule::exists(Vendor::class, 'id')],
            'sku' => ['nullable', 'string', 'max:100'],
            'collection_id' => ['nullable', Rule::exists(Collection::class, 'id')],
            'order_tags' => ['nullable', Rule::exists(Tag::class, 'id')],
        ]);

        $report = app(SubscriptionInventoryReport::class)->handle($validated);

        if (request()->has('export')) {
            return (new SubscriptionInventoryExport($report))
                ->download('subscription_inventory_report.csv', Excel::CSV);
        }

        $filters = Filter::where('type', 'subscription_inventory_report')->get();

        return view('reports.subscription-inventory.index')
            ->with([
                'savedFilters' => $filters,
                'appliedFilters' => request()->appliedFilters(),
                'appliedFilter' => $filters->firstWhere('id', request()->get('filter_id')),
                'report' => $report,
            ]);
    }
}
