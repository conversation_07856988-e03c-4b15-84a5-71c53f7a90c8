<?php

namespace App\Http\Controllers\Admin\Reports;

use App\Exports\ProductSalesExport;
use App\Http\Controllers\Controller;
use App\Models\Filter;
use App\Repositories\Reports\ProductSales;
use Illuminate\Http\Request;

class ProductSalesReportController extends Controller
{
    public function index(Request $request)
    {
        $query = (new ProductSales)->query($request);

        if ($request->has('export')) {
            return (new ProductSalesExport)->export($query);
        }

        $filters = Filter::where('type', 'product_sales_report')->get();

        return view('reports.product-sales.index')
            ->with([
                'savedFilters' => $filters,
                'appliedFilters' => $request->appliedFilters(),
                'appliedFilter' => $filters->where('id', $request->get('filter_id'))->first(),
                'results' => $query->get(),
            ]);
    }
}
