<?php

namespace App\Http\Requests\Admin;

use App\Http\Requests\Request;
use App\Models\Pickup;
use App\Models\Schedule;
use App\Rules\CheckForExistingActiveRecurringOrders;
use Illuminate\Validation\Rule;

class UpdatePickupRequest extends Request
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        /** @var Pickup $pickup */
        $pickup = $this->route()->parameter('pickup');

        return [
            // Description
            'title' => ['sometimes', 'required', 'string', 'max:255'],
            'display_name' => ['nullable', 'string', 'max:255'],
            'contact_name' => ['nullable', 'string', 'max:255'],
            'contact_phone' => ['nullable', 'string', 'max:255'],
            'contact_email' => ['nullable', 'string', 'max:255', 'email'],
            'contact_website' => ['nullable', 'string', 'max:255', 'url'],
            'page_heading' => ['nullable', 'string', 'max:255'],
            'description' => ['nullable', 'string'],

            // Address
            'street' => ['nullable', 'string', 'max:255'],
            'street_2' => ['nullable', 'string', 'max:255'],
            'city' => ['sometimes', 'required', 'string', 'max:255'],
            'state' => ['sometimes', 'required', 'string', 'max:2'],
            'zip' => ['nullable', 'string', 'max:10'],
            'country' => ['in:USA,Canada'],

            // Fees
            'settings' => ['array'],
            'settings.delivery_fee_type' => ['in:1,2'],
            'delivery_rate' => ['numeric', 'min:0'],
            'tax_delivery_fee' => ['boolean'],
            'apply_limit' => ['boolean'], // cap delivery fee
            'delivery_total_threshold' => ['numeric', 'min:0'],
            'delivery_fee_cap' => ['numeric', 'min:0'],
            'display_cart_shipping_calculator' => ['boolean'],

            // Settings
            'status_id' => [
                'sometimes',
                Rule::in([1,3,4]),
                new CheckForExistingActiveRecurringOrders($pickup),
            ],
            'visible' => ['boolean'],
            'tax_rate' => ['numeric', 'min:0', 'max:100'],
            'min_customer_orders' => ['numeric', 'min:0'],
            // TODO: restructure how form gets submitted so validation can be performed on each id in payment_methods array
            'payment_methods' => ['array'],
            'lat' => ['numeric', 'min:-90', 'max:90'],
            'lng' => ['numeric', 'min:-180', 'max:180'],
            'slug' => [Rule::unique('pickups')->ignore($pickup->slug, 'slug')],
            'settings.sales_channel' => ['integer'],
            'pricing_group_id' => ['nullable', 'exists:product_price_groups,id'],
            'settings.show_map' => ['boolean'],
            'settings.email_order_confirmation_template' => ['nullable', 'exists:templates,id'],
            'settings.email_order_packed_template' => ['nullable', 'exists:templates,id'],
            'settings.process_order_email' => ['nullable', 'exists:templates,id'],
            'settings.sms_subscription_reorder_template' => ['nullable', 'exists:templates,id'],
            'settings.recurring_orders_welcome_email_template_id' => ['nullable', 'exists:templates,id'],
            'settings.recurring_orders_reorder_email_template_id' => ['nullable', 'exists:templates,id'],

            // Display Messages
            'subtitle' => ['nullable', 'string', 'max:255'],
            'settings.checkout_notes' => ['nullable', 'string'],
            'settings.order_date_tba' => ['nullable', 'string'],
            'settings.next_date_heading' => ['nullable', 'string'],
            'settings.times_heading' => ['nullable', 'string'],
            'settings.schedule_table_heading_1' => ['nullable', 'string'],
            'settings.schedule_table_heading_2' => ['nullable', 'string'],
            'settings.schedule_table_heading_3' => ['nullable', 'string'],
            'settings.fees_heading' => ['nullable', 'string'],
            'settings.delivery_fee_title' => ['nullable', 'string'],

            // SEO
            'page_title' => ['nullable', 'string', 'max:255'],
            'page_description' => ['nullable', 'string', 'max:255'],

            // Other
            'pickup_times' => ['nullable', 'string'],
            'schedule_id' => ['sometimes',
                function ($attribute, $value, $fail) {
                    /** @var Pickup|null $pickup */
                    $pickup = request()->route('pickup');

                    if ($pickup?->subscriptions()->exists() ?? false) {
                        if (is_null($value)) {
                            $fail('The schedule cannot be removed from pickup locations with active subscriptions.');
                        } else if (Schedule::where(['id' => $value, 'type_id' => Schedule::TYPE_CUSTOM])->exists()) {
                            $fail('Custom schedules cannot be assigned to pickup locations with active subscriptions.');
                        }
                    }

                    if ( ! is_null($value) && Schedule::where('id', $value)->doesntExist()) {
                        $fail('The selected schedule id is invalid.');
                    }
                }
            ]
        ];
    }

    public function messages(): array
    {
        return [
            'title.required' => 'A location name is required.',
            'delivery_rate.numeric' => 'The Delivery Fee price field must be a number.',
            'delivery_rate.min' => 'The Delivery Fee price field cannot be negative.',
            'delivery_fee_cap.numeric' => 'The Capped Delivery Fee Total field must be a number.',
            'delivery_fee_cap.min' => 'The Capped Delivery Fee Total field cannot be negative.',
            'delivery_total_threshold.numeric' => 'The Cap Threshold field must be a number.',
            'delivery_total_threshold.min' => 'The Cap Threshold field cannot be negative.',
            'slug.unique' => 'The URL slug field is already being used.',
            'apply_limit.boolean' => 'The cap delivery fee field must be true or false.',
        ];
    }

    protected function prepareForValidation(): void
    {
        if ($this->has('tax_rate') && is_numeric($this->tax_rate)) {
            $this->merge(['tax_rate' => $this->tax_rate / 100]);
        }

        if ($this->filled(['state'])) {
            $this->merge(['country' => getCountryFromState($this->state)]);
        }
    }
}
