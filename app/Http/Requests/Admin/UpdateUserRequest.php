<?php

namespace App\Http\Requests\Admin;

use App\Http\Requests\Request;

class UpdateUserRequest extends Request
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Sanitize input before validation.
     */
    public function sanitizeInput(): array
    {
        if (isset($this['credit'])) {
            $this['credit'] = str_replace(',', '', $this['credit']);
        }
        return $this->all();
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'first_name' => [
                'sometimes',
                'required',
            ],
            'last_name' => [
                'sometimes',
                'required',
            ],
            'email' => [
                'sometimes',
                'required',
                'email:filter',
                'unique:users,email,' . $this->route('user'),
            ],
            'email_alt' => [
                'nullable',
                'email:filter',
            ],
            'credit' => [
                'numeric',
            ],
            'recurring_order_settings.promo_item' => ['integer', 'nullable']
        ];
    }

    public function messages(): array
    {
        return [
            'email_alt.email' => 'Alternative email must be a valid email address'
        ];
    }
}
