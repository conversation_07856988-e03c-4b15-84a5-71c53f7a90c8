<?php

namespace App\Http\Requests\Admin;

use App\Http\Requests\Request;

class CreateLocationRequest extends Request
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'title' => ['required',],
            'street' => ['required',],
            'city' => ['required',],
            'state' => ['required',],
            'zip' => ['max:10',],
            'type' => ['required',],
        ];
    }

    public function messages(): array
    {
        return [
            'title.required' => 'A location name is required',
            'street.required' => 'Street is required',
            'city.required' => 'City is required',
            'state.required' => 'State is required',
            'zip.required' => 'Zip is required',
            'type.required' => 'Location type is required',
        ];
    }
}
