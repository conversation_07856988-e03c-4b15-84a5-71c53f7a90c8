<?php

namespace App\Http\Requests\API;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\DB;

class CreateDeliveryZoneRequest extends FormRequest
{
    public $zips = [];
    public $existingZips = [];

    public function getValidatorInstance()
    {
        $validator = parent::getValidatorInstance();

        if ($this->filled('zips')) {
            $this->checkForExistingZips();
            if (count($this->existingZips)) {
                $validator->after(function () use ($validator) {
                    $message = 'These zips are already used in another zone:<br/>' . implode(',', $this->existingZips);
                    $validator->errors()->add('existing_zips', $message);
                });
            }
        }

        return $validator;
    }

    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'zone.title' => [
                'required',
            ],
            $this->get('zoneType') => 'required'
        ];
    }

    public function messages(): array
    {
        return [
            'zone.title.required' => 'A zone name is required',
            'zips.required' => 'Enter at least 1 Zip/Postal code',
            'states.required' => 'Select at least 1 State/Province'
        ];
    }

    public function checkForExistingZips(): self
    {
        // Break zips into chunks to prevent huge whereIn queries.
        $this->zips = array_map(function ($zip) {
            return trim($zip);
        }, explode(',', $this->get('zips')));

        $this->existingZips = array_merge(DB::table('pickup_zips')
            ->whereIn('zip', $this->zips)
            ->pluck('zip')
            ->toArray(), $this->existingZips);

        return $this;
    }
}
