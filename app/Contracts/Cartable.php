<?php

namespace App\Contracts;

use App\Cart\Cart;
use App\Cart\Item;
use App\Cart\Subscription;
use App\Exceptions\ExclusivityException;
use App\Models\Card;
use App\Models\Coupon;
use App\Models\Date;
use App\Models\Order;
use App\Models\Payment;
use App\Models\Pickup;
use App\Models\Product;
use App\Models\ProductPriceGroup;
use App\Models\User;
use Exception;
use Illuminate\Support\Collection;

interface Cartable
{
    public const ONE_TIME_PURCHASE = 1;
    public const SUBSCRIPTION_PURCHASE = 2;

    public function cartId();

    public function purchaseType(): int;

    public function cartIsEmpty(): bool;

    public function cartCustomer(): ?User;

    public function cartLocation(): ?Pickup;

    public function cartPricingGroup(): ?ProductPriceGroup;

    public function cartPricingGroupId(): ?int;

    public function cartDate(): ?Date;

    /** @return Collection<Item> */
    public function itemsInCart(): Collection;

    public function cartProductIds(): Collection;

    public function cartSubscription(): ?Subscription;

    public function addItemToCart(Item $item): Cartable;

    public function updateCartItemQuantity($id, int $quantity): Cartable;

    public function incrementCartItemQuantity($id): Cartable;

    public function decrementCartItemQuantity($id): Cartable;

    public function removeCartItem($id): Cartable;

    /**
     * @throws ExclusivityException
     */
    public function updateCartLocation(Pickup $location, bool $remove_excluded_items = false): Cartable;

    public function updateCartDate(?Date $date): Cartable;

    public function setCartAsOneTimePurchase(): Cartable;

    public function setCartAsSubscriptionPurchase(int $frequency = null, int $product_incentive_id = null): Cartable;

    public function isRecurring(): bool;

    public function applyCouponToCart(Coupon $coupon): Cartable;

    public function removeCouponFromCart(string $code): Cartable;

    public function cartSubtotal(): int;

    public function cartWeight(): float;

    public function cartCouponTotal(): int;

    public function cartStoreCreditTotal(): int;

    public function cartLocationFeeTotal(): int;

    public function cartPotentialSubscriptionSavingsTotal(): int;

    public function cartSubscriptionSavingsTotal(): int;

    public function cartSubscriptionProductIncentive(): ?Product;

    public function cartPotentialSubscriptionProductValue(): int;

    public function cartTaxTotal(): int;

    public function cartDeliveryTotal(): int;

    public function cartTotalBeforeStoreCredit(): int;

    public function cartTotal(): int;

    public function cartIsEligibleForSubscription(): bool;

    // NEW UNDER HERE
    public function addProduct(Product $product, int $quantity = 1): Item;

    public function meetsOrderMinimum(): bool;

    public function oneTimeItems(): Collection;

    public function subscriptionItems(): Collection;

    public function getFreeItemSavings(): int;

    public function cartCoupons(): Collection;

    public function toCartArray(): array;

    public function cartProductsUnderRequiredOrderMinimum(): Collection;

    public function cartItemsWithoutAvailableInventory(): Collection;

    public function hasValidSubscription(): bool;

    public function hasContactInfo(): bool;

    public function hasSubscriptionEligibleItems(): bool;

    public function getContactInfo(): array;

    public function setContactInfo(array $contact_info): Cartable;

    public function hasShippingInfo(): bool;

    public function getShippingInfo(): array;

    public function setShippingInfo(array $shipping_info): Cartable;

    public function hasSelectedBillingMethod(): bool;

    public function getBillingInfo(): array;

    public function setBillingInfo(array $billing_info): Cartable;

    public function getCustomerNotes(): ?string;

    public function cartBillingMethod(): ?Payment;

    public function cartBillingSource(): ?Card;

    public function cartSubscriptionProductIncentiveItem(): ?Item;

    // legacy checkout
    public function confirm(array $params): Order;

    public function confirmWithCart(Cart $cart): Order;

    public function belongsInDeliveryZone(string $zip = null, string $state = null): bool;

    public function buildPickupDateSelect(Collection $window_collection, ?string $selected_date = null): string;

    public function removeOutOfStockItems(bool $shouldUpdateQuantity = false): self;

    public function quantitiesByProductId(): Collection;
}
