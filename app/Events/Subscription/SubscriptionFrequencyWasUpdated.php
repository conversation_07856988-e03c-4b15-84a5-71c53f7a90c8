<?php

namespace App\Events\Subscription;

use App\Models\RecurringOrder;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class SubscriptionFrequencyWasUpdated
{
    use Dispatchable, InteractsWithSockets, SerializesModels;
    public function __construct(
        public RecurringOrder $subscription,
        public int $old_frequency,
        public int $new_frequency
    ) {}
}
