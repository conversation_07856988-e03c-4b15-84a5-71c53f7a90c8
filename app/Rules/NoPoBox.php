<?php

namespace App\Rules;

use App\Services\SettingsService;
use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class NoPoBox implements ValidationRule
{
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        if (
            ! app(SettingsService::class)->allowsPOBoxShipping()
                && str($value)
                    ->replace([' ', '.', ','], '')
                    ->lower()
                    ->contains('pobox')
        ) {
            $fail('Shipping to PO Box addresses is not allowed.');
        }
    }
}
