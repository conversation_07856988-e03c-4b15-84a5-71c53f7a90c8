<?php

namespace App\Mail;

use App\Models\Order;
use App\Models\Template;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\SerializesModels;
use Symfony\Component\Mime\Email;

class OrderProcessed extends TenantAwareMailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    public $order_id;
    public $from;
    public $subject;
    public $content;
    public $text;
    public $styles;
    public $metadata;

    public function __construct(int $order_id)
    {
        $this->order_id = $order_id;
    }

    public function build()
    {
        $order = Order::forEmail()->findOrFail($this->order_id);

        $this->metadata['order_id'] = $order->id;
        $this->metadata['customer_id'] = $order->customer_id;

        if ($order->pickup && $order->pickup->setting('process_order_email')) {
            $template = Template::findOrFail($order->pickup->setting('process_order_email'));
        } else {
            $template = Template::findOrFail(setting('process_order_email'));
        }

        $mergedContent = $template->mergeWithOrder($order);
        $this->subject = $template->subject;
        $this->content = $mergedContent->getHTML();
        $this->text = $mergedContent->getText();

        $this->from($template->getFromEmail(), $template->getFromName());
        $this->replyTo($template->getReplyToEmail(), $template->getFromName());

        if ($order->hasSecondaryEmail()) {
            $this->cc($order->secondaryEmail());
        }

        return $this->withSymfonyMessage(function (Email $m) {
            $headers = $m->getHeaders();
            $headers->addTextHeader('X-Mailgun-Variables', json_encode($this->metadata));
            $headers->addTextHeader('X-Mailgun-Tag', 'Order Processed');
        })
            ->view('emails.transactional-html')
            ->text('emails.transactional-text');
    }

    public function tags()
    {
        return ['email'];
    }
}
