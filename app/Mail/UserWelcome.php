<?php

namespace App\Mail;

use App\Models\Template;
use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\SerializesModels;
use Symfony\Component\Mime\Email;

class UserWelcome extends TenantAwareMailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    public $user_id;
    public $subject;
    public $from;
    public $content;
    public $text;
    public $styles;
    public $metadata;

    public function __construct(int $user_id)
    {
        $this->user_id = $user_id;
    }

    public function build()
    {
        $user = User::findOrFail($this->user_id);
        $template = Template::findOrFail(setting('email_customer_welcome_template'));

        $mergedContent = $template->mergeWithUser($user);
        $this->subject = $template->subject;
        $this->content = $mergedContent->getHTML();
        $this->text = $mergedContent->getText();
        $this->styles = $template->settings;

        $this->from($template->getFromEmail(), $template->getFromName());
        $this->replyTo($template->getReplyToEmail(), $template->getFromName());

        return $this->withSymfonyMessage(function (Email $m) {
            $headers = $m->getHeaders();
            $headers->addTextHeader('X-Mailgun-Variables', json_encode($this->metadata));
            $headers->addTextHeader('X-Mailgun-Tag', 'New Customer Welcome');
        })
            ->view('emails.transactional-html')
            ->text('emails.transactional-text');
    }

    public function tags()
    {
        return ['email'];
    }
}
