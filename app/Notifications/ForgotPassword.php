<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Messages\MailMessage;

class ForgotPassword extends TenantAwareNotification
{
    use Queueable;

    public function __construct(
        public string $token
    ) {}

    public function via(): array
    {
        return ['mail'];
    }

    public function toMail(): MailMessage
    {
        return (new MailMessage)
            ->view('emails.notifications.password-reminder')
            ->action('Reset Password', url('/admin/password/reset/' . $this->token))
            ->subject('Password Reset')
            ->from(config('mail.from.address'), config('mail.from.name'));
    }

    public function tags(): array
    {
        return ['notification'];
    }
}
