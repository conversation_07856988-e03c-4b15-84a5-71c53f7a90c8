<?php

namespace App\Notifications;

use App\Models\Order;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;

class OrderComment extends TenantAwareNotification implements ShouldQueue
{
    use Queueable;

    public function __construct(
        public int $order_id
    ) {}

    public function via(): array
    {
        return ['mail'];
    }

    public function toMail(): MailMessage
    {
        $order = Order::forEmail()->findOrFail($this->order_id);

        return (new MailMessage)
            ->view('emails.notifications.order-comment', [
                'order' => $order,
            ])
            ->subject('Order Comment Received')
            ->action('View Order', url('/admin/orders/' . $order->id . '/edit'))
            ->replyTo($order->customer_email, $order->getCustomerName())
            ->from(config('mail.from.address'), config('mail.from.name'));
    }

    public function tags(): array
    {
        return ['notification'];
    }
}
