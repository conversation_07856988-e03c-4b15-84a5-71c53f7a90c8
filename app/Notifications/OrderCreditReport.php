<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class OrderCreditReport extends Notification
{
    use Queueable;

    public function __construct(
        public string $filepath,
    ) {}

    public function via(object $notifiable): array
    {
        return ['mail'];
    }

    public function toMail(object $notifiable): MailMessage
    {
        $filename = Str::of($this->filepath)->afterLast('/');

        $previousMonth = now()->subMonth()->format('F Y');

        return (new MailMessage)
            ->subject("Monthly Order Credit Report: $previousMonth")
            ->line('Please find attached the order credit report for the previous month.')
            ->attachData(
                Storage::disk('s3')->get($this->filepath),
                $filename,
                ['mime' => 'text/csv']
            );
    }
}
