<?php

namespace App\Notifications;

use App\Models\Proposal;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;

class NewProposal extends TenantAwareNotification implements ShouldQueue
{
    use Queueable;

    public function __construct(
        public int $proposal_id
    ) {}

    public function via(): array
    {
        return ['mail'];
    }

    public function toMail(): MailMessage
    {
        /** @var Proposal $proposal */
        $proposal = Proposal::findOrFail($this->proposal_id);

        return (new MailMessage)
            ->view('emails.notifications.proposal', [
                'proposal' => $proposal,
            ])
            ->replyTo($proposal->email, $proposal->first_name . ' ' . $proposal->last_name)
            ->action('View Proposal', url('/admin/proposals/' . $proposal->id . '/edit'))
            ->subject('New Location Proposal')
            ->from(config('mail.from.address'), config('mail.from.name'));
    }

    public function tags(): array
    {
        return ['notification'];
    }
}
