<?php

namespace App\Traits;

use App\OrderWindow;
use Illuminate\Support\Collection;

trait PickupDateSelectBuilder
{
    /**
     * @param  Collection<int, OrderWindow>  $window_collection
     */
    public function buildPickupDateSelect(Collection $window_collection, ?string $selected_date = null): string
    {
        if ($window_collection->isEmpty()) {
            return 'No dates found.';
        }

        if ($window_collection->count() === 1) {
            return $window_collection->first()
                ->deliveryDatetime()
                ->copy()
                ->format('D, M jS, Y');
        }

        $date_array = $window_collection
            ->mapWithKeys(function (OrderWindow $order_window) {
                return [$order_window->dateId() => $order_window->deliveryDatetime()->format('D, M jS, Y')];
            })
            ->all();

        $output = '<select class="form-control" name="pickup_date" id="pickupSelect" tabindex="3">';
        $output .= selectOptions($date_array, $selected_date);
        $output .= '</select>';

        return $output;
    }
}