<?php

namespace App\Traits;

use Carbon\Carbon;
use Illuminate\Support\Collection;

trait DateRangeTrait
{
    /**
     * @return Collection<string, string|null>
     */
    public function getDateRange(array $range, string $format = 'Y-m-d'): Collection
    {
        $start = null;
        if ($range['start'] ?? false) {
            $start = Carbon::parse($range['start'])->format($format);
        }

        $end = null;
        if ($range['end'] ?? false) {
            $end = Carbon::parse($range['end'])->format($format);
        }

        $collection = collect();
        $collection->put('start', $start);
        $collection->put('end', $end);

        return $collection;
    }
}
