@extends('theme::_layouts.'.setting('login_layout', 'main'), [
    'pageTitle' => 'Finish Your Account: Choose A Password',
    'showLogo' => true,
    'messageType' => 'registration_message'
])

@section('content')
    <div class="advancedRegistration" style="background-image: linear-gradient(rgba(0,0,0,.6),rgba(0,0,0,.2)),url(https://grazecart.com/images/banner-background.jpg);">
        <div class="advancedRegistrationPanel">
            <div class="advancedRegistrationPanel-body">
                <header>
                    <h1>Choose Password</h1>
                    <div>Select a password to complete your account</div>
                </header>

                <form action="{{ route('choose-password.store') }}" method="POST" class="text-left">
                    @csrf
                    <div class="form-group">
                        <label for="email">Email</label>
                        <input
                                type="email"
                                name="email"
                                readonly
                                value="{{ old('email', request('email', (auth()->user()?->email ?? null))) }}"
                                class="form-control"
                                id="email"
                        >
                    </div>
                    <div class="form-group">
                        <label for="password">Password</label>
                        <input
                                type="password"
                                name="password"
                                class="form-control"
                                id="password"
                                placeholder="@lang('Password')"
                                required
                        >
                    </div>
                    <div class="form-group">
                        <button type="submit" class="btn btn-action btn-block">Submit</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
@endsection
