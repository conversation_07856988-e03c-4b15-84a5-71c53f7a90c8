@extends('theme::_layouts.main', ['pageTitle' => 'Forgot password'])

@section('pageMetaTags')
    <META NAME="ROBOTS" CONTENT="NOINDEX, NOFOLLOW">
@stop

@section('content')
    <div class="page<PERSON>ontainer forgotPasswordPage">
        <header class="forgotPasswordPage__header">
            <h1 class="forgotPasswordPage__heading h1">Reset Your Password</h1>
            <p>Enter your email address then check your inbox for further instructions.</p>
        </header>
        <form action="/password/forgot" method="POST" class="forgotPasswordPage__form">
            @csrf
            @if($errors->has('email'))
                <div class="form-group has-error">
                    <label class="control-label" for="email">Email ({{ $errors->first('email') }})</label>
                    @else
                        <div class="form-group">
                            <label class="control-label" for="email">Email</label>
                            @endif
                            <input type="email" name="email" value="{{ old('email') }}" class="form-control" id="email" tabindex="1" autofocus>
                        </div>

                        <div class="form-group">
                            <button type="submit" class="btn btn-default btn-block" tabindex="2">Submit</button>
                        </div>
        </form>
    </div>
@stop
