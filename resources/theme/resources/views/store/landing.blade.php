@extends('theme::_layouts.main', [
	'pageTitle' => $page->getMetaTitle(),
	'pageDescription' => $page->getMetaDescription(),
	'robots' => $page->robots()
])

@php
    /**
     * @var \App\Models\Order|null $openOrder
     * @var \App\Contracts\Cartable $cart
     */

    $store_menu_layout = 'no-menu';

@endphp

@section('pageMetaTags')
    {!! $page->settings->meta_tags !!}
@stop

@section('head')
    <link rel="stylesheet" href="/theme/pages/{{ $page->id }}/page.css?id={{ $page->updated_at->timestamp ?? Str::random('5') }}">
@stop

@section('content')
    @include('theme::store._layouts.'.$store_menu_layout, [
        'html' => $html ?? null,
        'order' => $openOrder,
        'cart' => $cart
    ])

    @if(auth()->check())
        <ul class="cartMenu">
            @if(auth()->user()->hasRecurringOrder())
                <li id="cartContainer">
                    <a @click="$dispatch('openPanel', { title: 'Subscription', component: 'theme.subscription-side-panel' });" class="tw-cursor-pointer hover:tw-no-underline">
                        <i class="fa fa-shopping-basket" style="margin-right: 0.25rem;"></i>
                        <livewire:theme.subscription-item-count/>
                    </a>
                </li>
                <li>
                    <a @click="$dispatch('openPanel', { title: 'Subscription', component: 'theme.subscription-side-panel' });" class="btn btn-brand btn-sm">
                        Edit Subscription <i class="fa fa-chevron-right hidden-xs"></i>
                    </a>
                </li>
            @elseif( ! is_null($openOrder))
                <li id="cartContainer">
                    <a @click="$dispatch('openPanel', { title: 'Order {{ $openOrder->id }}', component: 'theme.order-side-panel' });" class="tw-cursor-pointer hover:tw-no-underline">
                        <i class="fa fa-shopping-basket" style="margin-right: 0.25rem;"></i>
                        <livewire:theme.order-item-count/>
                        <small>(edit)</small>
                    </a>
                </li>
            @else
                <li id="cartContainer">
                    <a @click="$dispatch('openPanel', { title: 'Shopping cart', component: 'theme.cart-side-panel' });" class="tw-cursor-pointer hover:tw-no-underline">
                        <i class="fa fa-shopping-basket" style="margin-right: 0.25rem;"></i>
                        <livewire:theme.cart-item-count/>
                    </a>
                </li>
                <li>
                    <a href="/checkout" class="btn btn-action btn-sm">
                        @lang('Checkout') <i class="fa fa-chevron-right hidden-xs"></i>
                    </a>
                </li>
            @endif
        </ul>
    @endif
@endsection

@push('scripts')
    <script>
        if (typeof fbq !== 'undefined') {
            fbq('track', 'ViewContent', {
                content_name: 'Store',
                content_category: 'storefront'
            });
        }
    </script>
@endpush
