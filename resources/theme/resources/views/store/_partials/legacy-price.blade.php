@can('see-price', App\Models\Product::class)
    @if($product->isPricedByWeight() && setting('show_price_per_pound', true))
        @if($product->isOnSale())
            <div class="productListing__saleSavings productListing__saleSavings--list">&#36;{{ money($product->getSavings()) }}/{{ __("messages.uom." . setting('weight_uom', 'pounds')) }}. savings</div>

            <div class="productListing__originalPrice">&#36;{{ money($product->getRegularUnitPrice()) }}/{{ __("messages.uom." . setting('weight_uom', 'pounds')) }}.</div>

            <div class="saleTextColor">
                &#36;<span itemprop="price">{{ money($product->getUnitPrice()) }}</span>/{{ __("messages.uom." . setting('weight_uom', 'pounds')) }}.
            </div>

            <div class="productListing__averageWeight">Avg. {{ weight($product->weight) }}</div>

        @else
            <div>&#36;<span itemprop="price">{{ money($product->getUnitPrice()) }}</span>/{{ __("messages.uom." . setting('weight_uom', 'pounds')) }}.</div>
            <div class="productListing__averageWeight">Avg. {{ weight($product->weight) }}</div>
        @endif
    @else
        @if($product->isOnSale())
            <div class="productListing__saleSavings productListing__saleSavings--list">&#36;{{ money($product->getSavings()) }}{{ $product->isPricedByWeight() ? '/' . __("messages.uom." . setting('weight_uom', 'pounds')) : '' }} savings</div>

            <div class="productListing__originalPrice">&#36;{{ money($product->getRegularPrice()) }}</div>
            <div class="saleTextColor">
                &#36;<span itemprop="price">{{ money($product->getPrice()) }}</span>
            </div>
        @else
            &#36;<span itemprop="price">{{ money($product->getPrice()) }}</span>
        @endif
    @endif
@endcan
@cannot('see-price', App\Models\Product::class)
    @include('theme::store._partials.price_gate.price')
@endcannot