@if(setting('disqus_forum_shortname'))
<div id="disqus_thread"></div>
<script> 
var disqus_config = function () {
    this.page.url = "{{ request()->url() }}";
    this.page.identifier = "{{ $post->setting('page_identifier', null) ?? $post->id }}";
    this.page.title = "{{ $post->title }}"
};

(function() {
    var d = document, s = d.createElement('script');
    s.src = "https://{{ setting('disqus_forum_shortname') }}.disqus.com/embed.js";
    s.setAttribute('data-timestamp', + new Date());
    (d.head || d.body).appendChild(s);
})();
</script>
<noscript>Please enable JavaScript to view the <a href="https://disqus.com/?ref_noscript">comments powered by Disqus.</a></noscript>
@else
<div style="margin-top: 2rem; padding: 0.5rem; text-align: center; background-color: #eee; color: #999; font-size: 13px;">
    Missing Disqus website shortname. Please add from the admin.
</div>
@endif