<form action="/register" method="POST">
    @csrf
    <!-- Username Form Field -->
    <div class="form-group gc-form-field">
        <label for="username">Username</label>
        <input type="text" name="username" value="{{ old('username') }}" class="form-control" autocomplete="off"/>
        <input type="hidden" name="timestamp" value="{{ time() }}"/>
    </div>

    <div class="form-group">
        <label for="first_name">First Name</label>
        <input type="text" name="first_name" value="{{ old('first_name') }}" class="form-control"/>
    </div>

    <div class="form-group">
        <label for="last_name">Last Name</label>
        <input type="text" name="last_name" value="{{ old('last_name') }}" class="form-control"/>
    </div>

    <!-- Username Form Field -->
    <div class="form-group">
        <label for="email">Email</label>
        <input type="text" name="email" value="{{ old('email') }}" class="form-control"/>
    </div>

    <!-- Password Form Field -->
    <div class="form-group">
        <label for="password">Password</label>
        <input type="password" name="password" value="" class="form-control"/>
    </div>

    @if(!isset($location))
        <!-- Pickup Location -->
        <div class="form-group">
            <label for="pickup_point">Pickup Location</label>
            <x-theme::form.delivery-method-select :selected="request('location_id')"/>
        </div>
    @else
        @if($location->isDeliveryZone())
            <div class="form-group">
                <input type="hidden" name="pickup_point" value="{{ request()->get('location_id') }}">
            </div>
        @else
            <div class="form-group">
                <label for="pickup_point">Pickup Location</label>
                <x-theme::form.delivery-method-select :selected="request('location_id')"/>
            </div>
        @endif
    @endif
    <input type="hidden" value="{{ request('referral_code') }}" name="referral">
    @if(!isset($location))
        <!-- Pickup Location -->
        <div class="form-group">
            <label for="pickup_point">Pickup Location</label>
            <select class="form-control" name="pickup_point">
                <x-theme::form.delivery-method-select :selected="request('location_id')"/>
            </select>
        </div>
    @else
        @if($location->isDeliveryZone())
            <div class="form-group">
                <input type="hidden" name="pickup_point" value="{{ request()->get('location_id') }}">
            </div>
        @else
            <div class="form-group">
                <label for="pickup_point">Pickup Location</label>
                <select class="form-control" name="pickup_point">
                    <x-theme::form.delivery-method-select :selected="request('location_id')"/>
                </select>
            </div>
        @endif
    @endif
    <input type="hidden" value="{{ request('referral_code') }}" name="referral">

    <div class="form-group">
        <button type="submit" class="btn btn-danger btn-lg btn-block">Create Free Account</button>
    </div>
</form>
