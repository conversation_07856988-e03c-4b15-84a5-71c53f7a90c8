@php
    /** @var \App\Models\MenuItem $menu_item */
@endphp

@if($menu_item->isSubmenu())
    <div x-data="{ open: false }" @click.away="open = false" @mouseleave="open = false" class="tw-relative">
        <button type="button" @mouseenter="open = true" class="tw-py-2 tw-px-3 tw-rounded-md tw-inline-flex tw-text-white tw-items-center tw-gap-x-1 hover:tw-text-gray-100 hover:tw-bg-theme-order-status-bg-color/60 sm:tw-text-sm" :aria-expanded="open">
            <span>{{ $menu_item->getLabel() }}</span>
            <svg class="tw-h-4 tw-w-4" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                <path fill-rule="evenodd" d="M5.23 7.21a.75.75 0 011.06.02L10 11.168l3.71-3.938a.75.75 0 111.08 1.04l-4.25 4.5a.75.75 0 01-1.08 0l-4.25-4.5a.75.75 0 01.02-1.06z" clip-rule="evenodd"></path>
            </svg>
        </button>

        <!-- Flyout menu, show/hide based on flyout menu state. -->
        <div
                x-show="open"
                x-transition:enter="tw-transition tw-ease-out tw-duration-200"
                x-transition:enter-start="tw-opacity-0 tw-translate-y-1"
                x-transition:enter-end="tw-opacity-100 tw-translate-y-0"
                x-transition:leave="tw-transition tw-ease-in tw-duration-150"
                x-transition:leave-start="tw-opacity-100 tw-translate-y-0"
                x-transition:leave-end="tw-opacity-0 tw-translate-y-1"
                class="tw-absolute tw--left-4 tw-z-20 tw-flex tw-w-screen tw-max-w-min"
        >
            <div x-cloak class="tw-py-1.5 tw-w-56 tw-shrink tw-rounded-md tw-bg-white tw-text-gray-900 tw-shadow-lg tw-ring-1 tw-ring-gray-900/5 sm:tw-text-sm">
                @foreach($menu_item->submenu->items as $index => $sub_menu_item)
                    <a href="{{ $sub_menu_item->getUrl() }}" class="tw-block tw-px-4 tw-py-2 tw-text-gray-900 hover:tw-bg-gray-100 tw-no-underline">{{ $sub_menu_item->getLabel() }}</a>
                @endforeach
            </div>
        </div>
    </div>
@else
    <div class="tw-block">
        <a href="{{ $menu_item->getUrl() }}">
            <button type="button" class="tw-py-2 tw-px-3 tw-rounded-md tw-inline-flex tw-text-white tw-items-center tw-gap-x-1 hover:tw-text-gray-100 hover:tw-bg-theme-order-status-bg-color/60 sm:tw-text-sm">
                <span>{{ $menu_item->getLabel() }}</span>
            </button>
        </a>
    </div>
@endif
