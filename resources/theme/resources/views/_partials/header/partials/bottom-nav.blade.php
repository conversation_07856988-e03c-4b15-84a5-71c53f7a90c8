<!-- Start desktop main menu -->
@php
    /** @var \App\Models\Menu $main_menu */
@endphp
<div class="tw-hidden tw-bg-theme-main-navigation-bg-color lg:tw-block">
    <div class="tw-relative tw-mx-auto tw-max-w-7xl tw-px-4 sm:tw-px-6 lg:tw-px-8">
        <div class="tw-flex tw-items-center">
            <!-- Desktop Store menu toggle, controls the 'storeMenuIsOpen' state. -->
            <button @click="storeMenuIsOpen = !storeMenuIsOpen" type="button" class="tw--ml-4 tw-py-2 tw-px-4 tw-relative tw-flex tw-items-center tw-rounded-md tw-bg-transparent tw-text-theme-auxiliary-link-color hover:tw-bg-theme-order-status-bg-color/60">
                <span class="tw-absolute tw--inset-0.5"></span>
                <span class="tw-sr-only">Open store menu</span>
                <div class="tw-inline-flex tw-items-center tw-space-x-2">
                    <svg class="tw-h-4 tw-w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M3.75 6A2.25 2.25 0 0 1 6 3.75h2.25A2.25 2.25 0 0 1 10.5 6v2.25a2.25 2.25 0 0 1-2.25 2.25H6a2.25 2.25 0 0 1-2.25-2.25V6ZM3.75 15.75A2.25 2.25 0 0 1 6 13.5h2.25a2.25 2.25 0 0 1 2.25 2.25V18a2.25 2.25 0 0 1-2.25 2.25H6A2.25 2.25 0 0 1 3.75 18v-2.25ZM13.5 6a2.25 2.25 0 0 1 2.25-2.25H18A2.25 2.25 0 0 1 20.25 6v2.25A2.25 2.25 0 0 1 18 10.5h-2.25a2.25 2.25 0 0 1-2.25-2.25V6ZM13.5 15.75a2.25 2.25 0 0 1 2.25-2.25H18a2.25 2.25 0 0 1 2.25 2.25V18A2.25 2.25 0 0 1 18 20.25h-2.25A2.25 2.25 0 0 1 13.5 18v-2.25Z"/>
                    </svg>
                    <div class="tw-font-semibold tw-text-sm">Shop</div>
                    <svg class="tw-h-4 tw-w-4" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                        <path fill-rule="evenodd" d="M5.23 7.21a.75.75 0 011.06.02L10 11.168l3.71-3.938a.75.75 0 111.08 1.04l-4.25 4.5a.75.75 0 01-1.08 0l-4.25-4.5a.75.75 0 01.02-1.06z" clip-rule="evenodd"></path>
                    </svg>
                </div>
            </button>

            <!-- Divider -->
            <div class="tw-pl-4 tw-h-6 tw-border-r-2 tw-border-gray-700/10"></div>

            <!-- Desktop Main menu -->
            <div class="tw-pl-4 tw-relative tw-flex tw-flex-nowrap">
                <div class="tw-py-1 tw-space-x-4 tw-flex tw-items-start tw-flex-nowrap tw-grow-0 tw-shrink tw-basis-auto">
                    @foreach($main_menu->items as $index => $menu_item)
                        @include('theme::_partials.header.partials.main-menu-item', compact('menu_item'))
                    @endforeach

                    <div x-data="{ open: false }" @click.away="open = false" @mouseleave="open = false" class="tw-relative">
                        <button type="button" @mouseenter="open = true" class="tw-py-2 tw-px-3 tw-rounded-md tw-inline-flex tw-text-white tw-items-center tw-gap-x-1 hover:tw-text-gray-100 hover:tw-bg-theme-order-status-bg-color/60 sm:tw-text-sm" :aria-expanded="open">
                            <span>Help</span>
                            <svg class="tw-h-4 tw-w-4" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                <path fill-rule="evenodd" d="M5.23 7.21a.75.75 0 011.06.02L10 11.168l3.71-3.938a.75.75 0 111.08 1.04l-4.25 4.5a.75.75 0 01-1.08 0l-4.25-4.5a.75.75 0 01.02-1.06z" clip-rule="evenodd"></path>
                            </svg>
                        </button>

                        <!-- Flyout menu, show/hide based on flyout menu state. -->
                        <div
                                x-show="open"
                                x-transition:enter="tw-transition tw-ease-out tw-duration-200"
                                x-transition:enter-start="tw-opacity-0 tw-translate-y-1"
                                x-transition:enter-end="tw-opacity-100 tw-translate-y-0"
                                x-transition:leave="tw-transition tw-ease-in tw-duration-150"
                                x-transition:leave-start="tw-opacity-100 tw-translate-y-0"
                                x-transition:leave-end="tw-opacity-0 tw-translate-y-1"
                                class="tw-absolute tw--left-4 tw-z-20 tw-flex tw-w-screen tw-max-w-min"
                        >
                            <div x-cloak class="tw-py-1.5 tw-w-56 tw-shrink tw-rounded-md tw-bg-white tw-text-gray-900 tw-shadow-lg tw-ring-1 tw-ring-gray-900/5 sm:tw-text-sm">
                                <a href="{{ url('/customer-support') }}" class="tw-block tw-px-4 tw-py-2 tw-text-gray-900 hover:tw-bg-gray-100 tw-no-underline">Contact Us</a>
                                <button type="button" id="accessibilityTriggerId" class="tw-block tw-w-full tw-text-left tw-px-4 tw-py-2 tw-text-gray-900 hover:tw-bg-gray-100 tw-no-underline">Accessibility</button>
                                <button type="button" x-on:click="window.openZendeskWidget(); open = false;" class="tw-block tw-w-full tw-text-left tw-px-4 tw-py-2 tw-text-gray-900 hover:tw-bg-gray-100 tw-no-underline">Customer FAQs</button>
                            </div>
                        </div>
                    </div>
                </div>

                @if ($main_menu->items->count() > 5)
                    <div class="tw-whitespace-nowrap tw-basis-0 tw-shrink-0 tw-flex tw-items-center tw-justify-center ">
                        <div x-data="{ open: false }" @click.away="open = false" @mouseleave="open = false" class="tw-relative">
                            <button type="button" @mouseenter="open = true" class="tw-py-2 tw-pr-12 tw-inline-flex tw-items-center tw-gap-x-1 tw-text-sm tw-text-theme-main-navigation-link-color hover:tw-text-theme-main-navigation-link-color-hover" :aria-expanded="open">
                                <span>More</span>
                                <svg class="tw-h-4 tw-w-4" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                    <path fill-rule="evenodd" d="M5.23 7.21a.75.75 0 011.06.02L10 11.168l3.71-3.938a.75.75 0 111.08 1.04l-4.25 4.5a.75.75 0 01-1.08 0l-4.25-4.5a.75.75 0 01.02-1.06z" clip-rule="evenodd"></path>
                                </svg>
                            </button>

                            <!--
                              Flyout menu, show/hide based on flyout menu state.
                            -->
                            <div
                                    x-show="open"
                                    x-transition:enter="tw-transition tw-ease-out tw-duration-200"
                                    x-transition:enter-start="tw-opacity-0 tw-translate-y-1"
                                    x-transition:enter-end="tw-opacity-100 tw-translate-y-0"
                                    x-transition:leave="tw-transition tw-ease-in tw-duration-150"
                                    x-transition:leave-start="tw-opacity-100 tw-translate-y-0"
                                    x-transition:leave-end="tw-opacity-0 tw-translate-y-1"
                                    class="tw-absolute tw--left-4 tw-z-10 tw-flex tw-w-screen tw-max-w-min"
                            >
                                <div x-cloak class="tw-w-56 tw-shrink tw-rounded-md tw-bg-white tw-p-4 tw-text-sm tw-text-gray-900 tw-shadow-lg tw-ring-1 tw-ring-gray-900/5">
                                    @foreach($main_menu->items as $index => $menu_item)
                                        @php
                                            /** @var \App\Models\MenuItem $menu_item */
                                        @endphp
                                        <a href="{{ $menu_item->getUrl() }}" class="tw-p-2 hover:tw-text-gray-600 @if($index >= 5) tw-block @elseif($index >= 3) tw-block lg:tw-hidden @elseif($index >= 1) tw-block md:tw-hidden @else tw-hidden @endif">{{ $menu_item->getLabel() }}</a>
                                    @endforeach
                                </div>
                            </div>
                        </div>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>
<!-- End desktop main menu -->
