@php
    /** @var \App\Models\MenuItem $menu_item */
@endphp


@if($menu_item->isSubmenu())
    <div x-data="{ submenuIsExpanded: false }">
        <button @click="submenuIsExpanded = !submenuIsExpanded" type="button"
                class="tw-flex tw-items-center tw-w-full tw-text-left tw-rounded-md tw-py-3 tw-px-4 tw-gap-x-3 hover:tw-bg-gray-100 sm:tw-text-sm"
                aria-controls="sub-menu-{{ $menu_item->id }}" :aria-expanded="submenuIsExpanded">
            <span class="tw-flex-1">
                <a @click.stop href="{{ $menu_item->getUrl() }}"
                   class="tw-text-gray-900">{{ $menu_item->getLabel() }}</a>
            </span>
            <svg class="tw-text-gray-400 tw-h-5 tw-w-5 tw-shrink-0 tw-transition-transform tw-duration-75"
                 :class="{ 'tw-rotate-90 tw-text-gray-500': submenuIsExpanded, 'tw-text-gray-400': submenuIsExpanded }"
                 viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                <path fill-rule="evenodd"
                      d="M7.21 14.77a.75.75 0 01.02-1.06L11.168 10 7.23 6.29a.75.75 0 111.04-1.08l4.5 4.25a.75.75 0 010 1.08l-4.5 4.25a.75.75 0 01-1.06-.02z"
                      clip-rule="evenodd"></path>
            </svg>
        </button>
        <ul style="display: none;" x-show="submenuIsExpanded" class="tw-mt-1 tw-pl-4"
            id="sub-menu-{{ $menu_item->id }}">
            @foreach($menu_item->submenu->items as $index => $sub_menu_item)
                @php
                    /** @var \App\Models\MenuItem $sub_menu_item */
                @endphp
                <li>
                    <a href="{{ $sub_menu_item->getUrl() }}"
                       class="tw-no-underline tw-text-gray-700 tw-block tw-rounded-md tw-py-3 tw-px-4 hover:tw-bg-gray-100 sm:tw-text-sm">{{ $sub_menu_item->getLabel() }}</a>
                </li>
            @endforeach
        </ul>
    </div>

@else
    <a href="{{ $menu_item->getUrl() }}"
       class="tw-no-underline tw-text-gray-700 tw-block tw-rounded-md tw-py-3 tw-px-4 hover:tw-bg-gray-100 sm:tw-text-sm">{{ $menu_item->getLabel() }}</a>
@endif



