@props(['cart' => null, 'order' => null, 'subscription' => null, 'delivery_method'])
@php
    /**
     * @var \App\Contracts\Cartable|null $cart
     * @var \App\Models\Order|null $order
     * @var \App\Models\RecurringOrder|null $subscription
     * @var \App\Models\Pickup $delivery_method
     */
    $cap = (int) $delivery_method->delivery_fee_cap;

    if ( ! is_null($subscription)) {
        $subtotal = $subscription->subtotal();
        $has_met_cap = $delivery_method->hasMetDeliveryCapThreshold(
            $subtotal,
            $delivery_method->uncappedSubscriptionDeliveryFee($subscription)
        );
    } elseif ( ! is_null($order)) {
        $subtotal = $order->subtotal;
        $has_met_cap = $delivery_method->hasMetDeliveryCapThreshold(
            $subtotal,
            $delivery_method->uncappedOrderDeliveryFee($order)
        );
    } else {
        $subtotal = $cart->cartSubtotal();
        $has_met_cap = $delivery_method->hasMetDeliveryCapThreshold(
            $subtotal,
            $delivery_method->uncappedCartDeliveryFee($cart)
        );
    }

@endphp

<div class="tw-border-l-4 tw-bg-gray-50 @if($has_met_cap) tw-border-theme-brand-color @else tw-border-transparent @endif tw-px-6 tw-py-4">
    <div class="tw-flex tw-items-center tw-justify-between">
        <div class="tw-flex tw-items-center">
            <svg class="tw-text-gray-500 tw-w-5 tw-h-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" d="M8.25 18.75a1.5 1.5 0 01-3 0m3 0a1.5 1.5 0 00-3 0m3 0h6m-9 0H3.375a1.125 1.125 0 01-1.125-1.125V14.25m17.25 4.5a1.5 1.5 0 01-3 0m3 0a1.5 1.5 0 00-3 0m3 0h1.125c.621 0 1.129-.504 1.09-1.124a17.902 17.902 0 00-3.213-9.193 2.056 2.056 0 00-1.58-.86H14.25M16.5 18.75h-2.25m0-11.177v-.958c0-.568-.422-1.048-.987-1.106a48.554 48.554 0 00-10.026 0 1.106 1.106 0 00-.987 1.106v7.635m12-6.677v6.677m0 4.5v-4.5m0 0h-12" />
            </svg>
            <p class="tw-m-0 tw-ml-2 tw-text-base">
                @if( ! $has_met_cap)
                    ${{ money($delivery_method->delivery_total_threshold - $subtotal) }} away from {{ $cap > 0 ? "$".money($cap) : 'free' }} delivery
                @else
                    {{ $cap > 0 ? "$".money($cap) : 'Free'}} delivery applied!
                @endif
            </p>
        </div>

        <div>
            @if( ! $has_met_cap)
                <svg id="delivery-exclamation" class="tw-text-gray-500 tw-w-6 tw-h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M12 9v3.75m9-.75a9 9 0 11-18 0 9 9 0 0118 0zm-9 3.75h.008v.008H12v-.008z" />
                </svg>
            @else
                <svg id="delivery-check" class="tw-text-theme-brand-color tw-w-6 tw-h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
            @endif
        </div>
    </div>

</div>
