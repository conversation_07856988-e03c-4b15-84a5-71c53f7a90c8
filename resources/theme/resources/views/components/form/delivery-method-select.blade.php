@props(['selected' => null])

@php
    $delivery_methods = \App\Models\Pickup::select(['id', 'title'])
        ->where('visible', true)
        ->whereIn('status_id', [\App\Support\Enums\PickupStatus::open(), \App\Support\Enums\PickupStatus::comingSoon()])
        ->orderBy('title')
        ->pluck('title', 'id');
@endphp
<select {{ $attributes }}>
    @foreach($delivery_methods as $id => $title)
        <option value="{{ $id }}" @selected($selected === $id)>{{ $title }}</option>
    @endforeach
</select>
