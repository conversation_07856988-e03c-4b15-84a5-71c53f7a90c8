@props([
    'product',
    'order' => null,
    'has_subscription' => false,
    'cta_label' => null,
    'cta_action' => null,
    'cta_classes' => null,
    'variant_layout' => 'popper',
    'metadata' => [],
])

@php
    /**
     * @var \App\Models\Product $product
     * @var \App\Models\Order|null $order
     */

    $style = theme('store_button_style', 'btn-brand');
@endphp

@if($cta_action === 'show_details')
    <a href="{{ route('store.show', [$product->slug]) }}" class="tw-w-full tw-no-underline">
        <button type="button" class="tw-block tw-w-full @if($cta_classes ?? false) {{ $cta_classes }} @else tw-px-3 tw-py-2 tw-rounded-none tw-rounded-b-lg tw-text-sm tw-font-semibold @endif">
            {{ $cta_label ?? (!is_null($order) ? $product->orderActionLabel($order->isFromBlueprint()) : $product->cartActionLabel()) }}
        </button>
    </a>
@elseif($product->setting('links_externally'))
    <a href="{{ $product->setting('links_externally:url', url('/store/product/' . $product->slug)) }}" target="_blank" class="tw-block tw-w-full @if($cta_classes ?? false) {{ $cta_classes }} @else tw-px-3 tw-py-2 tw-rounded-none tw-rounded-b-lg tw-text-sm tw-font-semibold @endif">
        {{ $product->setting('links_externally:text', 'More Info') }}
    </a>
@elseif($product->hasVariants())
    {{--    <livewire:theme.add-variant-button--}}
    {{--            :product="$product"--}}
    {{--            :has_subscription="$has_subscription"--}}
    {{--            :has_order="!is_null($order)"--}}
    {{--            :cta_label="$cta_label"--}}
    {{--            :cta_classes="$cta_classes"--}}
    {{--            :variant_layout="$variant_layout"--}}
    {{--            :metadata="$metadata"--}}
    {{--    />--}}
    VARIANT
@elseif($product->isOutOfStock())
    <button class="tw-block tw-w-full tw-px-3 tw-py-2 tw-bg-gray-200 tw-text-gray-700 tw-rounded-none tw-rounded-b-lg tw-text-sm tw-font-semibold" type="button" disabled>
        Sold Out
    </button>
@elseif(auth()->guest())
    <a href="{{ route('register') }}" class="tw-w-full tw-no-underline">
        <button type="button" class="tw-block tw-w-full @if($cta_classes ?? false) {{ $cta_classes }} @else tw-px-3 tw-py-2 tw-rounded-none tw-rounded-b-lg tw-text-sm tw-font-semibold @endif  btn {{ $style }}  productListing__addToCartButton--grid">
            {{ $cta_label ?? (!is_null($order) ? $product->orderActionLabel($order->isFromBlueprint()) : $product->cartActionLabel()) }}
        </button>
    </a>
@elseif($product->isPreOrder() || ($product->isGiftCard() && $product->isFulfilledVirtually()))
    <a href="{{ $product->checkoutPath() }}" class="tw-block tw-w-full @if($cta_classes ?? false) {{ $cta_classes }} @else tw-px-3 tw-py-2 tw-rounded-none tw-rounded-b-lg tw-text-sm tw-font-semibold @endif   btn {{ $style }}  productListing__addToCartButton--grid">
        {{ !is_null($order) ? $product->orderActionLabel($order->isFromBlueprint()) : $product->cartActionLabel() }}
    </a>
@else
    <livewire:theme.add-product-counter-button
            :product="$product"
            :has_subscription="$has_subscription"
            :has_order="!is_null($order)"
            :cta_label="$cta_label"
            :cta_classes="$cta_classes"
            :metadata="$metadata"
    />
@endif

