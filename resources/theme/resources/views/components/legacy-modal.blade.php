@props(['id'])

<div x-data="legacyModal"
     x-show="open"
     x-cloak
     class="tw-reset"
     aria-labelledby="modal-title"
     x-ref="dialog"
     aria-modal="true"
     id="{{ $id }}"
     @keydown.window.escape="open = false"
>
    <div class="tw-relative tw-z-[1000]">
        <div
                x-show="open"
                x-cloak
                style="display: none;"
                x-transition:enter="tw-ease-out tw-duration-300"
                x-transition:enter-start="tw-opacity-0"
                x-transition:enter-end="tw-opacity-100"
                x-transition:leave="tw-ease-in tw-duration-200"
                x-transition:leave-start="tw-opacity-100"
                x-transition:leave-end="tw-opacity-0"
                x-on:click="close"
                class="tw-pointer-events-auto tw-fixed tw-inset-0 tw-bg-gray-500 tw-bg-opacity-75 tw-transition-opacity"
        ></div>

        <div x-show="open" class="tw-pointer-events-none tw-fixed tw-inset-0 tw-z-10 tw-overflow-y-auto">
            <div class="tw-flex tw-min-h-full tw-items-end tw-justify-center tw-p-4 tw-text-center sm:tw-items-center sm:tw-p-0">
                <div x-show="open"
                     x-cloak
                     style="display: none;"
                     x-transition:enter="tw-ease-out tw-duration-300"
                     x-transition:enter-start="tw-opacity-0 tw-translate-y-4 sm:tw-translate-y-0 sm:tw-scale-95"
                     x-transition:enter-end="tw-opacity-100 tw-translate-y-0 sm:tw-scale-100"
                     x-transition:leave="tw-ease-in tw-duration-200"
                     x-transition:leave-start="tw-opacity-100 tw-translate-y-0 sm:tw-scale-100"
                     x-transition:leave-end="tw-opacity-0 tw-translate-y-4 sm:tw-translate-y-0 sm:tw-scale-95"
                     class="tw-pointer-events-auto tw-w-full tw-relative tw-transform tw-transition-all"
                >
                    {{ $slot }}
                </div>
            </div>
        </div>
    </div>
</div>
