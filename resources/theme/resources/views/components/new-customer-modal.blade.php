<div class="tw-reset">
    <div x-data="{ open: false }" x-init="setTimeout(() => open = true, 2500); $watch('open', value => { if(!value) setTimeout(() => $el.remove(), 250) })" class="tw-relative tw-z-40" aria-labelledby="modal-title" role="dialog" aria-modal="true">
        <!--
          Background backdrop, show/hide based on modal state.
        -->
        <div x-show="open"
             x-cloak
             style="display: none;"
             x-transition:enter="tw-ease-out tw-duration-300"
             x-transition:enter-start="tw-opacity-0"
             x-transition:enter-end="tw-opacity-100"
             x-transition:leave="tw-ease-in tw-duration-200"
             x-transition:leave-start="tw-opacity-100"
             x-transition:leave-end="tw-opacity-0"
             class="tw-fixed tw-inset-0 tw-bg-gray-500/75 tw-transition-opacity"
             aria-hidden="true"
        >
            <div>
                <div class="tw-flex tw-justify-end tw-p-4">
                    <button type="button" x-on:click="open = false" class="tw-rounded-md tw-text-white hover:tw-text-gray-300 hover:bg-gray-50 focus:tw-outline-none focus:tw-ring-2 focus:tw-ring-theme-brand-color focus:ring-tw-offset-2">
                        <span class="tw-sr-only">Close</span>
                        <svg class="tw-size-8" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" aria-hidden="true" data-slot="icon">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M6 18 18 6M6 6l12 12"/>
                        </svg>
                    </button>
                </div>
            </div>
        </div>

        <div class="tw-fixed tw-inset-0 tw-z-10 tw-w-screen tw-overflow-y-auto">
            <div class="tw-flex tw-min-h-full tw-items-end tw-justify-center tw-p-4 tw-text-center sm:tw-items-center sm:tw-p-0">
                <!--
                  Modal panel, show/hide based on modal state.
                -->
                <div x-show="open"
                     x-on:click.away="open = false"
                     x-cloak
                     style="display: none;"
                     x-transition:enter="tw-ease-out tw-duration-300"
                     x-transition:enter-start="tw-opacity-0 tw-translate-y-4 sm:tw-translate-y-0 sm:tw-scale-95"
                     x-transition:enter-end="tw-opacity-100 tw-translate-y-0 sm:tw-scale-100"
                     x-transition:leave="tw-ease-in tw-duration-200"
                     x-transition:leave-start="tw-opacity-100 tw-translate-y-0 sm:tw-scale-100"
                     x-transition:leave-end="tw-opacity-0 tw-translate-y-4 sm:tw-translate-y-0 sm:tw-scale-95"
                     class="tw-relative tw-transform tw-overflow-hidden tw-rounded-lg tw-bg-white tw-px-2 tw-pb-2 tw-pt-3 tw-text-left tw-shadow-xl tw-transition-all sm:tw-my-8 sm:tw-w-full sm:tw-max-w-sm sm:tw-p-3"
                >
                    <div>
                        <div id="tolstoy-container" style="line-height:0;overflow:hidden;height:100%;width:100%;text-align:center">
                            <iframe id="tolstoy" src="https://player.gotolstoy.com/hbn24yd6qgnkr?host"
                                    style="width:100%;height:560px;max-width:400px"
                                    scrolling="no" frameborder="0" allow="autoplay *; clipboard-write *;camera *; microphone *; encrypted-media *; fullscreen *; display-capture *;">
                            </iframe>
                            <script src="https://widget.gotolstoy.com/script.js" defer></script>
                        </div>
                        <p class="tw-m-0 tw-mt-2 tw-text-center tw-text-gray-500 tw-text-xs">0:40 seconds</p>
                    </div>

                </div>
            </div>
        </div>
    </div>

</div>
