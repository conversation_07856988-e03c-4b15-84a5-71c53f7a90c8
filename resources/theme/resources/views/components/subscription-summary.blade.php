@props(['subscription', 'divided' => false])

@php
    /** @var \App\Models\RecurringOrder $subscription */
@endphp
<dl class="tw-m-0 tw-space-y-4">
    <div class="tw-flex tw-items-center tw-justify-between">
        <dt class="tw-font-normal tw-text-sm tw-text-gray-600">Subtotal</dt>
        <dd class="tw-font-normal tw-text-sm tw-font-medium tw-text-gray-900">${{ money($subscription->subtotal()) }}</dd>
    </div>
    @if ($subscription->locationFeeTotal() > 0)
        <div class="tw-flex tw-items-center tw-justify-between {{ $divided ? 'tw-border-t tw-border-gray-200 tw-pt-4' : '' }}">
            <dt class="tw-font-normal tw-text-sm tw-text-gray-600">Other fees</dt>
            <dd class="tw-font-normal tw-text-sm tw-font-medium tw-text-gray-900">${{ money($subscription->locationFeeTotal()) }}</dd>
        </div>
    @endif
    @if ($subscription->taxTotal() > 0)
        <div class="tw-flex tw-items-center tw-justify-between {{ $divided ? 'tw-border-t tw-border-gray-200 tw-pt-4' : '' }}">
            <dt class="tw-font-normal tw-text-sm tw-text-gray-600">Taxes</dt>
            <dd class="tw-font-normal tw-text-sm tw-font-medium tw-text-gray-900">${{ money($subscription->taxTotal()) }}</dd>
        </div>
    @endif
    <div class="tw-flex tw-items-center tw-justify-between {{ $divided ? 'tw-border-t tw-border-gray-200 tw-pt-4' : '' }}">
        <dt class="tw-font-normal tw-text-sm tw-text-gray-600">Delivery</dt>
        <dd class="tw-font-normal tw-text-sm tw-font-medium tw-text-gray-900">${{ money($subscription->deliveryTotal()) }}</dd>
    </div>
    @if ($subscription->subscriptionSavingsTotal() > 0)
        <div class="tw-flex tw-items-center tw-justify-between {{ $divided ? 'tw-border-t tw-border-gray-200 tw-pt-4' : '' }}">
            <dt class="tw-font-normal tw-text-sm tw-text-gray-600">Subscription savings</dt>
            <dd class="tw-font-normal tw-text-sm tw-font-medium tw-text-gray-900">(${{ money($subscription->subscriptionSavingsTotal()) }})</dd>
        </div>
    @endif
    @if ($subscription->couponTotal() > 0)
        <div class="tw-flex tw-items-center tw-justify-between {{ $divided ? 'tw-border-t tw-border-gray-200 tw-pt-4' : '' }}">
            <dt class="tw-font-normal tw-text-sm tw-text-gray-600">Coupon</dt>
            <dd class="tw-font-normal tw-text-sm tw-font-medium tw-text-gray-900">(${{ money($subscription->couponTotal()) }})</dd>
        </div>
    @endif
    @if ($subscription->storeCreditTotal() > 0)
        <div class="tw-flex tw-items-center tw-justify-between {{ $divided ? 'tw-border-t tw-border-gray-200 tw-pt-4' : '' }}">
            <dt class="tw-font-normal tw-text-sm tw-text-gray-600">Store credit</dt>
            <dd class="tw-font-normal tw-text-sm tw-font-medium tw-text-gray-900">(${{ money($subscription->storeCreditTotal()) }})</dd>
        </div>
    @endif
</dl>
