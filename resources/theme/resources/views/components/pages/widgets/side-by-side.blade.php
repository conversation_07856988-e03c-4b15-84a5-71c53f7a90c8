@props(['widget'])

@php
    $padding_top = match ($widget['settings']['padding']['top'] ?? '') {
        'sm' => 'tw-pt-6 lg:tw-pt-10',
        'md' => 'tw-pt-12 lg:tw-pt-16',
        'lg' => 'tw-pt-24 lg:tw-pt-32',
        'xl' => 'tw-pt-32 lg:tw-pt-48',
        default => '',
    };

    $padding_bottom = match ($widget['settings']['padding']['bottom'] ?? '') {
        'sm' => 'tw-pb-6 lg:tw-pb-10',
        'md' => 'tw-pb-12 lg:tw-pb-16',
        'lg' => 'tw-pb-24 lg:tw-pb-32',
        'xl' => 'tw-pb-32 lg:tw-pb-48',
        default => '',
    };

    $max_width = match ($widget['settings']['max_width'] ?? '') {
        'sm' => 'tw-max-w-lg',
        'md' => 'tw-max-w-4xl',
        'lg' => 'tw-max-w-6xl',
        'xl' => 'tw-max-w-7xl',
        default => '',
    };
@endphp

<div @if(!empty($widget['settings']['html_id'] ?? '')) id="{{ $widget['settings']['html_id'] }}" @endif class="tw-relative tw-w-full" style="background-color: {{ $widget['settings']['background']['color'] ?? '#ffffff' }};">
    <div class="tw-px-6 sm:tw-px-6 lg:tw-px-8 {{ $padding_top }} {{ $padding_bottom }}">
        <div class="tw-relative tw-mx-auto {{ $max_width }}">
            <div class="tw-w-full tw-flex tw-flex-col tw-gap-16 tw-px-6 {{ ($widget['settings']['image_position'] ?? 'left') === 'right' ? 'tw-flex-col-reverse lg:tw-flex-row-reverse' : '' }} sm:tw-px-8 lg:tw-mx-0 lg:tw-flex-row lg:tw-items-center xl:tw-gap-x-20 xl:tw-px-20">
                <img class="tw-h-96 tw-w-full tw-flex-none tw-rounded-2xl tw-object-cover tw-shadow-xl lg:tw-aspect-square lg:tw-h-auto lg:tw-max-w-md" src="{{ $widget['settings']['image_url'] }}">
                <div class="tw-w-full tw-flex-auto tw-prose">
                    {!! $widget['settings']['text'] !!}
                </div>
            </div>
        </div>
    </div>
</div>
