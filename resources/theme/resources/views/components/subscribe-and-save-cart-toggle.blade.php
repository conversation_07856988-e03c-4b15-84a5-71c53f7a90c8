@props(['cart', 'subscription_settings_service'])
@php
    /** @var \App\Services\SubscriptionSettingsService $subscription_settings_service */
    /** @var \App\Contracts\Cartable $cart */
    $percentage_incentive = $subscription_settings_service->discountIncentive();
    $has_product_incentive = $subscription_settings_service->hasProductIncentive();
@endphp

<div class="tw-bg-gray-50 tw-cursor-pointer hover:tw-bg-gray-100" wire:click="toggleSubscription">
    <div class="tw-border-l-4 @if($cart->isRecurring()) tw-border-theme-brand-color @else tw-border-transparent @endif  tw-px-6 tw-py-4">
        <div class="tw-relative tw-flex tw-items-start">
            <div class="tw-flex tw-h-6">
                <input id="recurring-opt-in" wire:model="is_subscribing" @click.stop wire:change="toggleSubscription" aria-describedby="recurring-opt-in-description" name="recurring-opt-in" type="checkbox" class="tw-mt-1 tw-h-4 tw-w-4 tw-cursor-pointer tw-rounded tw-border-gray-300 tw-text-theme-action-color focus:tw-outline-none focus:tw-ring-2 focus:tw-ring-theme-action-color focus:tw-ring-offset-2">
            </div>
            <div class="tw-ml-3 tw-w-full">
                <label for="recurring-opt-in" @click.stop class="tw-m-0 tw-cursor-pointer tw-font-medium tw-leading-6 tw-text-base tw-text-gray-900">
                    Subscribe @if($percentage_incentive > 0)
                        +
                        <span class="tw-text-theme-action-color"> Save
                            ${{ money($cart->cartPotentialSubscriptionSavingsTotal() + $cart->cartPotentialSubscriptionProductValue()) }}</span>
                    @endif
                </label>

                <p id="recurring-opt-in-description" class="tw-m-0 tw-text-gray-500 tw-text-sm">
                    Save 5% + Get a Free item on every order
                </p>
            </div>

            <div class="absolute top-3 right-3 ">
                @if( ! $cart->isRecurring())
                    <svg id="subscription-exclamation" class="tw-text-gray-500 tw-w-6 tw-h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M12 9v3.75m9-.75a9 9 0 11-18 0 9 9 0 0118 0zm-9 3.75h.008v.008H12v-.008z"/>
                    </svg>
                @else
                    <svg id="subscription-check" class="tw-text-theme-brand-color tw-w-6 tw-h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                    </svg>
                @endif
            </div>
        </div>
    </div>
</div>
