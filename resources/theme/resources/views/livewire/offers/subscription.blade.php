@php
    /** @var \App\Contracts\Cartable $cart */
    /** @var \App\Services\SubscriptionSettingsService $subscription_settings */
    $total_savings = $cart->cartPotentialSubscriptionSavingsTotal() + $selected_product_incentive_value;
@endphp
<div>
    @if(now()->month === 1)
        <div class="tw-max-w-6xl tw-mx-auto tw-border-b tw-border-gray-200  tw-mb-4">
            <div class="tw-text-center tw-py-3 tw-px-6 lg:tw-px-8">
                <p class="tw-m-0 tw-w-full tw-block tw-text-lg tw-text-theme-action-color tw-font-semibold lg:tw-text-2xl">
                    Claim Your <span class="tw-font-bold">DOUBLE FREEBIE</span>
                </p>
                <p class="tw-m-0 tw-w-full tw-block tw-text-sm tw-text-gray-900 tw-font-semibold lg:tw-text-lg">
                    Offer Ends January 31st
                </p>
            </div>
        </div>
    @endif
    <div class="tw-max-w-6xl tw-mx-auto">
        <div class="tw-mt-2 tw-mb-6 tw-relative tw-isolate tw-bg-white tw-px-6 lg:tw-px-8">

            <div class="tw-absolute tw-inset-x-0 tw--top-3 tw--z-10 tw-transform-gpu tw-overflow-hidden tw-px-36 tw-blur-3xl" aria-hidden="true">
                <div class="tw-mx-auto tw-aspect-[1155/678] tw-w-[72.1875rem] tw-bg-gradient-to-tr tw-from-[#ff80b5] tw-to-[#d9534f] tw-opacity-30" style="clip-path: polygon(74.1% 44.1%, 100% 61.6%, 97.5% 26.9%, 85.5% 0.1%, 80.7% 2%, 72.5% 32.5%, 60.2% 62.4%, 52.4% 68.1%, 47.5% 58.3%, 45.2% 34.5%, 27.5% 76.7%, 0.1% 64.9%, 17.9% 100%, 27.6% 76.8%, 76.1% 97.7%, 74.1% 44.1%)"></div>
            </div>
            <div class="tw-mx-auto tw-grid tw-max-w-lg tw-grid-cols-1 tw-items-start tw-gap-y-6 sm:tw-mt-12 sm:tw-gap-y-0 lg:tw-max-w-4xl lg:tw-grid-cols-2">
                <div class="tw-relative tw-bg-white lg:tw-rounded-3xl lg:tw-shadow-2xl lg:tw-ring-1 lg:tw-ring-gray-900/10 lg:tw-p-10">

                    <p class="tw-text-[1.8rem] tw-font-bold tw-tracking-tight tw-text-gray-900 sm:tw-text-3xl">
                        Subscribe & Save
                        <span class="tw-text-theme-brand-color">
                            <span> ${{ money($total_savings) }}</span>
                        </span>
                    </p>
                    <p class="tw-mt-4 tw-text-gray-600 tw-text-sm">
                        New Total
                    </p>
                    <p class="tw-mt-1 tw-flex tw-items-baseline tw-gap-x-2">
                        <span class="tw-text-4xl tw-font-bold tw-tracking-tight tw-text-gray-900">
                            ${{ money($cart->cartSubtotal() - $cart->cartPotentialSubscriptionSavingsTotal()) }}
                        </span>
                        <span class="tw-text-xl tw-line-through tw-text-theme-action-color">${{ money($cart->cartSubtotal()) }}</span>
                    </p>
                    <p class="tw-m-0 tw-mt-1 tw-text-xs tw-text-gray-600">
                        Includes Member's Only 5% Site-wide Savings
                    </p>
                    <div class="tw-w-full tw-flex-auto tw-grid tw-gap-x-3">
                        <div class="tw-mt-4">
                            <div class="tw-flex tw-justify-between">
                                <label for="email" class="tw-block tw-text-sm tw-font-medium tw-leading-6 tw-text-gray-900">
                                    + Select Your
                                    @if(now()->month === 1)
                                        <span class="tw-text-theme-brand-color tw-font-semibold">DOUBLE FREE ITEM</span>
                                    @else
                                        <span class="tw-text-theme-brand-color tw-font-semibold">FREE ITEM</span>
                                    @endif
                                </label>
                                <span class="tw-text-sm tw-leading-6 tw-text-gray-500" id="email-optional">
                                    ${{ money($selected_product_incentive_value) }} value
                                </span>
                            </div>
                            <div class="tw-relative tw-mt-1 tw-rounded-md tw-shadow-sm">
                                <div class="tw-pointer-events-none tw-absolute tw-inset-y-0 tw-left-0 tw-flex tw-items-center tw-pl-3">
                                    <svg class="tw-size-5 tw-text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M21 11.25v8.25a1.5 1.5 0 0 1-1.5 1.5H5.25a1.5 1.5 0 0 1-1.5-1.5v-8.25M12 4.875A2.625 2.625 0 1 0 9.375 7.5H12m0-2.625V7.5m0-2.625A2.625 2.625 0 1 1 14.625 7.5H12m0 0V21m-8.625-9.75h18c.621 0 1.125-.504 1.125-1.125v-1.5c0-.621-.504-1.125-1.125-1.125h-18c-.621 0-1.125.504-1.125 1.125v1.5c0 .621.504 1.125 1.125 1.125Z"/>
                                    </svg>
                                </div>
                                <label for="selected_product_id" class="tw-sr-only">Free product</label>
                                <select id="selected_product_id" name="selected_product_id" wire:model.live="selected_product_id" class="tw-block tw-w-full tw-rounded-md tw-border-0 tw-py-1.5 tw-pl-10 tw-pr-10 tw-text-gray-900 tw-ring-1 tw-ring-inset tw-ring-gray-300 focus:tw-ring-2 focus:tw-ring-theme-action-color/80 sm:tw-text-sm sm:tw-leading-6">
                                    @foreach($product_incentives as $product)
                                        @php /** @var \App\Models\Product $product */ @endphp
                                        <option value="{{ $product->id }}">{{ str($product->title)->replace('-', '')->trim() }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>

                        <div class="tw-mt-6">
                            <div class="tw-flex tw-justify-between">
                                <label for="email" class="tw-block tw-text-sm tw-font-medium tw-leading-6 tw-text-gray-900">& Choose Your Frequency</label>
                                <span wire:loading.remove wire:target="selected_frequency" class="tw-text-sm tw-leading-6 tw-text-gray-500" id="email-optional">
                                    Save ${{ number_format(round($total_savings * [7 => 52, 14 => 26, 28 => 12, 42 => 9, 56 => 6][$selected_frequency] / 100)) }} / year
                                </span>
                            </div>
                            <div class="tw-relative tw-mt-1 tw-rounded-md tw-shadow-sm">
                                <div class="tw-pointer-events-none tw-absolute tw-inset-y-0 tw-left-0 tw-flex tw-items-center tw-pl-3">
                                    <svg class="tw-size-5 tw-text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M8.25 18.75a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m3 0h6m-9 0H3.375a1.125 1.125 0 0 1-1.125-1.125V14.25m17.25 4.5a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m3 0h1.125c.621 0 1.129-.504 1.09-1.124a17.902 17.902 0 0 0-3.213-9.193 2.056 2.056 0 0 0-1.58-.86H14.25M16.5 18.75h-2.25m0-11.177v-.958c0-.568-.422-1.048-.987-1.106a48.554 48.554 0 0 0-10.026 0 1.106 1.106 0 0 0-.987 1.106v7.635m12-6.677v6.677m0 4.5v-4.5m0 0h-12"/>
                                    </svg>
                                </div>

                                <select id="selected_frequency" name="selected_frequency" wire:model.live="selected_frequency" class="tw-block tw-w-full tw-rounded-md tw-border-0 tw-py-1.5 tw-pl-10 tw-pr-10 tw-text-gray-900 tw-ring-1 tw-ring-inset tw-ring-gray-300 focus:tw-ring-2 focus:tw-ring-theme-action-color/80 sm:tw-text-sm sm:tw-leading-6">
                                    @foreach($frequency_options as $frequency_option)
                                        <option value="{{ $frequency_option['value'] }}">{{ $frequency_option['label'] }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>

                    </div>
                    <button wire:click="startSubscription" aria-describedby="tier-personal" class="tw-mt-8 tw-w-full tw-block tw-rounded-md tw-bg-theme-brand-color tw-px-3.5 tw-py-2.5 tw-text-center tw-text-base tw-leading-6 tw-font-semibold tw-text-white tw-shadow hover:tw-bg-theme-brand-color/75 focus-visible:tw-outline focus-visible:tw-outline-2 focus-visible:tw-outline-offset-2 focus-visible:tw-outline-theme-brand-color/60">
                        <span wire:loading.remove wire:target="startSubscription">
                            Subscribe to My Order
                        </span>
                        <span wire:loading.inline wire:target="startSubscription">
                            Updating
                        </span>
                    </button>
                    <p class="tw-mt-6 tw-text-lg tw-leading-7 tw-font-bold tw-text-gray-700">Additional Benefits</p>
                    <ul role="list" class="tw-mt-4 tw-space-y-3 tw-text-base tw-leading-6 tw-text-gray-600 sm:tw-mt-6">

                        <li class="tw-flex tw-gap-x-2">
                            <svg class="tw-h-6 tw-w-5 tw-flex-none tw-text-theme-brand-color" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                <path fill-rule="evenodd" d="M16.704 4.153a.75.75 0 01.143 1.052l-8 10.5a.75.75 0 01-1.127.075l-4.5-4.5a.75.75 0 011.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 011.05-.143z" clip-rule="evenodd"></path>
                            </svg>
                            Pause or Stop Subscription Anytime
                        </li>
                        <li class="tw-flex tw-gap-x-2">
                            <svg class="tw-h-6 tw-w-5 tw-flex-none tw-text-theme-brand-color" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                <path fill-rule="evenodd" d="M16.704 4.153a.75.75 0 01.143 1.052l-8 10.5a.75.75 0 01-1.127.075l-4.5-4.5a.75.75 0 011.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 011.05-.143z" clip-rule="evenodd"></path>
                            </svg>
                            Receive Text Message Reminders
                        </li>
                        <li class="tw-flex tw-gap-x-2">
                            <svg class="tw-h-6 tw-w-5 tw-flex-none tw-text-theme-brand-color" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                <path fill-rule="evenodd" d="M16.704 4.153a.75.75 0 01.143 1.052l-8 10.5a.75.75 0 01-1.127.075l-4.5-4.5a.75.75 0 011.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 011.05-.143z" clip-rule="evenodd"></path>
                            </svg>
                            Free to Join & No Fees to Cancel
                        </li>
                    </ul>
                </div>
                <div class="tw-mt-4 tw-border tw-border-gray-200 lg:tw-hidden"></div>

                <div class="tw-bg-white/60 tw-pb-24 sm:tw-mx-8 lg:tw-ring-1 lg:tw-ring-gray-900/10 lg:tw-rounded-t-none lg:tw-p-10 lg:tw-mt-16 lg:tw-mx-0 lg:tw-rounded-bl-none lg:tw-rounded-tr-3xl lg:tw-rounded-br-3xl">
                    <p class="tw-mt-2 tw-text-[1.8rem] tw-font-bold tw-tracking-tight tw-text-gray-900 sm:tw-text-3xl">
                        One-Time Purchase
                    </p>
                    <p class="tw-mt-4 tw-flex tw-items-baseline tw-gap-x-2">
                        <span class="tw-text-4xl tw-font-bold tw-tracking-tight tw-text-gray-900">
                            ${{ money($cart->cartSubtotal()) }}
                        </span>
                    </p>
                    <p class="tw-m-0 tw-mt-1 tw-text-xs tw-text-gray-600">No addtional savings</p>
                    {{--                <p class="tw-mt-6 tw-text-sm tw-leading-7 tw-text-gray-600">Not ready to commit? No problem. We're still happy to ship your order.</p>--}}
                    <ul role="list" class="tw-mt-4 tw-space-y-3 tw-text-base tw-leading-6 tw-text-gray-600 sm:tw-mt-6">
                        <li class="tw-flex tw-items-center tw-gap-x-3">
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="tw-h-6 tw-w-5 tw-flex-none tw-text-gray-600">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M6 18 18 6M6 6l12 12"/>
                            </svg>
                            5% savings storewide
                        </li>
                        <li class="tw-flex tw-items-center tw-gap-x-3">
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="tw-h-6 tw-w-5 tw-flex-none tw-text-gray-600">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M6 18 18 6M6 6l12 12"/>
                            </svg>
                            Free item with every order
                        </li>
                    </ul>
                    <button wire:click="shipOnce" aria-describedby="one-time-purchase" class="tw-mt-8 tw-w-full tw-block tw-rounded-md tw-bg-theme-brand-color tw-px-3.5 tw-py-2.5 tw-text-center tw-text-base tw-leading-6 tw-font-semibold tw-text-white tw-shadow hover:tw-bg-theme-brand-color/75 focus-visible:tw-outline focus-visible:tw-outline-2 focus-visible:tw-outline-offset-2 focus-visible:tw-outline-theme-brand-color/60">
                        <span wire:loading.remove wire:target="shipOnce">
                            Ship Once
                        </span>
                        <span wire:loading.inline wire:target="shipOnce">
                            Updating
                        </span>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

@script
<script>
    if (typeof gtag === 'function') {
        gtag('event', 'view_promotion', {
            creative_name: 'subscribe_and_save_before_checkout',
            creative_slot: 'before_checkout',
            promotion_name: 'Subscribe & Save',
            promotion_id: 'subscribe_and_save',
            items: @js($cart->itemsInCart()
                ->map(function (\App\Cart\Item $item, $index) {
                    return [
                        'item_id' => $item->product->id,
                        'item_name' => $item->product->title,
                        'index' => $index,
                        'item_category' => $item->product->category?->parentCategory ? $item->product->category->parentCategory->name : $item->product->category?->name,
                        'item_category2' => $item->product->category?->parentCategory ? $item->product->category->name : null,
                        'item_list_id' => null,
                        'item_list_name' => null,
                        'price' => $item->price(),
                        'quantity' => $item->quantity
                    ];
                })
                ->toArray())
        });
    }
</script>
@endscript
