<div x-data="{ open: @entangle('open') }"
     x-init="$watch('open', value => $dispatch(value === true ? 'modal-panel-opened' : 'modal-panel-closed'))"
     x-show="open"
     x-cloak
     class="tw-reset"
     aria-labelledby="slide-over-title"
     x-ref="dialog"
     aria-modal="true"
     @click.stop=""
>
    <div class="tw-relative tw-z-[1000]" aria-labelledby="modal-title" role="dialog" aria-modal="true">
        <div x-show="open"
             x-cloak
             style="display: none;"
             x-transition:enter="tw-ease-in-out tw-duration-500"
             x-transition:enter-start="tw-opacity-0"
             x-transition:enter-end="tw-opacity-100"
             x-transition:leave="tw-ease-in-out tw-duration-500"
             x-transition:leave-start="tw-opacity-100"
             x-transition:leave-end="tw-opacity-0"

             class="tw-fixed tw-inset-0 tw-bg-gray-500 tw-bg-opacity-75 tw-transition-opacity"
        ></div>

        <div class="tw-fixed tw-inset-0 tw-z-10 tw-overflow-y-auto tw-pointer-events-none">
            <div class="tw-flex tw-min-h-full tw-justify-center tw-text-center tw-items-center tw-p-0">
                <div
                    x-show="open"
                    x-cloak
                    style="display: none;"
                    x-transition:enter="tw-ease-out tw-duration-300"
                    x-transition:enter-start="tw-opacity-0 tw-translate-y-0 tw-scale-95"
                    x-transition:enter-end="tw-opacity-100 tw-translate-y-0 tw-scale-100"
                    x-transition:leave="tw-ease-in tw-duration-200"
                    x-transition:leave-start="tw-opacity-100 tw-translate-y-0 tw-scale-100"
                    x-transition:leave-end="tw-opacity-0 tw-translate-y-0 tw-scale-95"
                    class="tw-relative tw-pointer-events-auto tw-transform tw-overflow-hidden tw-rounded-lg tw-bg-white tw-px-4 tw-pt-5 tw-pb-4 tw-text-left tw-shadow-xl tw-transition-all sm:tw-my-8 sm:tw-w-full sm:tw-max-w-lg sm:tw-p-6"
                >
                    <div class="tw-absolute tw-top-0 tw-right-0 tw-hidden tw-pt-4 tw-pr-4 sm:tw-block">
                        <button type="button" @click.stop="open = false" class="tw-rounded-md tw-bg-white tw-text-gray-400 hover:tw-text-gray-500 focus:tw-outline-none focus:tw-ring-2 focus:tw-ring-theme-action-color/50 focus:tw-ring-offset-2">
                            <span class="tw-sr-only">Close</span>
                            <svg class="tw-h-6 tw-w-6" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        </button>
                    </div>
                    <div class="sm:tw-flex sm:tw-items-start">
                        <div class="tw-mx-auto tw-flex tw-h-12 tw-w-12 tw-flex-shrink-0 tw-items-center tw-justify-center tw-rounded-full tw-bg-red-100 sm:tw-mx-0 sm:tw-h-10 sm:tw-w-10">
                            <svg class="tw-h-6 tw-w-6 tw-text-red-600" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z" />
                            </svg>
                        </div>
                        <div class="tw-mt-3 tw-text-center sm:tw-mt-0 sm:tw-ml-4 sm:tw-text-left">
                            <h3 class="tw-text-base tw-font-semibold tw-leading-6 tw-text-gray-900" id="modal-title">{{ $title }}</h3>
                            <div class="tw-mt-2">
                                <p class="tw-text-sm tw-text-gray-500">{{ $message }}</p>
                            </div>
                        </div>
                    </div>
                    <div class="tw-mt-5 sm:tw-mt-4 sm:tw-flex sm:tw-flex-row-reverse">
{{--                            <button type="button" class="tw-inline-flex tw-w-full tw-justify-center tw-rounded-md tw-bg-red-600 tw-px-3 tw-py-2 tw-text-sm tw-font-semibold tw-text-white tw-shadow-sm hover:tw-bg-red-500 sm:tw-ml-3 sm:tw-w-auto">Deactivate</button>--}}
                        <button @click.stop="open = false" type="button" class="tw-mt-3 tw-inline-flex tw-w-full tw-justify-center tw-rounded-md tw-bg-white tw-px-3 tw-py-2 tw-text-sm tw-font-semibold tw-text-gray-900 tw-shadow-sm tw-ring-1 tw-ring-inset tw-ring-gray-300 hover:tw-bg-gray-50 sm:tw-mt-0 sm:tw-w-auto">Cancel</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
