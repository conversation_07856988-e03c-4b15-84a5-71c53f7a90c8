@php
    /**
     * @var \App\Models\RecurringOrder $subscription
     * @var \App\Models\Pickup|null $delivery_method
     * @var \App\Services\SubscriptionSettingsService $subscription_settings_service
     */

    $in_transit_order = null;
    $current_order = $subscription->currentOrder;

    if ( ! is_null($current_order) && $current_order->deadlineDatetime()->isPast()) {
        $in_transit_order = $current_order;
    }

    if (is_null($in_transit_order) && ! is_null($current_order) && ! $current_order->isNew()) {
        $in_transit_order = $current_order;
    }

    if ( ! is_null($in_transit_order)) {
        $next_deadline_date = $in_transit_order->deadlineDatetime();
        $next_delivery_date = $in_transit_order->pickupDatetime();

        $order_window = $subscription->nextOrderWindow();
        $upcoming_delivery_date = $order_window?->deliveryDatetime();
        $upcoming_deadline_date = $order_window?->deadlineDatetime();
    } elseif ( ! is_null($current_order)) {
        $next_deadline_date = $current_order->deadlineDatetime();
        $next_delivery_date = $current_order->pickupDatetime();

        $order_window = $subscription->nextOrderWindow($next_delivery_date);
        $upcoming_delivery_date = $order_window?->deliveryDatetime();
    } else {
        $order_window = $subscription->nextOrderWindow();
        $next_deadline_date = $order_window?->deadlineDatetime();
        $next_delivery_date = $order_window?->deliveryDatetime();

        $order_window = $subscription->nextOrderWindow($next_delivery_date);
        $upcoming_delivery_date = $order_window?->deliveryDatetime();
    }
@endphp

<div x-data="{ currentTab: 'next' }" class="tw-h-full tw-bg-white tw-flex tw-flex-col tw-pointer-events-auto tw-w-screen tw-max-w-md">
    <div class="tw-flex-1 tw-overflow-hidden tw-flex tw-flex-col tw-bg-white tw-shadow-xl">
        <div class="tw-flex-1 tw-overflow-y-auto tw-py-6 tw-px-4 sm:tw-px-6">
            <div>
                <div class="tw-w-full tw-flex tw-items-center tw-justify-between tw-text-theme-brand-color">
                    <div class="tw--ml-2 tw--mt-2 tw-flex tw-flex-wrap tw-items-baseline">
                        <h2 class="tw-m-0 tw-ml-2 tw-mt-2 tw-text-xl tw-font-body tw-font-medium" id="slide-over-title">Subscription</h2>
                        <p class="tw-m-0 tw-ml-2 tw-mt-1  tw-truncate tw-text-sm tw-text-gray-500">
                            repeats @if(in_array($subscription->reorder_frequency, [14,56]))
                                every
                            @endif {{ str(\App\Models\Schedule::$reorderFrequencies[$subscription->reorder_frequency])->lower() }}</p>
                    </div>
                    <div class="tw-ml-3 tw-flex tw-h-7 tw-items-center">
                        <button type="button" @click="open = false" class="tw--m-2 tw-p-2 tw-text-theme-brand-color/80 hover:tw-text-theme-brand-color/60">
                            <span class="tw-sr-only">Close panel</span>
                            <svg class="tw-h-6 tw-w-6" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        </button>
                    </div>
                </div>

                <div>
                    <!-- Start Order tabs -->
                    <div class="tw-mt-6 tw-border-b tw-border-gray-200">

                        <div class="sm:tw-flex sm:tw-items-baseline">
                            <div class="">
                                <nav class="tw--mb-px tw-flex tw-space-x-8">
                                    <a
                                            @click="currentTab = 'next'"
                                            class="tw-border-theme-brand-color/80 tw-text-theme-brand-color tw-no-underline tw-cursor-pointer tw-border-b-2 tw-px-1 tw-pb-4 tw-text-sm tw-font-medium"
                                            :class="{
                                                'tw-border-theme-brand-color/80 tw-text-theme-brand-color': currentTab === 'next',
                                                'tw-border-transparent tw-text-gray-500 hover:tw-border-gray-300 hover:tw-text-gray-700': currentTab === 'upcoming',
                                            }"
                                            :aria-current="currentTab === 'next' ? 'page' : false"
                                    >
                                        {{ $next_delivery_date?->format('M jS, Y') ?? ($delivery_method->isDeliveryZone() ? 'Next delivery' : 'Next pickup') }}
                                    </a>
                                    <a
                                            @click="currentTab = 'upcoming'"
                                            class="tw-border-transparent tw-text-gray-500 hover:tw-border-gray-300 hover:tw-text-gray-700 tw-no-underline tw-cursor-pointer tw-border-b-2 tw-px-1 tw-pb-4 tw-text-sm tw-font-medium"
                                            :class="{
                                                    'tw-border-theme-brand-color/80 tw-text-theme-brand-color': currentTab === 'upcoming',
                                                    'tw-border-transparent tw-text-gray-500 hover:tw-border-gray-300 hover:tw-text-gray-700': currentTab === 'next',
                                                }"
                                            :aria-current="currentTab === 'upcoming' ? 'page' : false"
                                    >
                                        {{ $upcoming_delivery_date?->format('M jS, Y') ?? ($delivery_method->isDeliveryZone() ? 'Upcoming delivery' : 'Upcoming pickup') }}
                                    </a>
                                </nav>
                            </div>
                        </div>
                    </div>
                    <!-- End Order tabs -->
                </div>

                <div x-show="currentTab === 'next'">
                    <div>
                        <div class="tw-mt-4 tw-space-y-2">
                            <div class="tw-flex tw-items-center tw-text-sm tw-text-gray-500">
                                @if( ! is_null($in_transit_order))
                                    <p class="tw-m-0 tw-text-sm tw-text-gray-700">Order processed</p>
                                @elseif( ! is_null($current_order))
                                    <p class="tw-m-0 tw-text-sm tw-text-gray-700">Order placed</p>
                                @else
                                    <p class="tw-m-0 tw-text-sm tw-text-gray-700">Order scheduled</p>
                                @endif
                            </div>
                            <div class="tw-flex tw-items-center tw-text-sm tw-text-gray-500">
                                @if ( ! is_null($in_transit_order))
                                    {{ $delivery_method->isDeliveryZone() ? 'Delivery' : 'Pickup' }}
                                    on {{ $in_transit_order->pickupDatetime()?->format('M jS') ?? 'TBA' }}
                                @else
                                    {{ $delivery_method->isDeliveryZone() ? 'Delivery' : 'Pickup' }} on {{ $next_delivery_date?->format('M jS') ?? 'TBA' }}
                                @endif

                                @if (is_null($in_transit_order))
                                    <a href="{{ route('customers.recurring.edit') }}" class="tw-ml-1.5 tw-no-underline tw-text-theme-action-color hover:tw-text-theme-action-color/70">
                                        <svg class="tw-h-4 tw-w-4 tw-flex-shrink-0 " xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" d="M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L10.582 16.07a4.5 4.5 0 01-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 011.13-1.897l8.932-8.931zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0115.75 21H5.25A2.25 2.25 0 013 18.75V8.25A2.25 2.25 0 015.25 6H10" />
                                        </svg>
                                    </a>
                                @endif
                            </div>
                            @if($in_transit_order?->deadlineHasPassed() ?? true)
                                <div class="tw-mt-2 tw-flex tw-items-center tw-text-sm tw-text-gray-500">
                                    @if ( ! is_null($in_transit_order))
                                        Edits ended {{ $next_deadline_date?->format('M d | h:iA') ?? 'TBA' }}
                                    @else
                                        Edit until {{ $next_deadline_date?->format('M d | h:iA') ?? 'TBA' }}
                                    @endif
                                </div>
                            @endif
                        </div>
                    </div>

                    <div class="tw-mt-6 tw-flow-root">
                        @if (!is_null($current_order))
                            <livewire:theme.order-items :order="$current_order" />
                        @else
                            <livewire:theme.subscription-items :subscription="$subscription" />
                        @endif
                    </div>
                </div>

                <div x-show="currentTab === 'upcoming'">
                    <div>

                        <div class="tw-mt-4 tw-space-y-2">
                            <div class="tw-flex tw-items-center tw-text-sm tw-text-gray-500">
                                <p class="tw-m-0 tw-text-sm tw-text-gray-700">Order scheduled</p>
                            </div>
                            <div class="tw-flex tw-items-center tw-text-sm tw-text-gray-500">
                                {{ $delivery_method->isDeliveryZone() ? 'Delivery' : 'Pickup' }} on {{ $upcoming_delivery_date?->format('M jS') ?? 'TBA' }}
                                <a href="{{ route('customers.recurring.edit') }}" class="tw-ml-1.5 tw-no-underline tw-text-theme-action-color hover:tw-text-theme-action-color/70">
                                    <svg class="tw-h-4 tw-w-4 tw-flex-shrink-0 " xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L10.582 16.07a4.5 4.5 0 01-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 011.13-1.897l8.932-8.931zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0115.75 21H5.25A2.25 2.25 0 013 18.75V8.25A2.25 2.25 0 015.25 6H10" />
                                    </svg>
                                </a>
                            </div>
                            <div class="tw-mt-2 tw-flex tw-items-center tw-text-sm tw-text-gray-500">
                                @if( ! is_null($in_transit_order) && ! $in_transit_order->deadlineHasPassed() && ! $in_transit_order->isNew())
                                    Edit starting {{ $next_deadline_date?->copy()->addSecond()->startOfHour()->format('M d | h:iA') ?? 'TBA' }}
                                @elseif( ! is_null($in_transit_order))
                                    Edit until {{ $upcoming_deadline_date?->copy()->addSecond()->startOfHour()->format('M d | h:iA') ?? 'TBA' }}
                                @else
                                    Edit starting {{ $next_deadline_date?->copy()->addSecond()->startOfHour()->format('M d | h:iA') ?? 'TBA' }}
                                @endif
                            </div>
                        </div>
                    </div>

                    <div class="tw-mt-6 tw-flow-root">
                        <livewire:theme.subscription-items :subscription="$subscription" :can_be_modified=" ! is_null($in_transit_order) && $in_transit_order->deadlineHasPassed()" />
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="tw-bg-white">
        @if($delivery_method?->display_cart_shipping_calculator && $delivery_method->deliveryFeeHasCap())
            <div x-show="currentTab === 'next'" class="tw-border-t tw-border-gray-200">
                @if ( ! is_null($current_order))
                    <x-theme::free-shipping-calculator
                            :order="$current_order"
                            :delivery_method="$delivery_method"
                    />
                @else
                    <x-theme::free-shipping-calculator
                            :subscription="$subscription"
                            :delivery_method="$delivery_method"
                    />
                @endif

            </div>

            <div x-show="currentTab === 'upcoming'" class="tw-border-t tw-border-gray-200">
                <x-theme::free-shipping-calculator
                        :subscription="$subscription"
                        :delivery_method="$delivery_method"
                />
            </div>
        @endif

        <div class="tw-relative tw-border-t tw-px-4 sm:tw-px-6 " x-data="{ is_showing_total_details: false }">
            <div
                    x-show="is_showing_total_details"
                    x-cloak
                    x-transition:enter="tw-transform tw-transition tw-ease-in-out tw-duration-500 sm:tw-duration-700"
                    x-transition:enter-start="tw-translate-y-full"
                    x-transition:enter-end="tw-translate-y-0"
                    x-transition:leave="tw-transform tw-transition tw-ease-in-out tw-duration-500 sm:tw-duration-700"
                    x-transition:leave-start="tw-translate-y-0"
                    x-transition:leave-end="tw-translate-y-full"
                    class="tw-pt-4"
            >

                <div x-show="currentTab === 'next'">
                    @if ( ! is_null($current_order))
                        <x-theme::order-summary :order="$current_order" />
                    @else
                        <x-theme::subscription-summary :subscription="$subscription" />
                    @endif
                </div>
                <div x-show="currentTab === 'upcoming'">
                    <x-theme::subscription-summary :subscription="$subscription" />
                </div>

            </div>
            <div class="tw-relative tw-z-10 tw-bg-white tw-border-gray-200 tw-cursor-pointer tw-pb-6 hover:tw-text-gray-700">
                <div @click="is_showing_total_details = ! is_showing_total_details">
                    <div class="tw-pt-6 tw-flex tw-justify-between">
                        <div class="tw-flex tw-items-center tw-space-x-2 tw-text-base tw-font-medium tw-text-gray-900">
                            <p class="tw-m-0">Total</p>
                            <svg x-show=" ! is_showing_total_details" id="total-chevron-down" class="tw-w-5 tw-h-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M5.23 7.21a.75.75 0 011.06.02L10 11.168l3.71-3.938a.75.75 0 111.08 1.04l-4.25 4.5a.75.75 0 01-1.08 0l-4.25-4.5a.75.75 0 01.02-1.06z" clip-rule="evenodd" />
                            </svg>
                            <svg x-show="is_showing_total_details" id="total-chevron-up" class="tw-w-5 tw-h-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M14.77 12.79a.75.75 0 01-1.06-.02L10 8.832 6.29 12.77a.75.75 0 11-1.08-1.04l4.25-4.5a.75.75 0 011.08 0l4.25 4.5a.75.75 0 01-.02 1.06z" clip-rule="evenodd" />
                            </svg>
                        </div>

                        <p x-show="currentTab === 'next'" class="tw-m-0">
                            @if ( ! is_null($current_order))
                                ${{ money($current_order->total) }}
                            @else
                                ${{ money($subscription->total()) }}
                            @endif
                        </p>

                        <p x-show="currentTab === 'upcoming'" class="tw-m-0">
                            ${{ money($subscription->total()) }}
                        </p>
                    </div>
                    @if (is_null($current_order))
                        <p class="tw-mt-0.5 tw-text-sm tw-text-gray-500">Taxes and fees finalized at deadline</p>
                    @endif
                </div>

                <div class="tw-pt-6 tw-space-y-6">
                    <a href="{{ route('customers.recurring.edit') }}" class="tw-no-underline">
                        <button type="button" class="tw-w-full tw-flex tw-items-center tw-justify-center tw-rounded-md tw-border tw-border-transparent tw-bg-theme-action-color tw-px-6 tw-py-3 tw-text-base tw-font-medium tw-text-white tw-shadow-sm hover:tw-bg-theme-action-color/70">
                            Manage subscription
                        </button>
                    </a>
                    <div class="tw-mt-6 tw-flex tw-justify-center tw-text-center tw-text-sm tw-text-gray-500">
                        <p class="tw-m-0">
                            <button type="button" @click="open = false" class="tw-font-medium tw-text-theme-action-color hover:tw-text-theme-action-color/70">
                                {{ __('messages.cart.keep_shopping') }}
                                <span aria-hidden="true"> &rarr;</span>
                            </button>
                        </p>
                    </div>

                </div>
            </div>
        </div>
    </div>
</div>

