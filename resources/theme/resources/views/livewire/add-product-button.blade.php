@php /** @var App\Models\Product $product */ @endphp
@php /** @var string $style */ @endphp
@php /** @var bool|null $rounded */ @endphp

<button
        class="tw-block tw-w-full @if($cta_classes ?? false) {{ $cta_classes }} @else tw-px-3 tw-py-2 tw-rounded-none tw-rounded-b-lg tw-text-sm tw-font-semibold @endif  btn {{ $style }}  productListing__addToCartButton--grid"
        type="button"
        wire:loading.attr="disabled"
        wire:click="add"
>
    <span wire:loading.remove>
        @if($has_subscription)
            <span></span>
            {{ $cta_label ?? $product->orderActionLabel(true) }}
        @elseif($has_order)
            {{ $cta_label ??  $product->orderActionLabel() }}
        @else
            {{ $cta_label ?? $product->cartActionLabel() }}
        @endif
    </span>
    <span wire:loading style="display:none;">Adding...</span>
</button>

