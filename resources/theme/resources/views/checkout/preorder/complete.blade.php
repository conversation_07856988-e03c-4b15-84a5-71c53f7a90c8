@extends('theme::_layouts.main', [
    'pageTitle' => 'Your Order Has Been Submitted'
])

@section('head')
    <meta property="og:type" content="website"/>
    <meta property="og:site_name" content="{{ setting('farm_name') }}"/>
    <meta property="og:url" content="{{ url('/') }}"/>
    <meta property="og:description" content=""/>
    <meta property="og:image" content="{{ theme('logo_src') }}"/>
    <meta name="twitter:card" content="summary">
    <meta property="twitter:description" content=""/>
    <meta name="twitter:url" content="{{ url('/') }}">
    <meta name="twitter:image" content="{{ theme('logo_src') }}">
@endsection

@section('content')
    <section class="tw-reset confirmationPage">
        <div class="tw-bg-white">
            <div class="tw-mx-auto tw-max-w-3xl tw-px-4 tw-py-16 sm:tw-px-6 sm:tw-py-24 lg:tw-px-8">
                <div>
                    <h1 class="tw-text-base tw-font-medium tw-text-theme-brand-color">Order #{{ $order->id }}</h1>
                    <p class="tw-mt-2 tw-text-4xl tw-font-bold tw-tracking-tight sm:tw-text-5xl confirmationPage__heading">Thanks for pre-ordering!</p>
                    <p class="tw-mt-4 tw-text-base tw-text-gray-500 confirmationPage__message">{!! getMessage('checkout_confirmation') !!}</p>

                    @if( ! empty($order->pickup->setting('checkout_notes')))
                        <p class="tw-mt-4 tw-text-base tw-text-gray-500 confirmationPage__deliveryMethodNotes">{!! $order->pickup->setting('checkout_notes') !!}</p>
                    @endif
                </div>

                <div>
                    <div class="tw-mt-6 tw-border-t tw-border-gray-200 tw-pt-6 tw-space-y-6">
                        <h3 class="tw-text-base tw-font-body tw-font-normal tw-text-gray-500 lg:tw-text-lg">Order details</h3>
                        <div>
                            <div>
                                <h4 class="tw-sr-only">Contact and Shipping</h4>
                                <dl class="tw-mt-6 tw-grid tw-grid-cols-2 tw-gap-x-6 tw-text-sm">
                                    <div>
                                        <dt class="tw-font-semibold tw-text-gray-500">Contact</dt>
                                        <dd class="tw-mt-2 tw-text-gray-700">
                                            <address class="tw-not-italic">
                                                <span class="tw-m-0 tw-text-base tw-font-bold tw-block tw-text-gray-900">{{ $order->customer_first_name }} {{ $order->customer_last_name }}</span>
                                                <span class="tw-m-0 tw-mt-1 tw-text-sm tw-block">{{ $order->customer_email }}</span>
                                                <span class="tw-m-0 tw-mt-1 tw-text-sm tw-block">{{ $order->customer_phone }}</span>
                                            </address>
                                        </dd>
                                    </div>
                                    <div>
                                        <dt class="tw-font-semibold tw-text-gray-500">Delivery method</dt>
                                        <dd class="tw-mt-2 tw-text-gray-700">
                                            <p class="tw-m-0 tw-text-base tw-font-bold tw-block tw-text-gray-900">{{ $order->pickup->isDeliveryZone() ? 'Home Delivery' : 'Pickup' }}</p>
                                            <p class="tw-m-0 tw-mt-1 tw-text-sm tw-block">{{ $order->pickup_date?->format('l, M j, Y') ?? 'Date TBA' }}</p>
                                        </dd>
                                    </div>
                                </dl>
                            </div>
                            <div>
                                <h4 class="tw-sr-only">Shipping and Payment</h4>
                                <dl class="tw-grid tw-grid-cols-2 tw-gap-x-6 tw-text-sm">
                                    <div>
                                        <dt class="tw-font-semibold tw-text-gray-500">
                                            {{ $order->pickup->isDeliveryZone() ? 'Shipping' : 'Pickup' }} address
                                        </dt>
                                        <dd class="tw-mt-2 tw-text-gray-700">
                                            <address class="tw-not-italic">
                                                <span class="tw-m-0 tw-text-base tw-font-bold tw-block tw-text-gray-900">
                                                    {{ $order->customer_first_name }} {{ $order->customer_last_name }}
                                                </span>
                                                @if($order->pickup->isDeliveryZone())
                                                    <span class="tw-m-0 tw-mt-1 tw-text-sm tw-block">
                                                        {{ $order->shipping_street }}
                                                        @if( ! empty($order->shipping_street_2))
                                                            <span class="tw-m-0 tw-mt-1 tw-text-sm tw-block">{{ $order->shipping_street_2 }}</span>
                                                        @endif
                                                    </span>
                                                    <span class="tw-m-0 tw-mt-1 tw-text-sm tw-block">
                                                        {{ $order->shipping_city }}, {{ $order->shipping_state }} {{ $order->shipping_zip }}
                                                    </span>
                                                @else
                                                    <span class="tw-m-0 tw-mt-1 tw-text-sm tw-block">
                                                        {{ $order->pickup->street }}
                                                        @if( ! empty($order->pickup->street_2))
                                                            <span class="tw-m-0 tw-mt-1 tw-text-sm tw-block">{{ $order->pickup->street_2 }}</span>
                                                        @endif
                                                    </span>
                                                    <span class="tw-m-0 tw-mt-1 tw-text-sm tw-block">
                                                        {{ $order->pickup->city }}, {{ $order->pickup->state }} {{ $order->pickup->zip }}
                                                    </span>
                                                @endif
                                            </address>
                                        </dd>
                                    </div>
                                    <div>
                                        <dt class="tw-font-semibold tw-text-gray-500">Payment method</dt>
                                        <dd class="tw-mt-2 tw-text-gray-700">
                                            <p class="tw-m-0 tw-text-base tw-font-bold tw-block tw-text-gray-900">{{ $order->paymentMethod?->title }}</p>
                                        </dd>
                                    </div>
                                </dl>
                            </div>

                            @if($order->isGift())
                                <div>
                                    <h4 class="tw-sr-only">Gift information</h4>
                                    <dl class="tw-text-sm">
                                        <div>
                                            <dt class="tw-font-semibold tw-text-gray-500">Gift information</dt>
                                            <dd class="tw-mt-2 tw-text-gray-700">
                                                <p class="tw-m-0 tw-text-base tw-font-bold tw-block tw-text-gray-900">{{ $order->recipient_email }}</p>
                                                @if(!empty($order->recipient_notes))
                                                    <span class="tw-m-0 tw-mt-2 tw-text-sm tw-block">{{  $order->recipient_notes }}</span>
                                                @endif
                                            </dd>
                                        </div>
                                    </dl>
                                </div>
                            @endif
                        </div>


                        <section aria-labelledby="summary-heading" class="tw-mt-8 tw-rounded-lg tw-bg-gray-50 tw-px-4 tw-py-6 sm:tw-p-6 lg:tw-col-span-5 lg:tw-mt-0 lg:tw-p-8">
                            <h3 class="tw-sr-only">Summary</h3>

                            <x-theme::order-summary :order="$order"/>

                            <div class="tw-mt-6 tw-flex tw-items-center tw-justify-between tw-border-t tw-border-gray-200 tw-pt-4">
                                <dt class="tw-text-base tw-font-medium tw-text-gray-900">Total</dt>
                                <dd class="tw-text-base tw-font-medium tw-text-gray-900">${{ money($order->total) }}</dd>
                            </div>
                        </section>

                        <section class="tw-mt-10">
                            <div class="tw-mt-6 tw-flex tw-justify-center tw-space-x-6 tw-text-center tw-text-sm tw-text-gray-500">
                                <a href="{{ route('customer.orders.show', [$order->id]) }}" class="hidden-print tw-m-0">
                                    <button type="button" class="tw-font-medium tw-text-theme-link-color hover:tw-text-theme-link-color/70">
                                        View details
                                    </button>
                                </a>
                                <button type="button" onclick="window.print();" class="hidden-print tw-font-medium tw-text-theme-link-color hover:tw-text-theme-link-color/70">
                                    Print
                                </button>
                            </div>
                        </section>
                    </div>
                </div>

                @include('theme::checkout._partials.checkout_sms_opt_in')

            </div>
        </div>
    </section>

    @if($order->isFirstTimeOrder() && !$order->isGift())
        <x-theme::new-customer-modal/>
    @endif

@stop

@push('scripts')
    @if(Session::has('orderWasConfirmed'))
        <script>
            if (typeof gtag == 'function') {
                gtag('event', 'purchase', {
                    'transaction_id': "{{ $order->id }}",
                    'affiliation': 'GrazeCart',
                    'first_name': "{{ $order->customer_first_name }}",
                    'last_name': "{{ $order->customer_last_name }}",
                    'email': "{{ $order->customer_email }}",
                    'phone': "{{ $order->customer_phone }}",
                    'is_pickup': @json($order->pickup->isPickup()),
                    'city': "{{ $order->pickup->isDeliveryZone() ? $order->shipping_city : $order->pickup->city }}",
                    'region': "{{ $order->pickup->isDeliveryZone() ? $order->shipping_state : $order->pickup->state }}",
                    'postal_code': "{{ $order->pickup->isDeliveryZone() ? $order->shipping_zip : $order->pickup->zip }}",
                    'country': "{{ app(\App\Services\SettingsService::class)->farmCountry() }}",
                    'value': {{ money($order->total, '') }},
                    'currency': "{{ strtoupper(setting('currency', 'USD')) }}",
                    'tax': {{ money($order->tax, '') }},
                    'shipping': {{ money($order->delivery_fee, '') }},
                    'items': {!! $order->formatItemsForGoogleAnalytics() !!}
                });

                @if($order->isFromBlueprint())
                gtag('event', 'subscribe', {
                    'transaction_id': "{{ $order->id }}",
                    'subscription_id': "{{ $order->blueprint_id }}",
                    'affiliation': 'GrazeCart',
                    'value': {{ money($order->total, '') }},
                    'currency': "{{ strtoupper(setting('currency', 'USD')) }}"
                });
                @endif
            } else if (typeof ga == 'function') {
                console.warn('You have the old Google Analytics installed.');
                ga('require', 'ecommerce');
                ga('ecommerce:addTransaction', {
                    'id': {{ $order->id }},
                    'revenue': '{{ money($order->total, '') }}',
                    'shipping': '{{ money($order->delivery_fee, '') }}',
                    'tax': '{{ $order->tax }}'
                });

                @foreach($order->items as $item)
                ga('ecommerce:addItem', {
                    'id': {{ $order->id }},
                    'name': '{{ addslashes($item->title) }}',
                    'sku': '{{ $item->product->sku }}',
                    'price': '{{ money($item->price, '') }}',
                    'quantity': '{{ $item->qty }}'
                });
                @endforeach
                ga('ecommerce:send');
            }

            if (typeof fbq == 'function') {
                fbq('track', 'Purchase', {
                    value: {{ money($order->total, '') }},
                    currency: 'USD'
                });

                @if($order->isFromBlueprint())
                fbq('track', 'Subscribe', {
                    value: {{ money($order->total, '') }},
                    currency: 'USD',
                    predicted_ltv: {{ money(($order->total * 2.5), '') }}
                });
                @endif
            }

            @if(isset($checkoutScripts))
                {!! $checkoutScripts !!}
            @endif
        </script>

        @include('theme::_partials.tolstoy-conversion-script')
    @endif
@endpush
