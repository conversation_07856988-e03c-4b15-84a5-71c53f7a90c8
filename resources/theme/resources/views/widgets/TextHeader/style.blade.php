.textHeader--{{ $widget->id }} {
    background-color: {{ $widget->getColor('background', 'transparent') }}; 
    color: {{ $widget->getColor('color', 'var(--text_color)') }}; 
    padding-top: {{ $widget->setting('paddingTop', 60) }}px; 
    padding-bottom: {{ $widget->setting('paddingBottom', 0) }}px;
}

.textHeader--{{ $widget->id }} .textHeader__heading {
    color: {{ $widget->getColor('header_text_color', 'inherit') }}; 
}

.textHeader--{{ $widget->id }} .textHeader__subheading {
    color: {{ $widget->getColor('subheader_text_color', 'inherit') }}; 
}