// .footer {
//   margin: 0;
//   width: 100%;
// }

// .footer__main {
//   max-width: 1200px;
//   padding: 10px 0 20px 0;
//   margin: 0 auto;
//   display: flex;
//   justify-content: space-around;
//   flex-wrap: wrap;
//   > div {
//     padding: 30px 20px 30px 20px;
//     width: 25%;
//   }
//   ul {
//     list-style-type: none;
//     margin-right: 0px;
//     padding: 0px;
//     li {
//       a {
//         display: block;
//         padding-bottom: 5px;
//       }
//       a:hover {
//         text-decoration: underline;
//       }
//     }
//   }
// }

// .footer hr {
//   border: none;
//   height: 2px;
// }

// .footer__contact {
//   text-align: left !important;
// }

// .footer__secondary {
//   margin: 0 auto;
//   text-align: center;
//   padding: 30px 0;
// }

// @media (max-width: 950px) {
//   .footer__main {
//     justify-content: space-between;
//     > div {
//       width: 50%;
//     }
//   }
// }

// @media (max-width: 600px) {
//   .footer__main {
//     justify-content: center;
//     > div {
//       width: 100%;
//     }
//   }

//   .footer__secondary {
//     width: 100%;
//   }
// }  