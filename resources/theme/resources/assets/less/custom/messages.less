.siteMessage {
	text-align: center;
	width: 100%;
	background-color: #EEE;
	border-bottom: solid 1px #DDD;
	padding: (@white-space / 2);
	ul {
		list-style-type: none;
		padding: 0;
		margin: (@white-space / 4) 0;
	}
}

.siteMessage__message {
	display: block;
	vertical-align: middle;
}

.siteMessage__closeButtonContainer {
	display: block;
	margin-top: (@white-space / 4);
	vertical-align: middle;
	a {
		color: #777;
		border-bottom: dotted 1px #777;
	}
}

.siteMessage--hide {
	display: none;
}

@media (max-width: @mobile-width) {
	.siteMessage__container {
		margin: 0 auto;
		width: auto;
		display: inline-block;
		position: relative;
	}
}	