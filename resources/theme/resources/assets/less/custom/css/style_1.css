.header--style_1 .header {
  width: 100%;
  height: auto;
  padding: 0;
}
.header--style_1 .header__container {
  max-width: 1200px;
  align-items: center;
  flex-wrap: no-wrap;
  height: auto;
  margin: 0 auto;
  justify-content: center;
  -ms-flex-pack: center;
}
.header--style_1 .logo {
  text-align: center;
  height: auto;
}
.header--style_1 .logo--text {
  font-size: 30px !important;
}
.header--style_1 .logo--container {
  flex: 0 1 auto;
  height: auto;
}
.header--style_1 .menu--container {
  flex: 1 1 auto;
  height: auto;
}
.header--style_1 .logo--img {
  display: inline-block;
  min-width: 75px;
}
.header--style_1 .navigationMenu--toggle {
  display: none;
  margin-left: auto;
}
@media (max-width: 768px) {
  .header--style_1 .header--container {
    flex-direction: column !important;
  }
  .header--style_1 .menu--container {
    width: 100% !important;
    padding: 0 !important;
  }
  .header--style_1 .navigationMenu--toggle {
    display: block;
    flex: 1 0 auto;
  }
  .header--style_1 .navigationMenu--toggle-button {
    display: block;
    border: none;
    background-color: #fff;
    color: #3d3d3d;
    padding: 6px 10px;
    border-radius: 2px;
    font-size: 22px;
    line-height: 1;
    margin: 6px 8px 6px auto;
  }
  .header--style_1 .navigationMenu--toggle-button:hover {
    background-color: #3d3d3d;
    color: #fff;
  }
  .header--style_1 .navigationMenu--toggle-button:focus {
    outline: none;
  }
  .header--style_1 .logo--container {
    display: flex;
    align-items: center;
    width: 100%;
    padding: 0 10px;
  }
  .header--style_1 .logo {
    flex: 1 0 auto;
    text-align: left;
  }
  .header--style_1 .logo--img {
    max-height: 75px !important;
    max-width: 250px;
  }
}
