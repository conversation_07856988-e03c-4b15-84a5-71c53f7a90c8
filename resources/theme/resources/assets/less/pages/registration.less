.registrationPage__header {
	text-align: center;
}

.registrationPage__form {
	display: block;
	width: (@site-width / 3);
	max-width: 100%;
	margin: (@white-space / 2) auto;
}

// Advnced Registration
.advancedRegistration {
	background-size: cover;
	background-position-y: 50%;
	background-repeat: no-repeat;
	padding: 3.75rem 1.25rem;
	display: flex;
	justify-content: center;
	align-items: center;
}

.advancedRegistrationPanel {
	background-color: #FFF;
	border-radius: 0.75rem;
	text-align: center;
	border: solid 1px #eee;
	width: 500px;
	max-width: 100%;
	.advancedRegistrationPanel-body {
		padding: 1.25rem;
	}
	header h1 {
		font-size: 2rem;
	}
	header > div {
		font-size: 1rem;
		color: #5d5d5d;
		padding: 0 0 1rem 0;
		max-width: 22.5rem;
		margin: 0 auto;
	}
	> .btn-group {
		transform: translateY(-50%);
	}
}