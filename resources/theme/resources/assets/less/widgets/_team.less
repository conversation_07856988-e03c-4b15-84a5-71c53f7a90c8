.teamWidget {
	padding: @white-space 0;
}

.teamWidget__scrollTo {
	margin: 0;
	padding: 0;
	width: 100%;
	height: 1px;
	display: block;
	background-color: transparent;
}

.teamWidget__list {
	padding: 0 (@white-space / 3);
	list-style-type: none;
	margin: 0 auto;
	max-width: @site-width;
}

.teamWidget__listItem {
	width: 200px;
	max-width: 100%;
	display: inline-block;
	vertical-align: top;
	text-align: center;
	margin: (@white-space / 2);
	> a {
		color: inherit;
		outline: 0;
	}
	> a:hover {
		text-decoration: none;
	}
	> a:active {
		border: none;
		outline: 0;
	}
}

.teamWidget__listImage {
	max-height: 150px;
	max-width: 150px;
	margin: 0 auto (@white-space / 3) auto;
	border-radius: 50%;
	display: block;
	transition: all 0.2s ease-in-out;
}

.teamWidget__listImage:hover {
	opacity: 0.7;
}

.teamWidget__listCaption {
	color: #3d3d3d;
}

.teamWidget__listCaption {
	margin: 0 0 6px 0;
	font-size: 16px;
}

.teamWidget__listSubcaption {
	margin: 0;
	color: #777;
	font-size: 13px;
}

.teamWidget__bioContainer {
	width: 100%;
	padding: @white-space (@white-space / 3);
	align-items: center;
	background-color: #fefefe;
	position: relative;
	box-shadow: inset 2px 2px 20px 1px rgba(119,119,119,0.25);
}

.teamWidget__bioInnerContainer {
	display: flex;
	padding: (@white-space / 3);
	// flex-wrap: wrap;
}

// Profile

.teamWidget__profileClose {
	position: absolute;
	top: 24px;
	right: 24px;
	font-size: 30px;
	color: #777;
}

.teamWidget__profileImage {
	max-width: 250px;
	width: 100%;
	margin: 0 auto (@white-space / 2) auto;
	display: block;
}

.teamWidget__profileLeft {
	text-align: center;
	flex: 1 0 auto;
}

.teamWidget__profileRight {
	text-align: left;
	padding-left: (@white-space / 3);
	flex: 1 1 100%;
	max-width: 100%;
}

.teamWidget__profileBio {
	margin-top: 0.75rem;
}

.teamWidget__profileCaption {
	margin: (@white-space / 4) 0 0 0;
}

.teamWidget__profileSubcaption {
	margin: 0;
	color: #777;
}

@media (max-width: @mobile-width) {
	.teamWidget__bioInnerContainer {
		display: block;
		padding: (@white-space / 4);
	}

	.teamWidget__profileBio {
		padding-bottom: 0;
	}

	.teamWidget__profileLeft {
		margin-bottom: (@white-space / 3);
	}

	.teamWidget__profileRight {
		
	}
}	