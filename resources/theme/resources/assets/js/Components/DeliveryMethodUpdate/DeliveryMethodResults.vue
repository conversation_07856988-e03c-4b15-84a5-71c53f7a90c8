<template>
    <div>
        <div v-show="!ineligible"  class="tw-bg-white tw-px-4 tw-pt-5 tw-pb-4 sm:tw-p-6 sm:tw-pb-4">
            <DialogTitle as="h3" class="tw-text-lg tw-leading-6 tw-font-medium tw-font-display tw-text-gray-900"> Available Options </DialogTitle>
            <div class="tw-pt-6 tw-flow-root">
                <ul role="list" class="tw--my-5 tw-divide-y tw-divide-gray-200">
                    <li v-for="zone in deliveryMethods.delivery" :key="zone.id" @click="validateLocation(zone)" class="tw-bg-white tw-group tw-p-4 tw-cursor-pointer hover:tw-bg-gray-100">
                        <div class="tw-flex tw-items-center tw-space-x-4">
                            <div class="tw-flex-shrink-0">
                                <TruckIcon class="tw-h-5 tw-w-5 tw-text-gray-500"/>
                            </div>
                            <div class="tw-flex-1 tw-min-w-0">
                                <p class="tw-text-sm tw-font-medium tw-text-gray-900 tw-truncate" v-text="deliveryLocationTitle(zone)"></p>
                            </div>
                            <div>
                                <CheckCircleIcon v-if="isCurrentlySelected(zone)" class="tw-h-5 tw-w-5 tw-text-green-500"/>
                                <div v-else>
                                    <svg v-if="validatingLocation && validatingLocation.id === zone.id" class="tw-animate-spin tw--ml-1 tw-h-5 tw-w-5  tw-text-gray-900" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                        <circle class="tw-opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                        <path class="tw-opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                    </svg>
                                    <ChevronRightIcon v-else class="tw-h-5 tw-w-5 tw-text-gray-500 group-hover:tw-text-gray-700"/>
                                </div>
                            </div>
                        </div>
                    </li>
                    <li v-for="pickup in deliveryMethods.pickup" :key="pickup.id" @click="validateLocation(pickup)" class="tw-bg-white tw-group tw-p-4 tw-cursor-pointer hover:tw-bg-gray-100">
                        <div class="tw-flex tw-items-center tw-space-x-4">
                            <div class="tw-flex-shrink-0">
                                <FlagIcon class="tw-h-5 tw-w-5 tw-text-gray-500"/>
                            </div>
                            <div class="tw-flex-1 tw-min-w-0">
                                <p class="tw-text-sm tw-font-medium tw-text-gray-900 tw-truncate" v-text="pickupLocationTitle(pickup)"></p>
                                <p class="tw-text-xs tw-text-gray-500 tw-truncate" v-text="pickupDistanceDetails(pickup)"></p>
                                <p class="tw-text-xs tw-text-gray-500 tw-truncate" v-text="pickupAddressDetails(pickup)"></p>
                            </div>
                            <div>
                                <CheckCircleIcon v-if="isCurrentlySelected(pickup)" class="tw-h-5 tw-w-5 tw-text-green-500"/>

                                <div v-else>
                                    <svg v-if="validatingLocation && validatingLocation.id === pickup.id" class="tw-animate-spin tw--ml-1 tw-h-5 tw-w-5 tw-text-gray-900" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                        <circle class="tw-opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                        <path class="tw-opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                    </svg>
                                    <ChevronRightIcon v-else class="tw-h-5 tw-w-5 tw-text-gray-500 group-hover:tw-text-gray-700"/>
                                </div>
                            </div>
                        </div>
                    </li>
                </ul>
                <div v-if="noResults" class="tw-flex tw-items-center tw-space-x-4">
                    <p class="tw-mt-1 tw-text-gray-500 tw-italic">No available options were found for your search.</p>
                </div>
            </div>
            <div class="tw-mt-6">
                <button @click="$emit('clear')" class="tw-w-full tw-flex tw-justify-center tw-items-center tw-px-4 tw-py-2 tw-border tw-border-gray-300 tw-shadow-sm tw-text-sm tw-font-medium tw-rounded-md tw-text-gray-700 tw-bg-white hover:tw-bg-gray-50"> Try another zipcode </button>
            </div>
        </div>
        <div v-if="ineligible">
            <div  class="tw-bg-white tw-px-4 tw-pt-5 tw-pb-4 sm:tw-p-6 sm:tw-pb-4">
                <DialogTitle as="h3" class="tw-text-lg tw-leading-6 tw-font-medium tw-font-display tw-text-gray-900"> Products Unavailable </DialogTitle>
                <div class="tw-mt-2">
                    <p class="tw-text-sm tw-text-gray-500">Some of the products are not available at the selected location. Are you sure you want to update your location?</p>
                </div>
                <div class="tw-pt-6 tw-flow-root">
                    <ul role="list" class="tw--my-5 tw-divide-y tw-divide-gray-200">
                        <li v-for="product in ineligibleProducts" :key="product" class="tw-py-4">
                            <div class="tw-flex tw-items-center tw-space-x-4">
                                <div class="tw-flex-1 tw-min-w-0">
                                    <p class="tw-text-sm tw-font-medium tw-text-gray-900 tw-truncate" v-text="product"></p>
                                </div>
                            </div>
                        </li>
                    </ul>
                </div>
            </div>

            <div class="tw-bg-gray-50 tw-px-4 tw-py-3 sm:tw-px-6 sm:tw-flex sm:tw-flex-row-reverse">
                <button v-show="!removingProducts" @click="selectLocation" :disabled="removingProducts" type="submit" class="tw-w-full tw-inline-flex tw-justify-center tw-rounded-md tw-border tw-border-transparent tw-shadow-sm tw-px-4 tw-py-2 tw-bg-theme-action-color tw-text-base tw-font-medium tw-text-white hover:tw-bg-theme-action-color focus:tw-outline-none focus:tw-ring-2 focus:tw-ring-offset-2 focus:tw-ring-theme-action-color sm:tw-ml-3 sm:tw-w-auto sm:tw-text-sm">Yes, remove products</button>
                <button v-show="removingProducts" type="button" class="tw-w-full tw-inline-flex tw-justify-center tw-rounded-md tw-border tw-border-transparent tw-shadow-sm tw-px-4 tw-py-2 tw-bg-theme-action-color tw-text-base tw-font-medium tw-text-white hover:tw-bg-theme-action-color focus:tw-outline-none focus:tw-ring-2 focus:tw-ring-offset-2 focus:tw-ring-theme-action-color sm:tw-ml-3 sm:tw-w-auto sm:tw-text-sm">
                    <svg class="tw-animate-spin tw--ml-1 tw-mr-3 tw-h-5 tw-w-5 tw-text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle class="tw-opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="tw-opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Removing...
                </button>
                <button type="button" class="tw-mt-3 tw-w-full tw-inline-flex tw-justify-center tw-rounded-md tw-border tw-border-gray-300 tw-shadow-sm tw-px-4 tw-py-2 tw-bg-white tw-text-base tw-font-medium tw-text-gray-700 hover:tw-bg-gray-50 focus:tw-outline-none focus:tw-ring-2 focus:tw-ring-offset-2 focus:tw-ring-theme-action-color sm:tw-mt-0 sm:tw-ml-3 sm:tw-w-auto sm:tw-text-sm" @click="$emit('clear')" ref="cancelButtonRef">Nevermind</button>
            </div>
        </div>
    </div>
</template>

<script>
import { TruckIcon, FlagIcon, CheckCircleIcon, ChevronRightIcon } from '@heroicons/vue/outline'
import { DialogTitle } from '@headlessui/vue'
import axios from "axios";
import { ref, computed } from "vue";

export default {
    name: "DeliveryMethodResults",

    emits: ['close', 'delivery-method-selected', 'clear'],

    props: {
        currentDeliveryMethod: { default: null },
        cartItems: { default: () => ([]) },
        deliveryMethods: { default: () => ([]) }
    },

    components: {
        FlagIcon,
        TruckIcon,
        DialogTitle,
        CheckCircleIcon,
        ChevronRightIcon
    },

    setup(props, { emit }) {
        const validatingLocation = ref(null)
        const ineligible = ref(false)
        const ineligibleReason = ref(null)
        const ineligibleProducts = ref([])
        const removingProducts = ref(false)

        const handleIneligibleCart = body => {
            ineligible.value = true;
            ineligibleReason.value = body.ineligible_reason;
            ineligibleProducts.value = body.products;
        }

        const validateLocation = location => {
            if (validatingLocation.value !== null) return;

            validatingLocation.value = location;
            ineligible.value = false;
            ineligibleReason.value = null;

            axios.post(`/api/locations/${location.id}/cart-eligibility`, {
                product_ids: Object.values(props.cartItems)
                    .map(item => item.product.id)
            })
                .then(response => {
                    if ( ! response.data.eligible) {
                        return handleIneligibleCart(response.data)
                    }

                    emit('delivery-method-selected', location)
                })
        }

        const deliveryLocationTitle = location => {
            if (props.deliveryMethods.delivery.length > 1) {
                return location.display_name ? location.display_name : location.title
            }

            return location.display_name ? location.display_name : 'Home Delivery'
        }

        const pickupLocationTitle = location => {
            const name = location.display_name ? location.display_name : location.title
            return `Pickup at ${name}`
        }

        const pickupDistanceDetails = location => {
            return `${Math.round(location.distance)} miles away`
        }

        const pickupAddressDetails = location => {
            return `${location.street}, ${location.city}, ${location.state} ${location.zip}`
        }

        const isCurrentlySelected = location => props.currentDeliveryMethod !== null && props.currentDeliveryMethod.id === location.id

        const noResults = computed(() => {
            if ( ! props.deliveryMethods.delivery && ! props.deliveryMethods.pickup) {
                return true;
            }

            return props.deliveryMethods.delivery.length === 0 && props.deliveryMethods.pickup.length === 0
        })

        return {
            deliveryLocationTitle,
            pickupLocationTitle,
            pickupDistanceDetails,
            pickupAddressDetails,
            isCurrentlySelected,
            validateLocation,
            validatingLocation,
            ineligible,
            removingProducts,
            ineligibleProducts,
            noResults
        }
    }
};
</script>
