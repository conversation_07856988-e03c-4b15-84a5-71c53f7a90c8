<template>
    <div>
        <div class="tw-relative tw-rounded-md tw-shadow-sm">
            <input
                :id="id"
                :ref="reference"
                :aria-describedby="errorId"
                :aria-invalid="hasError"
                :class="[
                    'tw-block tw-w-full focus:tw-outline-none sm:tw-text-sm tw-rounded-md',
                    hasError
                    ? 'tw-pr-10 tw-border-red-300 tw-text-red-900 tw-placeholder-red-300 focus:tw-ring-red-500 focus:tw-border-red-500'
                    : 'tw-border-gray-300 tw-text-gray-700 tw-placeholder-gray-300 focus:tw-ring-theme-action-color focus:tw-border-theme-action-color'
                ]"
                :name="name"
                :placeholder="placeholder"
                :type="type"
                :value="modelValue"
                v-bind="$attrs"
                @input="$emit('update:modelValue', $event.target.value)"
            />
            <div v-if="hasError" class="tw-absolute tw-inset-y-0 tw-right-0 tw-pr-3 tw-flex tw-items-center tw-pointer-events-none">
                <ExclamationCircleIcon aria-hidden="true" class="tw-h-5 tw-w-5 tw-text-red-500" />
            </div>
        </div>
        <p v-if="hasError" :id="errorId" class="tw-mt-2 tw-text-sm tw-text-red-600" v-text="error"></p>
    </div>
</template>

<script>
import { ExclamationCircleIcon } from '@heroicons/vue/solid';
import { computed } from 'vue';

export default {
    name: 'Input',

    emits: ['update:modelValue'],

    props: {
        modelValue: { required: true },
        id: { required: true },
        name: { required: true },
        type: { default: 'text' },
        error: { default: null },
        placeholder: { default: '' },
        reference: { default: null }
    },

    components: {
        ExclamationCircleIcon
    },

    setup(props) {
        const hasError = computed(() => !!props.error);

        const errorId = computed(() => hasError ? `${props.id}-error` : undefined);

        return {
            hasError,
            errorId
        };
    }
};
</script>
