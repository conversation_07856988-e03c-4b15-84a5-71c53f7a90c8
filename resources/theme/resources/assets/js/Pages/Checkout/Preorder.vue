<template>
    <Head>
        <title>Checkout - {{ checkout.product.title }}</title>
    </Head>

    <div class="tw-min-h-screen tw-bg-white">
        <!-- Background color split screen for large screens -->
        <div aria-hidden="true" class="tw-hidden lg:tw-block tw-fixed tw-top-0 tw-left-0 tw-w-1/2 tw-h-full tw-bg-white"/>
        <div aria-hidden="true" class="tw-hidden lg:tw-block tw-fixed tw-top-0 tw-right-0 tw-w-1/2 tw-h-full tw-bg-gray-50"/>

        <PageHeader/>

        <main class="tw-relative tw-grid tw-grid-cols-1 tw-gap-x-16 tw-max-w-7xl tw-mx-auto lg:tw-px-8 lg:tw-grid-cols-2 xl:tw-gap-x-48">
            <h1 class="tw-sr-only">Pre-order information</h1>

            <section aria-labelledby="summary-heading" class="tw-bg-gray-50 tw-pt-4 tw-pb-4 tw-px-4 sm:tw-px-6 lg:tw-px-0 lg:tw-pt-16 lg:tw-pb-16 lg:tw-bg-transparent lg:tw-row-start-1 lg:tw-col-start-2">
                <div class="tw-max-w-lg tw-mx-auto lg:tw-max-w-none">
                    <ProductSummary/>
                </div>
            </section>

            <div class="tw-pt-4 tw-pb-4 tw-px-4 sm:tw-px-6 lg:tw-pt-16 lg:tw-pb-16 lg:tw-px-0 lg:tw-row-start-1 lg:tw-col-start-1">
                <div class="tw-max-w-lg tw-mx-auto lg:tw-max-w-none">
                    <ProductCheckoutForm
                        :require-step-by-step="true"
                        :update-cart-on-location-change="false"
                    />
                </div>
            </div>
        </main>
    </div>
</template>

<script>
export default {
    name: 'CheckoutPreorder'
};
</script>

<script setup>
import PageHeader from '../../Components/PageHeader.vue';
import ProductSummary from '../../Components/Checkout/ProductSummary.vue';
import ProductCheckoutForm from '../../Components/Checkout/ProductCheckoutForm.vue';
import { useThemeStore } from '../../stores/theme';
import { useTenantStore } from '../../stores/tenant';
import { useUserStore } from '../../stores/user';
import { useMessageStore } from '../../stores/message';
import { useCartProductStore } from '../../stores/cartProduct';
import { useCheckoutStore } from '../../stores/checkout';
import { usePage } from '@inertiajs/inertia-vue3';
import { computed, toRaw } from 'vue';
import { find } from 'lodash';
import { Head } from '@inertiajs/inertia-vue3';
import { itemSubtotal } from '../../../../../../assets/js/modules/cart.js';

const props = defineProps({
    settings: { required: true },
    cart: { required: true },
    customer: { required: true },
    checkout_settings: { required: true }
});

const pageProps = usePage().props.value;

const theme = useThemeStore();
theme.$patch({ ...pageProps.settings.theme });

const tenant = useTenantStore();
tenant.$patch({ ...pageProps.settings.tenant });

const checkout = useCheckoutStore();
checkout.$patch({ ...pageProps.checkout_settings });

const user = useUserStore();
user.$patch({ ...pageProps.auth.user, ...pageProps.customer });

const cart = useCartProductStore();
cart.$patch(toRaw(pageProps.cart));

const message = useMessageStore();
message.$patch({ ...pageProps.settings.message });

cart.$patch({
    date_id: cart.date_id ? cart.date_id : '', // needed for null date select
    billing: { method: toRaw(checkout.available_payment_options)[0].key }
});

if (user.settings && user.settings.default_payment_method) {
    const preferredPaymentMethods = toRaw(checkout.available_payment_options)
        .filter(option => option.id === parseInt(user.settings.default_payment_method));

    if (preferredPaymentMethods.length > 0) {
        const preferredMethod = preferredPaymentMethods[0].key;
        let paymentSource = null;

        if (preferredMethod === 'card') {
            paymentSource = find(user.cards, { id: cart.billing.source_id });
        }

        cart.updateBillingMethod(preferredMethod, paymentSource ? paymentSource.id : null);
    }
} else {
    const hasCardPayment = toRaw(checkout.available_payment_options)
        .filter(option => option.key === 'card')
        .length > 0;

    if (hasCardPayment && user.checkout_card_id) {
        const checkoutCard = find(user.cards, { id: user.checkout_card_id });
        cart.updateBillingMethod('card', checkoutCard ? checkoutCard.id : null);
    }
}

if (checkout.available_dates.length > 0) {
    cart.$patch({ date_id: checkout.available_dates[0].id });
}

if (typeof gtag === 'function') {
    gtag('event', 'begin_checkout', {
        currency: 'USD',
        value: cart.itemsSubtotal / 100,
        items: cart.itemsAsGoogleArray
    });
}

</script>
