import { createApp, h } from 'vue';
import { createInertiaApp } from '@inertiajs/inertia-vue3';
import { InertiaProgress } from '@inertiajs/progress';
import { createPinia } from 'pinia';
// import bugsnagVuePlugin from "./bugsnag-vue";
import mitt from 'mitt';
import { resolvePageComponent } from 'laravel-vite-plugin/inertia-helpers';

InertiaProgress.init();

const emitter = mitt();

createInertiaApp({
    resolve: async name => {
        return await resolvePageComponent(`./Pages/${name}.vue`, import.meta.glob('./Pages/**/*.vue'));
    },
    // resolve: name => import(`./Pages/${name}.vue`), // using require instead of import until we figure out our code splitting URLs

    setup({ el, App, props, plugin }) {
        createApp({ render: () => h(App, props) })
            .use(plugin)
            .use(createPinia())
            // .use(bugsnagVuePlugin)
            .provide('events', emitter)
            .mount(el);
    }
});
