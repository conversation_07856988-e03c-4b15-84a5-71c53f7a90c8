@extends('layouts.full_screen', ['pageTitle' => $page->title])

@section('styles')
    <style>
        :root {
            --background_color: purple;
            --text_color: red;
            --link_color: blue;
        }

        body, html, #app {
            height: 100%;
        }

        body {
            padding: 0;
            margin: 0;
        }

        .redactor-box.redactor-styles-on {
            border: none;
            background-color: transparent;
            border-radius: 0;
        }

        .richTextPreview {
            background-color: transparent;
        }

        .richTextPreview .h1 .h2 .h3 .h4 .h5 .h6, h1 h2 h3 h4 h5 h6 legend {
            font-family: {!! app('theme')->setting('heading_font', 'Helvetica Neue, Helvetica, Arial, sans-serif') !!};
        }


        .richTextPreview p label .paragraph {
            font-family: {!! app('theme')->setting('paragraph_font', 'Helvetica Neue, Helvetica, Arial, sans-serif') !!};
            font-size: {!! app('theme')->setting('paragraph_font_size', '16px') !!};
        }

        .richTextPreview .brand {
            background-color: {!! app('theme')->setting('brand_color', '#34B393') !!};
            color: {!! app('theme')->setting('brand_color_inverted', '#FFFFFF') !!};
            border-color: {!! app('theme')->setting('brand_color', '#34B393') !!};
        }

        .richTextPreview .text-brand {
            color: {!! app('theme')->setting('brand_color', '#34B393') !!};
        }

        .richTextPreview .text-action {
            color: {!! app('theme')->setting('action_color', '#34B393') !!};
        }

        .richTextPreview .brand-inverted {
            background-color: {!! app('theme')->setting('brand_color_inverted', '#FFFFFF') !!};
            color: {!! app('theme')->setting('brand_color', '#34B393') !!};
            border-color: {!! app('theme')->setting('brand_color_inverted', '#FFFFFF') !!};
        }

        .richTextPreview .action {
            background-color: {!! app('theme')->setting('action_color', '#CF3D2E') !!};
            color: {!! app('theme')->setting('action_color_inverted', '#FFFFFF') !!};
            border-color: {!! app('theme')->setting('action_color', '#CF3D2E') !!};
        }

        .richTextPreview .action-inverted {
            background-color: {!! app('theme')->setting('action_color_inverted', '#FFFFFF') !!};
            color: {!! app('theme')->setting('action_color', '#CF3D2E') !!};
            border-color: {!! app('theme')->setting('action_color_inverted', '#FFFFFF') !!};
        }
    </style>
@endsection


@section('content')
    <page-editor id="{{ $page->id }}"/>
@endsection

@section('scripts')
    <script>
        document.addEventListener('DOMContentLoaded', () => {
            $('.colorpicker').spectrum({
                preferredFormat: 'hex',
                showInput: true
            });
        });
    </script>
@endsection
