@extends('layouts.main', ['pageTitle' => $page->title])

@section('toolbar-breadcrumb')
    <li>Pages</li>
@endsection

@section('content')
    <livewire:admin.pages.show :page_id="$page->id"/>
    <media-browser/>

@endsection

@section('modals')
    <livewire:admin.modals.add-widget :page_id="$page->id"/>
    <livewire:admin.modals.edit-widget :page_id="$page->id"/>
    <livewire:admin.modals.preview-widget :page_id="$page->id"/>
    <livewire:admin.modals.preview-page :page_id="$page->id"/>
@endsection
