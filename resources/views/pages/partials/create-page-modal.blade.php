<div class="gc-modal gc-modal-mask" id="createPageModal" @click="hideModal('createPageModal')">
    <div class="gc-modal-wrapper">
        <div class="gc-modal-container" @click.stop>
            <form action="{{ route('admin.pages.store') }}" method="POST" id="storePageForm">
                @csrf
                <div class="gc-modal-header">
                    Create a Page
                </div>

                <div class="gc-modal-body">
                    <div class="form-group">
                        <label for="type">Type</label>
                        {{ html()->select('type', ['legacy'=> 'Legacy', 'promo' => 'Promo'])->class('form-control') }}
                    </div>
                    <div class="form-group">
                        <label for="title">Page Name</label>
                        {{ html()->text('title')->class('form-control') }}
                    </div>
                    <div class="form-group">
                        <label for="page_title">SEO Title</label>
                        {{ html()->text('page_title')->class('form-control') }}
                    </div>

                    <div class="form-group">
                        <label for="description">SEO Description</label>
                        <textarea name="description" class="form-control" rows="4"></textarea>
                    </div>
                </div>

                <div class="gc-modal-footer">
                    <button type="button" class="btn btn-alt" @click="hideModal('createPageModal')">Cancel</button>
                    <button type="submit" class="btn btn-action" @click.prevent="submitForm('storePageForm', $event)">Create Page</button>
                </div>
            </form>
        </div>
    </div>
</div>
