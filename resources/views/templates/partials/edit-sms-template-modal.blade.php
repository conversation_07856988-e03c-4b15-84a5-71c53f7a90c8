<div class="gc-modal gc-modal-mask" id="editTemplateModal-{{ $template->id }}" @click="hideModal('editTemplateModal-{{ $template->id }}')">
    <div class="gc-modal-wrapper">
        <div class="gc-modal-container" @click.stop>
            <form action="/admin/templates/{{ $template->id }}" method="POST" id="editTemplateForm-{{ $template->id }}">
                @csrf
                @method('PUT')

                <input type="hidden" name="sms" value="true" />

                <div class="gc-modal-header">
                    Edit SMS Template
                </div>

                <div class="gc-modal-body">
                    <div class="form-group">
                        <label for="title">Template Name</label>
                        {{ html()->text('title', $template->title)->class('form-control') }}
                    </div>

                    <div class="merge-tags-list">
                        <div>
                            <label>Merge Tags</label>
                            <div>
                                @include('templates.partials.merge-tags.sms')
                            </div>
                        </div>
                    </div>
                    <div style="clear:both;"></div>


                    <div class="form-group">
                        <label for="plain_text">Message</label>
                        {{ html()->textarea('plain_text', $template->plain_text)->class('form-control') }}
                    </div>
                </div>

                <div class="gc-modal-footer">
                    <button type="submit" name="send_test" value="1" class="btn btn-action send-test-btn">Send Test</button>
                    <button type="button" class="btn btn-alt" @click="hideModal('editTemplateModal-{{ $template->id }}')">Cancel</button>
                    <button type="submit" class="btn btn-action" @click.prevent="submitForm('editTemplateForm-{{ $template->id }}', $event)">Save</button>
                </div>
            </form>
        </div>
    </div>
</div>
