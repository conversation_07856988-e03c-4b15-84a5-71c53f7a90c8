@if($item->is_grouped)
    <tr>
        <td class="weightColumn">
            @if($item->isPricedByWeight())
                <div class="weightBox">{{ $item->weight }} est.</div>
            @else
                <div class="checkBox"></div>
            @endif
        </td>
        <td>
            <div class="qtyContainer">
                @if($item->isUnfulfilled())
                    <div class="quantity no-break"><span class="strike-through ml-1">( {{ $item->qty }} )</span>( {{ $item->fulfilled_qty }} )</div>
                @else
                    <div class="quantity no-break">( {{ $item->qty }} )</div>
                @endif
                <div>
                    <div class="itemName">
                        <div class="bundle_{{ $item->bundle }}">&#9733;</div>
                        <div>
                            @if($item->stock_status === 'out')
                                <span class="strike-through">{{ $item->title }}</span>
                            @else
                                {{ $item->title }}
                            @endif
                            <div class="fulfillmentInstructions">
                                {{ $item->fulfillment_instructions ?? '' }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>     
        </td>    
    </tr>
@else
    @for($i = 0; $i < $item->fulfilledQuantity(); $i++)
        <tr>
            <td class="weightColumn">
                @if($item->isPricedByWeight())
                    <div class="weightBox">{{ $item->weight }} est.</div>
                @else
                    <div class="checkBox"></div>
                @endif
            </td>
            <td>
                <div class="qtyContainer">
                    <div class="quantity no-break">( 1 )</div>
                    <div>
                        <div class="itemName">
                            <div class="bundle_{{ $item->bundle }}">&#9733;</div>
                            <div>
                                {{ $item->title }}
                                <div class="fulfillmentInstructions">
                                    {{ $item->fulfillment_instructions ?? '' }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>     
            </td>
        </tr>
    @endfor    
@endif