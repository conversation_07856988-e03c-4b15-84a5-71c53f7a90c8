<table class="header">
    <tr>
        <td>
            <img 
                src="{{ theme('logo_src') }}" 
                class="logo logo_{{ setting('invoice_logo_visibility', 'hide') }}" alt="Logo"
            >
            <ul class="companyDetails">
                <li class="farmName"><strong>{{ $farm['farm_name'] }}</strong></li>
                <li>{{ $farm['farm_street'] }}</li>
                <li>{{ $farm['farm_city'] }}, {{ $farm['farm_state'] }} {{ $farm['farm_zip'] }}</li>
                <li>{{ $farm['farm_phone'] }}</li>
            </ul>
        </td>
        <td>
            <ul class="invoiceDetails">
                <li><strong>Invoice #{{ $order->id }}</strong></li>
                <li>Date: {{ $order->present()->confirmedDate() }}</li>
                <li>Payment: {{ $order->present()->paymentMethod }}</li>
                <li>Containers: {{ $order->containers + $order->containers_2 }}</li>
            </ul>
        </td>
    </tr>
</table>

<table class="deliveryDetails">
    <thead>
        <tr>
            <th>For:</th>
            <th>{{ $order->pickup->isDeliveryZone() ? 'Deliver to:' : 'Pick-up at:' }}</th>
        </tr>    
    </thead>
    <tbody>
        <tr>
        <td width="50%">
            <ul>
                <li>{{ $order->customer_first_name }} {{ $order->customer_last_name }}</li>
                <li>{{ $order->shipping_street }}</li>
                <li>{{ $order->shipping_city }}, {{ $order->shipping_state }} {{ $order->shipping_zip }}</li>
                <li>{{ $order->customer_phone }} - {{ $order->customer_email }}</li>
            </ul>
        </td>
        <td width="50%">
            @if($order->pickup->isDeliveryZone())
            <ul>
                <li>{{ $order->customer_first_name }} {{ $order->customer_last_name }}</li>
                <li>{{ $order->shipping_street }} {{ $order->shipping_street_2 }}</li>
                <li>{{ $order->shipping_city }}, {{ $order->shipping_state }} {{ $order->shipping_zip }}</li>
            </ul>
            @else
            <ul>
                <li>{{ $order->present()->pickupTitle() }}</li>
                <li>{!! $order->pickup->present()->fullAddress() !!}</li>
                <li>{!! $order->pickup_date ? $order->pickup_date->format('M jS, Y') : '' !!}</li>
            </ul>
            @endif
        </td>
    </tr>
    </tbody>
</table>