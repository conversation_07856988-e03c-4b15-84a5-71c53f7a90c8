<div class="gc-modal gc-modal-mask" id="createProposalModal" @click="hideModal('createProposalModal')">
    <div class="gc-modal-wrapper">
        <div class="gc-modal-container" @click.stop>
            <form action="/admin/proposals" method="POST">
                @csrf
                <div class="gc-modal-header">
                    Create a Proposal
                </div>

                <div class="gc-modal-body">
                    {{--First name--}}
                    <div class="form-group">
                        <label for="first_name">First Name:</label>
                        <input type="text" name="first_name" class="form-control" value="{{ old('first_name') }}" />
                    </div>

                    {{--Last name--}}
                    <div class="form-group">
                        <label for="last_name">Last Name:</label>
                        <input type="text" name="last_name" class="form-control" value="{{ old('last_name') }}" />
                    </div>

                    {{--Phone--}}
                    <div class="form-group">
                        <label for="phone">Phone:</label>
                        <input type="text" name="phone" class="form-control" value="{{ old('phone') }}" />
                    </div>

                    {{--Email--}}
                    <div class="form-group">
                        <label for="email">Email:</label>
                        <input type="text" name="email" class="form-control" value="{{ old('email') }}" />
                    </div>

                    {{--Location Street--}}
                    <div class="form-group">
                        <label for="title">Street:</label>
                        <input type="text" name="street" value="{{ old('street') }}" class="form-control">
                    </div>

                    {{--Location Street 2--}}
                    <div class="form-group">
                        <label for="title">Street Line 2:</label>
                        <input type="text" name="street_2" value="{{ old('street_2') }}" class="form-control">
                    </div>

                    <div class="flex align-items-m">
                        {{--Location City--}}
                        <div class="flex-item-fill mr-sm">
                            <label for="title">City:</label>
                            <input type="text" name="city" value="{{ old('city') }}" class="form-control">
                        </div>

                        {{--Location State--}}
                        <div class="flex-item-fill mr-sm">
                            <label for="state">State</label>
                            <x-form.state-select
                                    class="form-control"
                                    name="state"
                                    :selected="old('state')"
                            />
                        </div>

                        {{--Location Zip--}}
                        <div class="flex-item-fill">
                            <label for="zip">Zip</label>
                            <input type="text" name="zip" class="form-control" value="{{ old('zip') }}" />
                        </div>
                    </div>
                </div>

                <div class="gc-modal-footer">
                    <button type="button" class="btn btn-alt" @click="hideModal('createProposalModal')">Cancel</button>
                    <button type="submit" class="btn btn-action">Create Proposal</button>
                </div>
            </form>
        </div>
    </div>
</div>
