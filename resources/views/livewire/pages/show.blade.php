@php
    /** @var App\Models\Page $page */
@endphp

<div class="mx-auto max-w-7xl">
    <div class="relative border-b border-gray-200 pb-5">
        <div class="md:flex md:items-center md:justify-between">
            <div class="min-w-0 flex-1">
                <h2 class="text-2xl/7 font-bold text-gray-900 sm:truncate sm:text-3xl sm:tracking-tight">{{ $title }}</h2>
            </div>
            <div class="mt-4 flex md:ml-4 md:mt-0">
                <button type="button" x-on:click="$dispatch('open-modal-preview-page')" class="inline-flex items-center rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z"/>
                        <path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"/>
                    </svg>
                </button>
                <a href="{{ route('page.show', [$slug]) }}" target="_blank" class="ml-3 inline-flex items-center rounded-md bg-keppel-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-keppel-700 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-keppel-600">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M13.5 6H5.25A2.25 2.25 0 0 0 3 8.25v10.5A2.25 2.25 0 0 0 5.25 21h10.5A2.25 2.25 0 0 0 18 18.75V10.5m-10.5 6L21 3m0 0h-5.25M21 3v5.25"/>
                    </svg>
                </a>
            </div>
        </div>
    </div>
    <div class="mt-4">
        <div class="block">
            <nav class="flex space-x-4" aria-label="Tabs">
                <!-- Current: "bg-gray-200 text-gray-800", Default: "text-gray-600 hover:text-gray-800" -->
                <button wire:click="set('tab', 'settings')" class="rounded-md px-3 py-2 text-sm font-medium @if($tab === 'settings') bg-gray-200 text-gray-800 hover:text-gray-800 @else text-gray-600 hover:text-gray-800 @endif">
                    Settings
                </button>
                <button wire:click="set('tab', 'widgets')" class="rounded-md px-3 py-2 text-sm font-medium @if($tab === 'widgets') bg-gray-200 text-gray-800 hover:text-gray-800 @else text-gray-600 hover:text-gray-800 @endif">
                    Widgets
                </button>
            </nav>
        </div>
    </div>

    @if($tab === 'settings')
        <div wire:key="settings-tab" class="mt-4 rounded-lg bg-white shadow">
            <div class="mx-auto max-w-2xl px-4 py-5 sm:p-6">

                <div class="mt-6 space-y-12">
                    <div class="border-b border-gray-900/10 pb-12">
                        <h2 class="text-base font-semibold leading-7 text-gray-900">Basic</h2>
                        <p class="mt-1 text-sm leading-6 text-gray-600">This information is used when displaying this page.</p>

                        <div class="mt-10 grid grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-6">


                            <div class="col-span-full">
                                <label for="slug" class="block text-sm font-medium leading-6 text-gray-900">URL</label>
                                <div class="mt-2">
                                    <div class="relative isolate flex rounded-md shadow-sm ring-1 ring-inset ring-gray-300 focus-within:ring-2 focus-within:ring-inset focus-within:ring-keppel-600 @error('slug') border-red-300 placeholder-red-300 focus:border-red-500 focus:ring-red-500 @enderror">
                                        <span class="flex select-none items-center pl-3 text-gray-500 sm:text-sm">
                                            {{ config('app.url') }}/
                                        </span>
                                        <input type="text" name="slug" id="slug" wire:model="slug" class="block flex-1 border-0 bg-transparent py-1.5 pr-10 pl-0.5 text-gray-900 placeholder:text-gray-400 focus:ring-0 sm:text-sm sm:leading-6 @error('slug') pr-10 text-red-900 placeholder-red-300 @enderror" placeholder="my-page-slug" @error('slug') aria-invalid="true" aria-describedby="slug-error" @enderror>
                                        <div class="absolute inset-y-0 right-0 flex items-center pr-3">
                                            <a href="{{ route('page.show', [$slug]) }}" target="_blank">
                                                <svg class="h-5 w-5 text-keppel-700 hover:text-keppel-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" d="M13.5 6H5.25A2.25 2.25 0 0 0 3 8.25v10.5A2.25 2.25 0 0 0 5.25 21h10.5A2.25 2.25 0 0 0 18 18.75V10.5m-10.5 6L21 3m0 0h-5.25M21 3v5.25"/>
                                                </svg>

                                            </a>

                                        </div>
                                    </div>
                                </div>
                                @error('slug')
                                <p class="m-0 mt-2 text-sm text-red-600" id="slug-error">{{ $message }}</p>
                                @enderror
                            </div>

                            <div class="sm:col-span-3">
                                <label for="title" class="block text-sm font-medium leading-6 text-gray-900">Title</label>
                                <div class="relative mt-2 rounded-md shadow-sm">
                                    <input type="text" name="title" id="title" wire:model="title"
                                           class="block w-full rounded-md border border-gray-300 focus:outline-none focus:border-keppel-500 focus:ring-keppel-500 sm:text-sm @error('title') border-red-300 pr-10 text-red-900 placeholder-red-300 focus:border-red-500 focus:ring-red-500 @enderror"
                                           @error('title') aria-invalid="true" aria-describedby="title-error" @enderror
                                    />
                                    @error('title')
                                    <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3">
                                        <svg class="h-5 w-5 text-red-500" xmlns="http://www.w3.org/2000/svg"
                                             viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                            <path fill-rule="evenodd"
                                                  d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z"
                                                  clip-rule="evenodd"/>
                                        </svg>
                                    </div>
                                    @enderror
                                </div>
                                @error('title')
                                <p class="m-0 mt-2 text-sm text-red-600" id="title-error">{{ $message }}</p>
                                @enderror
                            </div>

                            <div class="sm:col-span-6">
                                <div x-data="{ public: @js($public) }" class="flex items-center justify-between">
                                    <span class="flex grow flex-col">
                                        <span class="text-sm/6 font-medium text-gray-900" id="visibility-label">Publicly available</span>
                                        <span class="text-sm text-gray-500" id="visibility-description">Public pages can be viewed by non-administrators.</span>
                                    </span>
                                    <!-- Enabled: "bg-keppel-600", Not Enabled: "bg-gray-200" -->
                                    <button type="button" x-on:click="public = !public; $wire.public = public;" x-bind:class="{ 'bg-keppel-600': public, 'bg-gray-200': !public }" class="relative inline-flex h-6 w-11 shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-keppel-600 focus:ring-offset-2" role="switch" aria-checked="false" aria-labelledby="visibility-label" aria-describedby="visibility-description">
                                        <!-- Enabled: "translate-x-5", Not Enabled: "translate-x-0" -->
                                        <span aria-hidden="true" x-bind:class="{ 'translate-x-5': public, 'translate-x-0': !public }" class="pointer-events-none inline-block size-5  transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out"></span>
                                    </button>
                                </div>

                            </div>
                        </div>

                    </div>

                    <div class="border-b border-gray-900/10 pb-12">
                        <h2 class="text-base font-semibold leading-7 text-gray-900">SEO</h2>
                        <p class="mt-1 text-sm leading-6 text-gray-600">Configure the SEO settings for this category.</p>

                        <div class="mt-10 grid grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-6">
                            <div class="sm:col-span-4">
                                <label for="seo_meta_title" class="block text-sm font-medium leading-6 text-gray-900">Title</label>
                                <div class="relative mt-2 rounded-md shadow-sm">
                                    <input type="text" name="seo_meta_title" id="seo_meta_title" wire:model="seo_meta_title"
                                           class="block w-full rounded-md border border-gray-300 focus:outline-none focus:border-keppel-500 focus:ring-keppel-500 sm:text-sm @error('seo_meta_title') border-red-300 pr-10 text-red-900 placeholder-red-300 focus:border-red-500 focus:ring-red-500 @enderror"
                                           @error('seo_meta_title') aria-invalid="true" aria-describedby="seo_meta_title-error" @enderror
                                    />
                                    @error('seo_meta_title')
                                    <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3">
                                        <svg class="h-5 w-5 text-red-500" xmlns="http://www.w3.org/2000/svg"
                                             viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                            <path fill-rule="evenodd"
                                                  d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z"
                                                  clip-rule="evenodd"/>
                                        </svg>
                                    </div>
                                    @enderror
                                </div>
                                @error('seo_meta_title')
                                <p class="m-0 mt-2 text-sm text-red-600" id="seo_meta_title-error">{{ $message }}</p>
                                @enderror
                                <p class="mt-3 text-sm leading-6 text-gray-600">The title used in the HTML header.</p>

                            </div>

                            <div class="col-span-full">
                                <label for="seo_meta_description" class="block text-sm font-medium text-gray-700">Instructions</label>
                                <div class="relative mt-1 rounded-md shadow-sm">
                                    <textarea id="seo_meta_description" wire:model="seo_meta_description" rows="3" class="block w-full rounded-md shadow-sm border border-gray-300 focus:outline-none focus:border-keppel-500 focus:ring-keppel-500 sm:text-sm @error('seo_meta_description') border-red-300 pr-10 text-red-900 placeholder-red-300 focus:border-red-500 focus:ring-red-500 @enderror"></textarea>
                                    @error('seo_meta_description')
                                    <div class="pointer-events-none absolute top-0 right-0 flex items-center pt-3 pr-3">
                                        <!-- Heroicon name: mini/exclamation-circle -->
                                        <svg class="h-5 w-5 text-red-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd"/>
                                        </svg>
                                    </div>
                                    @enderror
                                </div>

                                @error('seo_meta_description')
                                <p class="m-0 mt-2 text-sm text-red-600" id="seo_meta_description-error">{{ $message }}</p>
                                @enderror
                                <p class="mt-2 text-sm leading-6 text-gray-600">The description used in the HTML header.</p>

                            </div>

                            <div class="sm:col-span-6">
                                <div class="flex items-center">
                                    <div class="relative flex items-start">
                                        <div class="flex h-6 items-center">
                                            <input id="visibility" aria-describedby="visibility-description" wire:model="seo_visible" name="visibility" type="checkbox" class="h-4 w-4 rounded border-gray-300 text-keppel-600 focus:ring-keppel-600">
                                        </div>
                                        <div class="ml-3 text-sm leading-6">
                                            <label for="visibility" class="font-medium text-gray-900">Visible to search engines</label>
                                            <p id="visibility-description" class="text-gray-500">Set whether this category url is indexable and followable.</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="mt-6 flex items-center justify-end gap-x-6">
                    <button type="button" wire:click="save" class="rounded-md bg-keppel-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-keppel-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-keppel-600">
                        Save
                    </button>
                </div>
            </div>
        </div>
    @elseif($tab === 'widgets')
        @php
            $widgets = collect($content ?? []);
        @endphp
        <div wire:key="widgets-tab" class="mt-4 bg-white shadow rounded-md">
            <div class="mx-auto max-w-3xl px-4 py-5 sm:p-6">
                <div class="sm:flex sm:items-center">
                    <div class="sm:flex-auto">
                        <h1 class="text-base font-semibold leading-6 text-gray-900">Widgets</h1>
                        <p class="mt-2 text-sm text-gray-700">A list of widgets assigned to this page.</p>
                    </div>

                    <div class="mt-4 sm:ml-16 sm:mt-0 sm:flex-none">
                        <button type="button"
                                wire:click="$dispatch('open-modal-add-widget')"
                                class="block rounded-md bg-keppel-600 px-3 py-2 text-center text-sm font-semibold text-white shadow-sm hover:bg-keppel-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-keppel-600">
                            Add Widget
                        </button>
                    </div>
                </div>
                @if($widgets->isEmpty())
                    <div class="mt-8 flow-root text-center">
                        <svg class="mx-auto h-12 w-12 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none"
                             viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round"
                                  d="M9.879 7.519c1.171-1.025 3.071-1.025 4.242 0 1.172 1.025 1.172 2.687 0 3.712-.203.179-.43.326-.67.442-.745.361-1.45.999-1.45 1.827v.75M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-9 5.25h.008v.008H12v-.008z"/>
                        </svg>
                        <h3 class="mt-2 text-sm font-semibold text-gray-900">No widgets</h3>
                    </div>
                @else
                    <div class="mt-2 flow-root">
                        <div class="-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
                            <div class="inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8">
                                <table class="min-w-full divide-y divide-gray-300">
                                    <thead>
                                    <tr>
                                        <th></th>
                                        <th scope="col"
                                            class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-0">
                                            Name
                                        </th>
                                    </tr>
                                    </thead>
                                    <tbody class="divide-y divide-gray-200" wire:sortable="updateWidgetOrder">
                                    @foreach($widgets as $widget)
                                        @php
                                            /** @var array{id: string, type: string, settings: array} $widget */
                                        @endphp
                                        <tr wire:sortable.item="{{ $widget['id'] }}" wire:key="widget-{{ $widget['id'] }}">
                                            <td wire:sortable.handle>
                                                <span class="fa draghandle" title="Drag to reorder"></span>
                                            </td>
                                            <td class="whitespace-nowrap py-3 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-0">
                                                <div class="flex w-full items-center justify-between text-left text-gray-900">
                                                    <span class="text-sm">{{ $widget['settings']['name'] ?? '' }}</span>
                                                    <span class="ml-6 flex h-7 items-center space-x-3">
                                                        <button type="button" class="text-gray-500 hover:text-gray-700" x-on:click="$dispatch('open-modal-preview-widget', { widget: @js($widget) })">
                                                            <svg class="size-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                                                <path stroke-linecap="round" stroke-linejoin="round" d="M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z"/>
                                                                <path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"/>
                                                            </svg>
                                                        </button>

                                                        <button type="button" class="text-gray-500 hover:text-gray-700" x-on:click="$dispatch('open-modal-edit-widget', { widget: @js($widget) })">
                                                            <svg class="size-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                                                <path stroke-linecap="round" stroke-linejoin="round" d="m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10"/>
                                                            </svg>
                                                        </button>

                                                         <button type="button" class="text-gray-500 hover:text-gray-700" wire:click="cloneWidget('{{ $widget['id'] }}')">
                                                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6">
                                                              <path stroke-linecap="round" stroke-linejoin="round" d="M15.75 17.25v3.375c0 .621-.504 1.125-1.125 1.125h-9.75a1.125 1.125 0 0 1-1.125-1.125V7.875c0-.621.504-1.125 1.125-1.125H6.75a9.06 9.06 0 0 1 1.5.124m7.5 10.376h3.375c.621 0 1.125-.504 1.125-1.125V11.25c0-4.46-3.243-8.161-7.5-8.876a9.06 9.06 0 0 0-1.5-.124H9.375c-.621 0-1.125.504-1.125 1.125v3.5m7.5 10.375H9.375a1.125 1.125 0 0 1-1.125-1.125v-9.25m12 6.625v-1.875a3.375 3.375 0 0 0-3.375-3.375h-1.5a1.125 1.125 0 0 1-1.125-1.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H9.75" />
                                                            </svg>
                                                        </button>
                                                    </span>
                                                </div>
                                            </td>
                                        </tr>
                                    @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                @endif
            </div>
        </div>
    @endif


</div>
