<div class="bg-white rounded-lg">
    <div class="p-4 sm:p-6 lg:p-8">
        <div class="sm:flex sm:items-center">
            <div class="sm:flex-auto">
                <h1 class="text-base font-semibold leading-6 text-gray-900">Restock Log</h1>
                <p class="mt-2 text-sm text-gray-700">A list of recent restock events.</p>
            </div>
            <div class="mt-4 sm:ml-16 sm:mt-0 sm:flex-none">
                <button type="button" wire:click="export" wire:target="export" wire:loading.attr="disabled" wire
                        class="block rounded-md bg-keppel-600 px-3 py-2 text-center text-sm font-semibold text-white shadow-sm hover:bg-keppel-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-keppel-600">
                    Export
                </button>
            </div>
        </div>
        <div class="mt-8">
            <div class="md:flex md:justify-between mb-4 px-4 md:p-0">
                <div class="w-full mb-4 md:mb-0 md:flex md:flex-wrap md:gap-2">

                    <div>
                        <fieldset class="p-0">
                            <legend class="sr-only">Event date</legend>
                            <div class="rounded-md bg-white shadow-sm">
                                <div class="flex -space-x-px">
                                    <div class="w-1/2 min-w-0 flex-1">
                                        <div>
                                            <label for="event_start_date" class="sr-only">Event date start</label>
                                            <div class="relative rounded-md shadow-sm">

                                                <input type="date" name="event_start_date" id="event_start_date"
                                                       pattern="\d{4}-\d{2}-\d{2}" wire:model.live="event_start_date"
                                                       class="relative block w-full rounded-none rounded-l-md border-0 bg-transparent py-1.5 text-gray-900 ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:z-10 focus:ring-2 focus:ring-inset focus:ring-keppel-600 sm:text-sm sm:leading-6">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="min-w-0 flex-1">
                                        <div>
                                            <label for="event_end_date" class="sr-only">Event date end</label>
                                            <div class="relative rounded-md shadow-sm">
                                                <input type="date" name="event_end_date" id="event_end_date"
                                                       pattern="\d{4}-\d{2}-\d{2}" wire:model.live="event_end_date"
                                                       class="relative block w-full rounded-none rounded-r-md border-0 bg-transparent py-1.5 text-gray-900 ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:z-10 focus:ring-2 focus:ring-inset focus:ring-keppel-600 sm:text-sm sm:leading-6">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </fieldset>
                    </div>


                    <div>
                        <label for="product-search" class="sr-only">Product search</label>
                        <div class="relative rounded-md shadow-sm">
                            <input type="text" wire:model.live.debounce.250ms="product_term" id="product-search"
                                   placeholder="Search products..."
                                   class="block w-full rounded-md border-0 py-1.5 pr-10 text-gray-900 ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-keppel-600 sm:text-sm sm:leading-6">
                            <div wire:loading.flex wire:target="product_term"
                                 class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3">
                                <svg class="animate-spin h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg"
                                     fill="none" viewBox="0 0 24 24">
                                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor"
                                            stroke-width="4"></circle>
                                    <path class="opacity-75" fill="currentColor"
                                          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                            </div>
                        </div>
                    </div>


                    <div class="block text-sm text-gray-700" role="menuitem">
                        <button wire:click.prevent="clearFilters"
                                x-on:click="open = false"
                                type="button"
                                class="inline-flex w-full justify-center gap-x-1.5 rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50"
                        >
                            Clear
                        </button>
                    </div>
                </div>
            </div>
        </div>

        @if($products->isEmpty())
            <div class="mt-8 flow-root text-center">
                <svg class="mx-auto h-12 w-12 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none"
                     viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round"
                          d="M9.879 7.519c1.171-1.025 3.071-1.025 4.242 0 1.172 1.025 1.172 2.687 0 3.712-.203.179-.43.326-.67.442-.745.361-1.45.999-1.45 1.827v.75M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-9 5.25h.008v.008H12v-.008z" />
                </svg>
                <h3 class="mt-2 text-sm font-semibold text-gray-900">No events</h3>
                <p class="mt-1 text-sm text-gray-500">Try updating the filters.</p>
                <div class="mt-6">
                    <button wire:click.prevent="clearFilters" type="button"
                            class="inline-flex items-center rounded-md bg-keppel-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-keppel-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-keppel-600">
                        Clear filters
                    </button>
                </div>
            </div>
        @else
            <div class="mt-8 flow-root">
                <div class="-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
                    <div class="inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8">
                        <table class="min-w-full divide-y divide-gray-300">
                            <thead>
                            <tr>
                                <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-3">Events</th>
                                <th scope="col" class="px-3 py-3.5 text-right text-sm font-semibold text-gray-900">On-site inventory</th>
                                <th scope="col" class="px-3 py-3.5 text-right text-sm font-semibold text-gray-900">Off-site inventory</th>
                                <th scope="col" class="px-3 py-3.5 text-right text-sm font-semibold text-gray-900">Subscription reserve</th>
                                <th scope="col" class="relative py-3.5 pl-3 pr-4 text-right text-sm font-semibold text-gray-900 sm:pr-3">
                                    Event time
                                </th>
                            </tr>
                            </thead>
                            <tbody class="divide-y divide-gray-200">

                            @foreach($products as $product)
                                @php /** @var \App\Models\Product $product */ @endphp
                                <tr class="border-t border-gray-200">
                                    <th scope="colgroup" class="bg-gray-50 py-2 pl-4 pr-3 text-left block text-sm font-semibold text-gray-900 sm:pl-3">
                                        <a href="{{ route('admin.products.edit', ['product' => $product, 'tab' => 'inventory']) }}"
                                           target="_blank"
                                           class="text-keppel-500 hover:text-keppel-700">{{ $product->title }}</a>
                                        <span class="font-normal text-gray-500">&nbsp;|&nbsp;{{ $product->sku }}</span>
                                    </th>
                                    <th class="bg-gray-50 py-2 pl-4 pr-3 text-right text-sm font-semibold @if($product->onSiteInventory() + $product->offSiteInventory() <= 0) text-red-600 @elseif($product->onSiteInventory() + $product->offSiteInventory() <= $product->oos_threshold_inventory) text-yellow-600 @else text-green-600 @endif sm:pl-3">
                                    </th>
                                    <th class="bg-gray-50 py-2 pl-4 pr-3 text-right text-sm font-semibold @if($product->onSiteInventory() + $product->offSiteInventory() <= 0) text-red-600 @elseif($product->onSiteInventory() + $product->offSiteInventory() <= $product->oos_threshold_inventory) text-yellow-600 @else text-green-600 @endif sm:pl-3">
                                    </th>
                                    <th class="bg-gray-50 py-2 pl-4 pr-3 text-right text-sm font-semibold @if($product->onSiteInventory() + $product->offSiteInventory() <= 0) text-red-600 @elseif($product->onSiteInventory() + $product->offSiteInventory() <= $product->oos_threshold_inventory) text-yellow-600 @else text-green-600 @endif sm:pl-3">
                                    </th>
                                    <th class="bg-gray-50 py-2 pl-4 pr-3 text-right text-sm font-semibold @if($product->onSiteInventory() + $product->offSiteInventory() <= 0) text-red-600 @elseif($product->onSiteInventory() + $product->offSiteInventory() <= $product->oos_threshold_inventory) text-yellow-600 @else text-green-600 @endif sm:pl-3">
                                    </th>
                                </tr>

                                @foreach($product->events as $event)
                                    @php /** @var \App\Models\Event $event */ @endphp

                                    <tr class="border-t border-gray-300">
                                        <td class="whitespace-nowrap py-4 pl-4 pr-3 text-sm text-gray-900 sm:pl-3">
                                            @if($event->event_id === \App\Events\Product\InventoryDecreasedToThresholdOrBelow::class)
                                                <span class="inline-flex items-center gap-x-1.5 rounded-md px-2 py-1 text-sm font-medium">
                                                    <svg class="h-5 w-5 text-yellow-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                                        <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 6 9 12.75l4.286-4.286a11.948 11.948 0 0 1 4.306 6.43l.776 2.898m0 0 3.182-5.511m-3.182 5.51-5.511-3.181" />
                                                    </svg>

                                                    Inventory Decreased To Threshold Or Below
                                                </span>
                                            @elseif($event->event_id === \App\Events\Product\InventoryDecreasedToZeroOrBelow::class)
                                                <span class="inline-flex items-center gap-x-1.5 rounded-md px-2 py-1 text-sm font-medium">
                                                    <svg class="h-5 w-5 text-red-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                                        <path stroke-linecap="round" stroke-linejoin="round" d="M12 9v3.75m9-.75a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9 3.75h.008v.008H12v-.008Z" />
                                                    </svg>
                                                    Inventory Decreased To Zero Or Below
                                                </span>
                                            @elseif($event->event_id === \App\Events\Product\InventoryIncreasedAboveZero::class)
                                                <span class="inline-flex items-center gap-x-1.5 rounded-md px-2 py-1 text-sm font-medium">
                                                    <svg class="h-5 w-5 text-purple-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                                        <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 18 9 11.25l4.306 4.306a11.95 11.95 0 0 1 5.814-5.518l2.74-1.22m0 0-5.94-2.281m5.94 2.28-2.28 5.941" />
                                                    </svg>

                                                    Inventory Increase Above Zero
                                                </span>
                                            @else
                                                <span class="inline-flex items-center gap-x-1.5 rounded-md px-2 py-1 text-sm font-medium">
                                                    <svg class="h-5 w-5 text-green-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                                        <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" />
                                                    </svg>

                                                    Inventory Increased Above Threshold
                                                </span>
                                            @endif
                                        </td>
                                        <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500 text-right">{{ $event->metadata->inventory }}</td>
                                        <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500 text-right">{{ $event->metadata->other_inventory }}</td>
                                        <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500  text-right">{{ $event->metadata->oos_threshold_inventory ?? '-' }}</td>
                                        <td class="relative whitespace-nowrap py-4 pl-3 pr-4 text-right text-sm sm:pr-3">
                                            {{ $event->created_at->format('M j | H:i A') }}
                                        </td>
                                    </tr>
                                @endforeach

                            @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        @endif
        @if($products->hasPages())
            <div class="border-t border-gray-200 mt-6 pt-6">
                {{ $products->links('pagination.livewire-tailwind') }}
            </div>
        @endif
    </div>
</div>
