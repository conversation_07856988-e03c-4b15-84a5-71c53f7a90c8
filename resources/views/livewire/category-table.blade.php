<div class="bg-white rounded-lg">
    <div class="p-4 sm:p-6 lg:p-8">
        <div class="sm:flex sm:items-center">
            <div class="sm:flex-auto">
                <h1 class="text-base font-semibold leading-6 text-gray-900">Categories</h1>
                <p class="mt-2 text-sm text-gray-700">A list of product categories.</p>
            </div>
            <div class="mt-4 sm:ml-16 sm:mt-0 sm:flex-none">
                <button type="button"
                        wire:click="$dispatch('open-modal-add-category')"
                        class="block rounded-md bg-keppel-600 px-3 py-2 text-center text-sm font-semibold text-white shadow-sm hover:bg-keppel-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-keppel-600">
                    Add Category
                </button>
            </div>
        </div>

        @if($categories->isEmpty())
            <div class="mt-8 flow-root text-center">
                <svg class="mx-auto h-12 w-12 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M9.879 7.519c1.171-1.025 3.071-1.025 4.242 0 1.172 1.025 1.172 2.687 0 3.712-.203.179-.43.326-.67.442-.745.361-1.45.999-1.45 1.827v.75M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-9 5.25h.008v.008H12v-.008z" />
                </svg>
                <h3 class="mt-2 text-sm font-semibold text-gray-900">No Categories</h3>
            </div>
        @else
            <div class="mt-8 flow-root">
                <div class="-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
                    <div class="inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8">
                        <table class="min-w-full divide-y divide-gray-300">
                            <thead>
                            <tr>
                                <th></th>
                                <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-0">
                                    Name
                                </th>
                                <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                                    Slug
                                </th>
                            </tr>
                            </thead>
                            <tbody class="divide-y divide-gray-200" wire:sortable="updateOrder">
                            @foreach($categories as $category)
                                @php /** @var \App\Models\Category $category */ @endphp
                                <tr wire:sortable.item="{{ $category->id }}" wire:key="category-{{ $category->id }}">
                                    <td wire:sortable.handle>
                                        <span class="fa draghandle" title="Drag to reorder"></span>
                                    </td>
                                    <td class="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-0">
                                        <a href="{{ route('admin.categories.edit', [$category->id]) }}" class="text-keppel-600 hover:text-keppel-500">
                                            {{ $category->name }}
                                        </a>
                                    </td>
                                    <td class="relative whitespace-nowrap py-4 pl-3 pr-4 text-sm font-medium sm:pr-0">
                                        <div class="text-gray-500">{{ $category->slug }}</div>
                                    </td>
                                </tr>
                            @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        @endif
        @if($categories->hasPages())
            <div class="border-t border-gray-200 mt-6 pt-6">
                {{ $categories->links('pagination.livewire-tailwind') }}
            </div>
        @endif
    </div>
</div>
