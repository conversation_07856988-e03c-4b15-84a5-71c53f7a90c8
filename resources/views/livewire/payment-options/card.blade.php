@php use App\Models\Card; @endphp
<section aria-labelledby="card-details-heading">
    <form wire:submit="save">
        <div class="shadow sm:overflow-hidden sm:rounded-md">
            <div class="bg-white py-6 px-4 space-y-8 sm:p-6 ">
                <div class="sm:flex sm:items-baseline sm:justify-between">
                    <div class="sm:w-0 sm:flex-1">
                        <h2 id="card-details-heading" class="text-lg font-bold text-gray-900">Credit Card</h2>
                        <p class="m-0 mt-1 max-w-2xl text-sm text-gray-500">Allow customer's to use credit cards to pay for their orders. This is the easiest
                            and most common way to accept payments. Orders will automatically be marked as paid when payments are successfully processed.</p>
                    </div>

                    <div class="mt-4 flex items-center justify-between sm:mt-0 sm:ml-6 sm:flex-shrink-0 sm:justify-start">
                        <!-- Enabled: "bg-keppel-600", Not Enabled: "bg-gray-200" -->
                        <button wire:click="toggle" type="button" class="@if($enabled) bg-keppel-600 @else bg-gray-200 @endif relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-keppel-500 focus:ring-offset-2" role="switch" aria-checked="false">
                            <span class="sr-only">Use setting</span>
                            <!-- Enabled: "translate-x-5", Not Enabled: "translate-x-0" -->
                            <span class="@if($enabled) translate-x-5 @else translate-x-0 @endif pointer-events-none relative inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out">
                                <!-- Enabled: "opacity-0 ease-out duration-100", Not Enabled: "opacity-100 ease-in duration-200" -->
                                <span class="@if($enabled) opacity-0 ease-out duration-100 @else opacity-100 ease-in duration-200 @endif absolute inset-0 flex h-full w-full items-center justify-center transition-opacity" aria-hidden="true">
                                    <svg class="h-3 w-3 text-gray-400" fill="none" viewBox="0 0 12 12">
                                        <path d="M4 8l2-2m0 0l2-2M6 6L4 4m2 2l2 2" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                                    </svg>
                                </span>
                                <!-- Enabled: "opacity-100 ease-in duration-200", Not Enabled: "opacity-0 ease-out duration-100" -->
                                <span class="@if($enabled) opacity-100 ease-in duration-200 @else opacity-0 ease-out duration-100 @endif absolute inset-0 flex h-full w-full items-center justify-center transition-opacity" aria-hidden="true">
                                    <svg class="h-3 w-3 text-keppel-600" fill="currentColor" viewBox="0 0 12 12">
                                        <path d="M3.707 5.293a1 1 0 00-1.414 1.414l1.414-1.414zM5 8l-.707.707a1 1 0 001.414 0L5 8zm4.707-3.293a1 1 0 00-1.414-1.414l1.414 1.414zm-7.414 2l2 2 1.414-1.414-2-2-1.414 1.414zm3.414 2l4-4-1.414-1.414-4 4 1.414 1.414z" />
                                    </svg>
                                </span>
                            </span>
                        </button>
                    </div>
                </div>
            </div>
            <div class="bg-white py-6 px-4 space-y-8 sm:p-6 ">

                <div class="md:grid md:grid-cols-3 md:gap-6">
                    <div class="md:col-span-1">
                        <h3 class="text-lg font-medium leading-6 text-gray-900">Messaging</h3>
                        <p class="mt-1 text-sm text-gray-500">This information is shown at checkout</p>
                    </div>
                    <div class="mt-5 space-y-6 md:col-span-2 md:mt-0">
                        <div class="grid grid-cols-3 gap-6">
                            <div class="col-span-3 sm:col-span-2">
                                <label for="credit-card-title" class="block text-sm font-medium text-gray-700">Label</label>
                                <div class="relative mt-1 rounded-md shadow-sm">
                                    <input type="text" wire:model="title" id="credit-card-title" class="block w-full rounded-md border border-gray-300 focus:outline-none focus:border-keppel-500 focus:ring-keppel-500 sm:text-sm @error('title') border-red-300 pr-10 text-red-900 placeholder-red-300 focus:border-red-500 focus:ring-red-500 @enderror" aria-invalid="true" aria-describedby="title-error">
                                    @error('title')
                                    <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3">
                                        <!-- Heroicon name: mini/exclamation-circle -->
                                        <svg class="h-5 w-5 text-red-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd" />
                                        </svg>
                                    </div>
                                    @enderror
                                </div>
                                @error('title')
                                <p class="m-0 mt-2 text-sm text-red-600" id="title-error">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>

                        <div>
                            <label for="instructions" class="block text-sm font-medium text-gray-700">Instructions</label>
                            <div class="relative mt-1 rounded-md shadow-sm">
                                <textarea id="instructions" wire:model="instructions" rows="3" class="block w-full rounded-md shadow-sm border border-gray-300 focus:outline-none focus:border-keppel-500 focus:ring-keppel-500 sm:text-sm @error('instructions') border-red-300 pr-10 text-red-900 placeholder-red-300 focus:border-red-500 focus:ring-red-500 @enderror"></textarea>
                                @error('instructions')
                                <div class="pointer-events-none absolute top-0 right-0 flex items-center pt-3 pr-3">
                                    <!-- Heroicon name: mini/exclamation-circle -->
                                    <svg class="h-5 w-5 text-red-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd" />
                                    </svg>
                                </div>
                                @enderror
                            </div>

                            <p class="m-0 mt-2 text-sm text-gray-500">Additional instructions shown to customer when this payment method is selected.</p>
                            @error('instructions')
                            <p class="m-0 mt-2 text-sm text-red-600" id="instructions-error">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>
                </div>

                <div class="md:grid md:grid-cols-3 md:gap-6">
                    <div class="md:col-span-1">
                        <h3 class="text-lg font-medium leading-6 text-gray-900">Gateway</h3>
                        <p class="mt-1 text-sm text-gray-500">The gateway credit card payments are processed through</p>
                    </div>
                    <div class="mt-5 space-y-6 md:col-span-2 md:mt-0">
                        <div class="overflow-hidden bg-white sm:rounded-md">
                            <ul role="list" class="divide-y divide-gray-200">
                                <li>
                                    <livewire:admin.payment-options.gateways.stripe :is_default="true" />
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>

            </div>
            <div class="bg-gray-50 px-4 py-3 text-right sm:px-6">
                <button type="button" disabled wire:loading class="inline-flex justify-center rounded-md border border-transparent bg-keppel-400 py-2 px-4 text-sm font-medium text-white shadow-sm focus:outline-none focus:ring-2 focus:ring-keppel-600 focus:ring-offset-2">
                    Saving...
                </button>
                <button type="submit" wire:loading.remove wire:loading.attr="disabled" class="inline-flex justify-center rounded-md border border-transparent bg-keppel-600 py-2 px-4 text-sm font-medium text-white shadow-sm hover:bg-keppel-800 focus:outline-none focus:ring-2 focus:ring-keppel-600 focus:ring-offset-2">
                    Save
                </button>
            </div>
        </div>
    </form>
</section>
