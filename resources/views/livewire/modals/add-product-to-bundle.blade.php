<x-modal>
    <form
            x-trap="open"
            wire:submit="submit"
            class="relative transform rounded-lg bg-white text-left shadow-xl sm:mx-auto sm:my-8 sm:w-full sm:max-w-2xl"
    >
        @if (!is_null($bundle_id))
            <div class="rounded-t-lg bg-white px-4 pb-4 pt-5 sm:p-6 sm:pb-4">
                <div class="sm:flex sm:items-start">
                    <div class="mx-auto flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-full bg-keppel-100 sm:mx-0 sm:h-10 sm:w-10">
                        <svg class="h-6 w-6 text-keppel-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M21 7.5l-9-5.25L3 7.5m18 0l-9 5.25m9-5.25v9l-9 5.25M3 7.5l9 5.25M3 7.5v9l9 5.25m0-9v9" />
                        </svg>
                    </div>
                    <div class="mt-3 text-center sm:ml-4 sm:mt-0 sm:text-left">
                        <h3 class="text-base font-semibold leading-6 text-gray-900" id="modal-title">Add product</h3>
                        <div class="mt-2">
                            <p class="text-sm text-gray-500">Add a new product to the bundle</p>
                        </div>
                        <div>
                            <div class="text-left mt-10 grid grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-6">
                                <div class="col-span-full sm:col-span-4">
                                    <label for="product_id" class="block text-sm font-medium text-gray-700">Product</label>
                                    <div class="relative mt-1 rounded-md shadow-sm">
                                        <livewire:admin.product-select />
                                        @error('product_id')
                                        <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3">
                                            <!-- Heroicon name: mini/exclamation-circle -->
                                            <svg class="h-5 w-5 text-red-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd" />
                                            </svg>
                                        </div>
                                        @enderror
                                    </div>
                                    @error('product_id')
                                    <p class="m-0 mt-2 text-sm text-red-600" id="product_id-error">{{ $message }}</p>
                                    @enderror
                                </div>

                                <div class="col-span-full sm:col-span-2">
                                    <label for="quantity" class="block text-sm font-medium text-gray-700">Quantity</label>
                                    <div class="relative mt-1 rounded-md shadow-sm">
                                        <input type="text" wire:model="quantity" name="quantity" id="quantity" class="block w-full rounded-md border border-gray-300 focus:outline-none focus:border-keppel-500 focus:ring-keppel-500 sm:text-sm @error('quantity') border-red-300 pr-10 text-red-900 placeholder-red-300 focus:border-red-500 focus:ring-red-500 @enderror" aria-invalid="true" aria-describedby="quantity-error">
                                        @error('quantity')
                                        <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3">
                                            <!-- Heroicon name: mini/exclamation-circle -->
                                            <svg class="h-5 w-5 text-red-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd" />
                                            </svg>
                                        </div>
                                        @enderror
                                    </div>
                                    @error('quantity')
                                    <p class="m-0 mt-2 text-sm text-red-600" id="quantity-error">{{ $message }}</p>
                                    @enderror
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="rounded-b-lg bg-gray-50 px-4 py-3 sm:flex sm:flex-row-reverse sm:px-6">
                <button type="submit" wire:target="submit" wire:loading.attr="disabled" class="inline-flex w-full justify-center rounded-md bg-keppel-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-keppel-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-keppel-600 sm:ml-3 sm:w-auto">
                    Add
                    <span wire:loading wire:target="submit" class="inline-block">ing...</span>
                </button>
                <button type="button" wire:target="submit" wire:loading.attr="disabled" wire:click="close" class="mt-3 inline-flex w-full justify-center rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50 sm:mt-0 sm:w-auto">
                    Cancel
                </button>
            </div>
        @endif
    </form>
</x-modal>
