<div class="gc-modal gc-modal-mask" id="createPickupModal" @click="hideModal('createPickupModal')">
    <div class="gc-modal-wrapper">
        <div class="gc-modal-container" @click.stop>
            <form action="/admin/logistics/pickups" method="POST" id="storePickupForm">
                @csrf
                <div class="gc-modal-header">
                    Create a Pickup Location
                </div>

                <div class="gc-modal-body">
                    <div class="form-group">
                        <label for="name">
                            Location Name
                        </label>
                        <input type="text" name="title" class="form-control" value="{{ old('title') }}" />
                    </div>

                    <div class="form-group">
                        <label for="title">Street</label>
                        <input type="text" name="street" value="{{ old('street') }}" class="form-control">
                    </div>

                    <div class="form-group">
                        <label for="title">Street Line 2</label>
                        <input type="text" name="street_2" value="{{ old('street_2') }}" class="form-control">
                    </div>
                    <div class="flex align-items-b">
                        <div class="form-group flex-item-fill mr-sm">
                            <label for="title">
                                City
                            </label>
                            <input type="text" name="city" value="{{ old('city') }}" class="form-control">
                        </div>

                        <div class="form-group flex-item-fill mr-sm">
                            <label for="state">State/Province</label>
                            <x-form.state-select
                                    class="form-control"
                                    name="state"
                                    :selected="old('state')"
                            />
                        </div>

                        <div class="form-group flex-item-fill">
                            <label for="zip">Zip</label>
                            <input type="text" name="zip" class="form-control" value="{{ old('zip') }}" />
                        </div>
                    </div>
                    <div class="form-group">
                        <label>Schedule</label>
                        <x-form.schedule-select
                                class="form-control"
                                name="schedule_id"
                                placeholder="No Schedule"
                                :only-active="false"
                        />
                    </div>
                </div>

                <div class="gc-modal-footer">
                    <button type="button" class="btn btn-alt" @click="hideModal('createPickupModal')">Cancel</button>
                    <button type="submit" class="btn btn-action" @click.prevent="submitForm('storePickupForm')">Save</button>
                </div>
            </form>
        </div>
    </div>
</div>
