@extends('layouts.main', ['pageTitle' => 'Credit Card'])

@section('toolbar-breadcrumb')
<li><a href="/admin/settings">Settings</a></li>
<li><a href="/admin/settings/payments">Payment Options</a></li>
<li><a href="/admin/settings/payments/card/edit">Credit Cards</a></li>
<li>Stripe</li>
@stop

@section('content')

<div class="wizard__container">
    <div class="wizard__innerContainer">
        <div class="wizzard__instructionsContainer">
            <div class="flex align-items-m mb-md">
                <span class="numberBullet bg-primary-8 text-primary-5 bold mr-sm">1</span>
                <span class="bold fs-2">Connect Your Stripe Account.</span>
            </div>
            <p class="lh-3 fs-1">
                Click "Connect with Stripe" and follow the on-screen instructions to create a new Stripe account or connect a new
                one.
            </p>
        </div>
        <div class="wizard__controlsContainer">
            @if(!checkStripeConnection())
                    <a href="{{ stripeConnectUrl() }}">
                        <img src="/images/stripe-connect.png" alt="Connect your Stripe account" width="190" height="33" />
                    </a>
                @else
                <div class="flex">
                    <div>
                        <p>Your Stripe account is successfully connected to your GrazeCart account.</p>
                        <a href="/admin/payments/stripe-disconnect" class="btn btn-light btn-sm">Disconnect Account</a>
                    </div>    
                </div>
            @endif
        </div>
    </div>
</div>

@if(checkStripeConnection())
<div class="wizard__container mt-lg b-none">
    <div class="wizard__innerContainer">
        <div class="wizzard__instructionsContainer">
            <div class="flex align-items-m mb-md">
                <span class="numberBullet bg-primary-8 text-primary-5 bold mr-sm">2</span>
                <span class="bold fs-2">Enable Payment Method.</span>
            </div>
            <p class="lh-3 fs-1">
                Enable Stripe as your primary credit card processor.
            </p>
        </div>
        <div class="wizard__controlsContainer">
            @if($payment->enabled && setting('payment_gateway') === 'stripe')
                <form action="/admin/payments/stripe-disconnect" method="GET">
                    @csrf
                    <button class="btn btn-danger" type="submit" value="true">Disable Stripe</button>
                </form>
            @else
                <form action="/admin/payments/stripe-reconnect" method="POST">
                    @csrf
                    <button class="btn btn-action" type="submit" value="true">Enable Stripe</button>
                </form>
            @endif
        </div>
    </div>
</div>
@endif
@stop