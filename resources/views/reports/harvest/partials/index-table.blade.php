@include('partials.saved-filters')
<div class="panel">
    <div class="panel-heading">
        <form action="{{ route('admin.reports.harvest') }}" method="GET" id="filterReportsForm" class="hidden-print">
            <input type="hidden" name="field" value="{{ request()->get('field', 'inventory') }}">
            <div class="flex align-items-m">
                <button
                    type="button"
                    class="btn btn-white flex-item br-right-0 br--l"
                    @click.stop="showPanel('filterPanel')"
                    tabindex="1"
                >Filter <span class="hide-mobile">Report</span> <i class="fas fa-caret-down"></i></button>
                <div class="flex-item-fill">
                    <button type="submit" class="btn btn-clear btn-input-overlay"><i class="fas fa-search"></i></button>
                    <input
                        tabindex="1"
                        type="text"
                        class="form-control input-overlay-left br--r"
                        placeholder="Search products by name..."
                        name="products"
                        value="{{ Request::get('products') }}"
                    >
                </div>
            </div>
            @include('reports.harvest.partials.filter-form')
        </form>
        @include('partials.applied-filters', ['filter_resource' => 'product_sales_report'])
    </div>
    <div class="panel-body pa-0">
        <div class="table-responsive">
            <table class="table table-striped table-full table-hover">
                <thead>
                <tr>
                    <th width="250">{!! sortTable('Title', 'title') !!}</th>
                    <th>{!! sortTable('Qty.', 'count_on_order') !!}</th>
                    <th>{!! sortTable('SKU', 'sku') !!}</th>
                    <th>{!! sortTable('Shelf', 'sort') !!}</th>
                    <th>{!! sortTable('Weight', 'weight') !!}</th>
                </tr>
                </thead>
                <tbody>
                <tr>
                    <td></td>
                    <td data-label="On Order"><strong>{{ $results->sum('count_on_order') }}</strong></td>
                    <td></td>
                    <td></td>
                    <td data-label="Weight"><strong>{{ weight($results->sum('weight')) }}</strong></td>
                </tr>
                @foreach($results as $result)
                    <tr>
                        <td width="250"><a href="{{ route('admin.products.edit', $result['product_id']) }}">{{ $result['title'] }}</a></td>
                        <td data-label="Qty">{{ $result->count_on_order }}</td>
                        <td data-label="SKU">{{ $result->sku }}</td>
                        <td data-label="Sort">{{ $result->sort }}</td>
                        <td data-label="Weight">{{ weight($result['weight']) }}</td>
                    </tr>
                @endforeach
                </tbody>
                @if(!$results->count())<tr><td colspan="100%">No results found.</td></tr>@endif
            </table>
        </div>
    </div>
</div>
