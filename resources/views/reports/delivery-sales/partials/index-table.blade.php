@include('partials.saved-filters')

<div class="@if(count($savedFilters)) mt-6 @endif">
    <div class="border-b border-gray-200 pb-5 sm:flex sm:items-center sm:justify-between">
        <h1 class="text-xl font-semibold text-gray-900">Delivery Sales</h1>
        <div class="mt-3 flex sm:mt-0 sm:ml-4">
            <form action="{{ route('admin.reports.delivery-sales') }}" method="GET" id="filterReportsForm"
                  class="hidden-print">
                @include('reports.delivery-sales.partials.filter-form')
                <div class="flex">
                    <button type="submit" name="export" value="true"
                            class="inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-keppel-500 focus:ring-offset-2">
                        Export
                    </button>
                    <button type="button" @click.stop="showPanel('filterPanel')"
                            class="ml-3 inline-flex items-center rounded-md border border-transparent bg-keppel-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-keppel-700 focus:outline-none focus:ring-2 focus:ring-keppel-500 focus:ring-offset-2">
                        Filter
                    </button>
                </div>
            </form>
        </div>

    </div>
    <div>
        @include('partials.applied-filters', ['filter_resource' => 'location_sales_report'])
    </div>
    @if($results->isEmpty())
        <div class="mt-8 text-center">
            <svg class="mx-auto h-12 w-12 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none"
                 viewBox="0 0 24 24" stroke="currentColor" stroke-width="1.5">
                <path stroke-linecap="round" stroke-linejoin="round"
                      d="M8 16l2.879-2.879m0 0a3 3 0 104.243-4.242 3 3 0 00-4.243 4.242zM21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900">No results</h3>
            <p class="mt-1 text-sm text-gray-500">Try filtering by different criteria.</p>
        </div>
    @else
        <div class="mt-8 flex flex-col">
            <div class="-my-2 -mx-4 overflow-x-auto sm:-mx-6 lg:-mx-8">
                <div class="inline-block min-w-full py-2 align-middle md:px-6 lg:px-8">
                    <div class="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
                        <table class="min-w-full divide-y divide-gray-300">
                            <thead class="bg-gray-50">
                            <tr>
                                <th scope="col"
                                    class="whitespace-nowrap py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6">
                                    {!! sortTableTailwind('Delivery Method', 'pickup_title') !!}
                                </th>
                                <th scope="col"
                                    class="whitespace-nowrap px-2 py-3.5 text-right text-sm font-semibold text-gray-900">
                                    {!! sortTableTailwind('Orders', 'order_count') !!}
                                </th>
                                <th scope="col"
                                    class="whitespace-nowrap px-2 py-3.5 text-right text-sm font-semibold text-gray-900">
                                    {!! sortTableTailwind('Weight', 'weight') !!}
                                </th>
                                <th scope="col"
                                    class="whitespace-nowrap py-3.5 pl-3 pr-4 text-right text-sm font-semibold text-gray-900 sm:pr-6">
                                    {!! sortTableTailwind('Subtotal', 'subtotal') !!}
                                </th>
                            </tr>
                            </thead>
                            <tbody class="divide-y divide-gray-200 bg-white">
                            @php
                                $net_orders = 0;
                                $net_weight = 0;
                                $net_subtotal = 0;
                            @endphp
                            @foreach($results as $result)
                                @php
                                    $net_orders += $result->order_count;
                                    $net_weight += $result->weight;
                                    $net_subtotal += $result->subtotal;
                                @endphp
                                <tr>
                                    <td class="py-2 pl-4 pr-3 text-sm text-gray-500 sm:pl-6">{{ $result->location }}</td>
                                    <td class="whitespace-nowrap px-2 py-2 text-right text-sm font-medium">{{ $result->order_count }}</td>
                                    <td class="whitespace-nowrap px-2 py-2 text-right text-sm font-medium">{{ weight($result->weight) }}</td>
                                    <td class="relative whitespace-nowrap py-2 pl-3 pr-4 text-right text-sm font-medium sm:pr-6">
                                        &#36;{{ money($result->subtotal) }}</td>
                                </tr>
                            @endforeach
                            </tbody>
                            <tfoot>
                            <tr>
                                <th scope="row"
                                    class="py-4 pl-4 pr-3 flex items-center text-sm font-semibold text-gray-900 sm:pl-6">
                                    Totals
                                </th>
                                <th scope="row"
                                    class="py-4 px-2 py-2 text-right text-sm font-semibold text-gray-900">{{ $net_orders }}</th>
                                <th scope="row"
                                    class="py-4 px-2 py-2 text-right text-sm font-semibold text-gray-900">{{ weight($net_weight) }}</th>
                                <td scope="row"
                                    class="py-4 pl-3 pr-4 text-right text-sm font-semibold text-gray-900 sm:pr-6">
                                    &#36;{{ money($net_subtotal) }}</td>
                            </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    @endif
</div>
