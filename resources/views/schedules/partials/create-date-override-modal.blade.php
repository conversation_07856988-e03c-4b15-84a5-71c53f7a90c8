@component('partials.modal', ['id' => 'createDateOverrideModal'])
    @php
        use App\Services\SettingsService;
        $settings_service = app(SettingsService::class);
    @endphp
    @slot('body')
        <h3 class="-mt-4 text-lg leading-6 font-medium text-gray-900" id="modal-title">
            Add Date Override
        </h3>
        <form class="mt-4 " action="{{ route('admin.schedules.overrides.store', compact('schedule')) }}" method="POST" id="storeDateOverrideForm">
            @csrf
            <div>
                <h5 class="font-medium text-base text-gray-900">Delivery Window</h5>
                <p class="mt-1 mb-0 text-gray-500">Set the target delivery date range. The window cannot overlap with another delivery window on the same schedule.</p>
                <div class="mt-3 grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
                    <div class="sm:col-span-3">
                        <label for="window_start"  class="block text-sm font-medium text-gray-700">Start</label>
                        <input type="text" id="window_start" name="window_start" class="form-control pickup-date" value="{{ old('window_start') }}"/>
                        <p class="mt-1 mb-0 text-gray-500 text-xs max-w-lg">Start of day</p>
                    </div>
                    <div class="sm:col-span-3">
                        <label for="window_end"  class="block text-sm font-medium text-gray-700">End</label>
                        <input type="text" id="window_end" name="window_end" class="form-control pickup-date" value="{{ old('window_end') }}"/>
                        <p class="mt-1 mb-0 text-gray-500 text-xs max-w-lg">End of day</p>
                    </div>
                </div>
            </div>
            <div class="mt-6 pt-4">
                <h5 class="font-medium text-base text-gray-900">Order Overrides</h5>
                <p class="mt-1 mb-0 text-gray-500">Set the desired override dates. These dates are applied to an order instead of the default repeating deadline and delivery dates.</p>
                <div class="mt-3 grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
                    <div class="sm:col-span-3">
                        <label for="deadline_date" class="block text-sm font-medium text-gray-700">Deadline</label>
                        <input type="text" id="deadline_date" name="deadline_date" class="form-control pickup-date" value="{{ old('deadline_date') }}"/>
                        <p class="mt-1 mb-0 text-gray-500 text-xs max-w-lg">{{ $settings_service->deadlineEndTime() === 24 ? 'End of day' : $settings_service->formattedDeadlineEndTime() }}</p>
                    </div>
                    <div class="sm:col-span-3">
                        <label for="delivery_date"  class="block text-sm font-medium text-gray-700">Delivery</label>
                        <input type="text" id="delivery_date" name="delivery_date" class="form-control pickup-date" value="{{ old('delivery_date') }}"/>
                    </div>
                </div>
            </div>
    @endslot

    @slot('footer')
            <button type="button" class="btn btn-alt" @click="hideModal('createDateOverrideModal')">Cancel</button>
            <button type="submit" class="btn btn-action" @click="submitForm('storeDateOverrideForm')">Add</button>
        </form>
    @endslot
@endcomponent