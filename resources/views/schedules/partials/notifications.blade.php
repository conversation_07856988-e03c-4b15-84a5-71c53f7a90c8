@php /** @var \App\Models\Schedule $schedule */ @endphp
<form class="mt-4 " action="{{ route('admin.schedules.update', $schedule->id) }}" method="POST" id="updateResourceForm">
    @csrf
    @method('PUT')
    <div class="panel panel-tabs">
        <div class="panel-body pa-0">
            <table class="table table-settings">
                <tbody>
                <tr>
                    <td>
                        <h2>Last-chance Email</h2>
                        <p class="mb-0">Configure if and when marketing notification(s) are automatically sent to eligible customers before an order window
                            closes. Eligible customers include those who:</p>
                        <ul class="pl-4 mt-1 text-xs list-disc list-outside">
                            <li>are assigned to this schedule</li>
                            <li>have not placed an order during the order window or still have time to edit their current order</li>
                            <li>do not have an active subscription</li>
                        </ul>
                    </td>
                    <td>
                        <h2>Main</h2>
                        <p>Configure the main notification.</p>
                        <div class="space-y-6">
                            <div class="radio">
                                <label class="mr-sm">
                                    <input tabindex="1" type="radio" name="reminder_enabled" value="1"
                                           onclick="toggleDisabled('.reminder', this.value)"
                                           @if($schedule->reminder_enabled) checked @endif
                                    > Enabled
                                </label>
                                <label>
                                    <input tabindex="1" type="radio" name="reminder_enabled" value="0"
                                           onclick="toggleDisabled('.reminder', this.value)"
                                           @if(!$schedule->reminder_enabled) checked @endif
                                    > Disabled
                                </label>
                            </div>
                            <div class="flex align-items-m">
                                <div class="flex-item mr-md">Email Template</div>
                                <div class="flex-item-fill">
                                    <x-form.email-template-select
                                            class="form-control reminder"
                                            name="template_id"
                                            placeholder="Select an email template"
                                            :selected="(int) $schedule->template_id"
                                    />
                                </div>
                            </div>
                            <div class="flex align-items-m mt-md mb-md flex-wrap">
                                <div class="flex-item mr-sm">
                                    Send
                                </div>
                                <input type="number" min="0" max="31" name="reminder_days"
                                       class="form-control reminder flex-item mr-sm" value="{{ $schedule->reminder_days}}"
                                       style="width: 55px;">
                                <div class="flex-item mr-sm">
                                    day(s) before deadline at
                                </div>
                                <div class="flex flex-item-fill">
                                    <x-form.reminder-hour-select
                                            class="reminder form-control flex-item mr-sm"
                                            name="reminder_hour"
                                            :selected="(int) $schedule->reminder_hour"
                                    />
                                    <x-form.reminder-minute-select
                                            class="reminder form-control flex-item mr-sm"
                                            name="reminder_minute"
                                            :selected="(int) $schedule->reminder_minute"
                                    />
                                    <x-form.reminder-meridiem-select
                                            class="reminder form-control flex-item"
                                            name="reminder_meridiem"
                                            :selected="$schedule->reminder_meridiem"
                                    />
                                </div>
                            </div>
                        </div>

                        <hr>

                        <h2>Secondary</h2>
                        @if( ! $schedule->reminder_enabled)
                            <p>Cannot be configured without a enabling the main notification</p>
                        @else
                            <p>Configure a secondary notification.</p>
                            <div class="space-y-6">
                                <div class="radio">
                                    <label class="mr-sm">
                                        <input tabindex="1" type="radio" name="secondary_reminder_enabled" value="1"
                                               onclick="toggleDisabled('.secondary_reminder', this.value)"
                                               @if($schedule->secondary_reminder_enabled) checked @endif
                                        > Enabled
                                    </label>
                                    <label>
                                        <input tabindex="1" type="radio" name="secondary_reminder_enabled" value="0"
                                               onclick="toggleDisabled('.secondary_reminder', this.value)"
                                               @if(!$schedule->secondary_reminder_enabled) checked @endif
                                        > Disabled
                                    </label>
                                </div>
                                <div class="flex align-items-m">
                                    <div class="flex-item mr-md">Email Template</div>
                                    <div class="flex-item-fill">
                                        <x-form.email-template-select
                                                class="form-control secondary_reminder"
                                                name="secondary_template_id"
                                                placeholder="Select a template"
                                                :selected="(int) $schedule->secondary_template_id"
                                        />
                                    </div>
                                </div>
                                <div class="flex align-items-m mt-md flex-wrap">
                                    <div class="flex-item mr-sm">
                                        Send
                                    </div>
                                    <input type="number" min="0" max="31" name="secondary_reminder_days"
                                           class="form-control secondary_reminder flex-item mr-sm"
                                           value="{{ $schedule->secondary_reminder_days}}" style="width: 55px;">
                                    <div class="flex-item mr-sm">
                                        day(s) before deadline at
                                    </div>
                                    <div class="flex flex-item-fill">
                                        <x-form.reminder-hour-select
                                                class="secondary_reminder form-control flex-item mr-sm"
                                                name="secondary_reminder_hour"
                                                :selected="(int) $schedule->secondary_reminder_hour"
                                        />
                                        <x-form.reminder-minute-select
                                                class="secondary_reminder form-control flex-item mr-sm"
                                                name="secondary_reminder_minute"
                                                :selected="(int) $schedule->secondary_reminder_minute"
                                        />
                                        <x-form.reminder-meridiem-select
                                                class="secondary_reminder form-control flex-item"
                                                name="secondary_reminder_meridiem"
                                                :selected="$schedule->secondary_reminder_meridiem"
                                        />
                                    </div>
                                </div>
                            </div>
                        @endif
                    </td>
                </tr>
                </tbody>
            </table>
        </div>
        <div class="panel-footer text-right">
            <button class="btn btn-action" @click="submitForm('updateResourceForm')">Save</button>
        </div>
    </div>

    <div class="panel">
        <div class="panel-body pa-0">
            <table class="table table-settings">
                <tbody>
                <tr>
                    <td>
                        <h2>Subscription Reminder</h2>
                        <p class="mb-0">Configure if and when notification(s) are automatically sent to subscribers before:
                        <ul class="pl-4 mt-1 text-xs list-disc list-outside">
                            <li>the current subscription order is longer editable by the customer</li>
                            <li>the next subscription order is generated and editable</li>
                        </ul>
                        <p class="mt-2 mb-0">Choose between using
                            <a href="{{ route('admin.apps.edit', ['app' => 'subscribe-save', 'tab' => 'notifications']) }}" target="_blank">global
                                subscription notification settings</a> or overriding the global settings when subscribed on this schedule.</p>
                    </td>
                    <td>
                        <div class="mt-sm">
                            <div class="select">
                                <div class="form-group">
                                    @php
                                        $selected = $schedule->usesGlobalMailSubscriptionDeadlineReminderSetting() ? 'global' : 'custom'
                                    @endphp
                                    <select class="form-control" name="subscription_reminder_settings_level" onChange="javascript:if(this.value == 'custom') { document.getElementById('sms_fields').disabled = false; } else { document.getElementById('sms_fields').disabled = true; };">
                                        <option value="global" @selected($selected === 'global')>Use global settings</option>
                                        <option value="custom" @selected($selected === 'custom')>Override global settings</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <br>
                        <fieldset style="padding:0;" id="sms_fields" @if($schedule->usesGlobalMailSubscriptionDeadlineReminderSetting()) disabled @endif>
                            <div class="flex align-items-m justify-between">
                                <div class="fs-1">Email</div>
                                <input type="checkbox"
                                       value="1"
                                       id="subscription_reminder_enabled"
                                       onClick="toggleAttributeOn('#subscription_reminder_enabled', 'checked')"
                                       class="switch disable-gray sms-toggle"
                                       name="subscription_reminder_enabled"
                                       @if($schedule->subscription_reminder_enabled) checked @endif
                                >
                            </div>
                            <div class="mt-sm form-group">
                                <div class="flex">
                                    <x-form.email-template-select
                                            class="form-control"
                                            name="subscription_template_id"
                                            id="subscription_template_id"
                                            placeholder="Default template"
                                            :selected="(int) $schedule->subscription_template_id"
                                    />
                                </div>
                            </div>

                            <div class="pt-md flex justify-between align-items-m">
                                <div class="fs-1">SMS</div>
                                <input type="checkbox"
                                       value="1"
                                       id="subscription_sms_reminder_enabled"
                                       onClick="toggleAttributeOn('#subscription_sms_reminder_enabled', 'checked')"
                                       class="switch disable-gray sms-toggle"
                                       name="subscription_sms_reminder_enabled"
                                       @if($schedule->subscription_sms_reminder_enabled) checked @endif
                                >
                            </div>
                            <div class="mt-sm form-group">
                                <div class="flex">
                                    <select class="form-control" name="secondary_subscription_template_id" id="secondary_subscription_template_id">
                                        <option value="" selected>Default template</option>
                                    </select>
                                </div>
                            </div>
                            <hr>
                            <div class="mt-sm">
                                <p>How soon before the deadline should the notification(s) be sent?</p>
                                <div class="flex">
                                    <div class="form-group mr-sm">
                                        <label for="subscription_reminder_days">Day(s) Before</label>
                                        <input id="subscription_reminder_days" name="subscription_reminder_days" type="text" min="1" value="{{ $schedule->subscription_reminder_days }}" class="form-control" />
                                    </div>
                                    <div class="form-group">
                                        <label for="subscription_reminder_hour">Hour(s) Before</label>
                                        <input id="subscription_reminder_hour" name="subscription_reminder_hour" type="text" min="1" value="{{ $schedule->subscription_reminder_hour }}" class="form-control" />
                                    </div>
                                </div>
                            </div>

                        </fieldset>
                    </td>
                </tr>
                </tbody>
            </table>
        </div>
        <div class="panel-footer text-right">
            <button class="btn btn-action" @click="submitForm('updateResourceForm')">Save</button>
        </div>
    </div>
</form>
