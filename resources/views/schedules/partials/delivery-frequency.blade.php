<form action="{{ route('admin.schedules.update', $schedule->id) }}" method="POST" id="updateResourceForm">
    @csrf
    @method('PUT')
    <table class="table table-settings">
        <tbody>
        <tr>
            <td>
                <h2>Delivery Frequency</h2>
                <p>How often you will make deliveries on the schedule.</p>
            </td>
            <td>
                <div class="flex align-items-m">
                    <div class="flex-item-fill mr-lg text-right">every</div>
                    <div class="flex-item">
                        <x-form.delivery-frequency-select
                                class="form-control bold"
                                name="delivery_frequency"
                                :disabled="$schedule->isActive()"
                                :selected="$schedule->delivery_frequency"
                                :appended_values="['' => 'Select...', '0' => 'Multiple days/week']"
                        />
                    </div>
                </div>
            </td>
        </tr>
        @if($schedule->isRepeating())
            <tr>
                <td class="v-top">
                    <h2>Re-Order Frequency Option(s)</h2>
                    <p>This creates subscription frequencies customers can select at checkout.</p>
                </td>
                <td class="v-top">
                    <div class=" mr-md text-right mb-md">Repeat every</div>
                    <div class="flex justify-end">
                        <div class="checkbox mr-lg flex direction-column">
                            <label class="bold">
                                <input
                                        type="checkbox"
                                        value="7"
                                        name="reorder_frequency[]"
                                        @if(in_array(7, $schedule->reorder_frequency)) checked @endif
                                        @if($schedule->delivery_frequency > 7 || $schedule->isActive()) disabled @endif
                                >
                                {{ App\Models\Schedule::$reorderFrequencies[7] }}
                            </label>
                            <label class="bold">
                                <input
                                        type="checkbox"
                                        value="28"
                                        name="reorder_frequency[]"
                                        @if(in_array(28, $schedule->reorder_frequency)) checked @endif
                                        @if($schedule->delivery_frequency > 28 || $schedule->isActive()) disabled @endif
                                >
                                {{ App\Models\Schedule::$reorderFrequencies[28] }}
                            </label>
                        </div>
                        <div class="checkbox flex mr-md direction-column">
                            <label class="bold">
                                <input
                                        type="checkbox"
                                        value="14"
                                        name="reorder_frequency[]"
                                        @if(in_array(14, $schedule->reorder_frequency)) checked @endif
                                        @if($schedule->delivery_frequency > 14 || $schedule->isActive()) disabled @endif
                                >
                                {{ App\Models\Schedule::$reorderFrequencies[14] }}
                            </label>
                            <label class="bold">
                                <input
                                        type="checkbox"
                                        value="56"
                                        name="reorder_frequency[]"
                                        @if(in_array(56, $schedule->reorder_frequency)) checked @endif
                                        @if($schedule->delivery_frequency > 56 || $schedule->isActive()) disabled @endif
                                >
                                {{ App\Models\Schedule::$reorderFrequencies[56] }}
                            </label>
                        </div>
                    </div>
                </td>
            </tr>
        @endif
        </tbody>
    </table>
    <div class="panel-footer text-right">
        @if( ! $schedule->isActive())
            <button class="btn btn-action" @click="submitForm('updateResourceForm', $event)">Save</button>
        @endif
        @if(count($schedule->reorder_frequency) > 0 && ! is_null($schedule->delivery_frequency) )
            @if( ! $schedule->isActive())
                <a href="#" @click="showModal('activateScheduleModal')" class="btn btn-clear text-gray-3">Activate</a>
            @else
                <a href="#" class="btn btn-clear" disabled>Activated</a>
            @endif
        @endif
    </div>
</form>
