@component('partials.modal', ['id' => 'createDateModal'])
    @slot('header')
        Add a Date
    @endslot
    
    @slot('body')
        <form action="/admin/schedules/{{ $schedule->id}}/dates" method="POST" id="storeDateForm">
            @csrf
            <input type="hidden" name="schedule_id" value="{{ $schedule->id }}">
            <input type="hidden" name="schedule_type_id" value="{{ $schedule->type_id }}">

            <div class="form-group">
                <label for="pickup_date">Delivery Date:</label>
                <input type="text" name="pickup_date" class="form-control pickup-date" value="{{ old('pickup_date') }}"/>
            </div>

            <div class="form-group">
                <label for="order_window">Order Window:</label>
                <input type="text" name="order_window" class="form-control order-window" value="{{ old('order_window') }}"/>
            </div>

            @if($schedule->isCustom())
                <div class="checkbox">
                    <label>
                        <input type="checkbox" name="repeat" id="repeatingDatesCheckbox"> Repeating Date
                    </label>
                </div>
                <div class="flex align-items-m ml-md mt-sm hidden" id="repeatingDatesContainer">
                    <div class="flex-item mr-sm">Repeat every</div>
                    <div class="flex-item mr-sm">
                        <select name="increments" class="form-control">
                            <option value="7">Week</option>
                            <option value="14">2 Weeks</option>
                            <option value="21">3 Week</option>
                            <option value="28">4 Weeks</option>
                            <option value="35">5 Weeks</option>
                            <option value="42">6 Weeks</option>
                            <option value="49">7 Weeks</option>
                            <option value="56">8 Weeks</option>
                            <option value="63">9 Weeks</option>
                            <option value="70">10 Weeks</option>
                            <option value="77">11 Weeks</option>
                            <option value="84">12 Weeks</option>
                        </select>
                    </div>
                    <div class="flex-item mr-sm">
                        until
                    </div>
                    <div class="flex-item">
                        <input type="text" class="repeat-stop form-control" name="repeat_stop">
                    </div>
                </div>
            @endif
    @endslot

    @slot('footer')
            <button type="button" class="btn btn-alt" @click="hideModal('createDateModal')">Cancel</button>
            <button type="submit" class="btn btn-action" @click="submitForm('storeDateForm')">Add Date</button>
        </form>
    @endslot
@endcomponent