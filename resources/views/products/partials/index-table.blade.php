@include('partials.saved-filters')
<div class="panel">
    <div class="panel-heading">
        <form action="{{ route('admin.products.index') }}" method="GET" class="hidden-print" id="filterproductsForm">
            <input type="hidden" name="field" value="{{ request()->get('field', 'inventory') }}">

            <div class="flex align-items-m">
                <button
                        type="button"
                        class="btn btn-white flex-item br-right-0 br--l"
                        @click.stop="showPanel('filterPanel')"
                        tabindex="1"
                >Filter
                    <span class="hide-mobile">Products</span>
                    <i class="fas fa-caret-down"></i></button>
                <div class="flex-item-fill">
                    <button type="submit" class="btn btn-clear btn-input-overlay"><i class="fas fa-search"></i></button>
                    <input
                            tabindex="1"
                            type="text"
                            class="form-control input-overlay-left br--r"
                            placeholder="Search products by name..."
                            name="products"
                            value="{{ Request::get('products') }}"
                    >
                </div>
            </div>
            <input type="hidden" name="orderBy" value="{{ request()->get('orderBy', 'title') }}">
            <input type="hidden" name="sort" value="{{ request()->get('sort', 'asc') }}">
            @include('products.partials.filter-form')
        </form>
        @include('partials.applied-filters', ['filter_resource' => 'product'])
    </div>
    <div class="panel-body pa-0">
        <div class="table-responsive">
            <table class="table table-striped table-full">
                <thead>
                <tr>
                    <th>{!! sortTable('Name', 'title') !!}</th>
                    <th>
                        <div class="flex align-items-m">
                            <div class="dropdown flex-item">
                                <button
                                        type="button"
                                        class="btn btn-alt btn-alt btn-link pa-0 mr-sm"
                                        data-toggle="dropdown"
                                >
                                    <i class="fas fa-cog" aria-hidden="true"></i>
                                </button>
                                <ul class="dropdown-menu pull-right">
                                    @php
                                        $selected = request('field', 'inventory');
                                        $fields = [
                                            'inventory' => 'On-site inventory',
                                            'stock_out_inventory' => 'Re-order threshold',
                                            'oos_threshold_inventory' => 'Subscription reserve',
                                            'other_inventory' => 'Off-site inventory',
                                            'sku' => 'SKU',
                                            'custom_sort' => 'Storage Location ID',
                                            'unit_price' => 'Retail Price',
                                            'sale_unit_price' => 'Retail Sale Price',
                                            'item_cost' => 'Item Cost',
                                            'unit_description' => 'Unit Description',
                                            'barcode' => 'Barcode',
                                            'notes' => 'Notes',
                                            'visible' => 'Visibility',
                                            'taxable' => 'Taxable',
                                            'fulfillment_instructions' => 'Fulfillment Instructions',
                                            'accounting_class' => 'Accounting Class',
                                            'weight' => 'Weight',
                                        ];
                                    @endphp
                                    @foreach ($fields as $key => $label)
                                        <li>
                                            <a href="/admin/products?{{ appendToUrl(['field' => $key]) }}">
                                                <i class="fa fa-fw @if($key === $selected) fa-check-circle text-primary @endif"></i>&nbsp;{{ $label }}
                                            </a>
                                        </li>
                                    @endforeach
                                </ul>
                            </div>

                            <div class="flex-item mr-sm">
                                {!! sortTable($fields[$selected] ?? 'Unknown', $selected) !!}
                            </div>
                        </div>
                    </th>
                    <th>{!! sortTable('SKU', 'sku') !!}</th>
                    <th>{!! sortTable('Retail Price', 'prices.unit_price') !!}</th>
                </tr>
                </thead>
                <tbody>
                @foreach($products as $product)
                    @php /** @var \App\Models\Product $product */ @endphp
                    <tr>
                        <td>
                            <a href="{{ route('admin.products.edit', [$product->id]) }}">{{ $product->title }}</a>
                            @if(!empty($product->vendor))
                                <div class="text-gray-medium mt-xs mb-xs fs-sm">{{ $product->vendor['title'] }}</div>
                            @endif
                            <span class="label label-light _{!! (int) !$product->visible !!}">Hidden</span>
                            <span class="label label-danger _{!! (int) $product->trashed() !!}">Deleted</span>
                        </td>
                        <td style="max-width: 100px;">
                            <form class="quickEditForm">
                                @include('products.partials.quick-edit.'.request('field', 'inventory'))
                            </form>
                        </td>
                        <td data-label="SKU">{{ $product->sku }}</td>
                        <td data-label="Retail Price">
                            &#36;{{ money($product->defaultPrice->unit_price).' '.$product->present()->unitOfIssue() }}
                        </td>
                    </tr>
                @endforeach
                @if(!$products->count())
                    <tr>
                        <td colspan="100%">
                            <div class="fs-1 bold pt-md pb-md">No products found.</div>
                        </td>
                    </tr>
                @endif
                </tbody>
            </table>
        </div>
    </div>
</div>

<div class="text-center">
    <p>Showing {{ $products->count() }} of {{ $products->total() }} result(s)</p>
    {!! $products->appends(request()->all())->render() !!}
</div>
