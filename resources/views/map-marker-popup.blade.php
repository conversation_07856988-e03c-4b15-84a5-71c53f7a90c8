<div class="text-center" style="min-width: 175px;">
<h4>{{ $location->title }}</h4>
    @if($type == 'App\Models\Pickup' && $location->isComingSoon())
        <div class="form-group"><span class="label action">Coming Soon!</span></div>
    @endif
    @if($type == 'App\Models\Pickup')
        <a href="/locations/{{ $location->slug }}">View full schedule &amp; times</a>
    @endif
<div class="form-group">
{!! $location->present()->fullAddress() !!}
</div>
@if($type != 'App\Models\Pickup' || auth()->guest())
<div class="text-center">
    <a href="{{ $buttonLinkOrAction }}" class="btn btn-cta btn-danger btn-sm" style="width: 100%; white-space: normal;">{{ $buttonText }}</a>
</div>
@else {{-- the location is a Pickup (not an "other" (retail, market, restaurant) location) and the user is signed in --}}
    @if(!$location->isComingSoon())
    <form 
        action="{{ $buttonLinkOrAction }}"
        method="POST">
            @csrf
            @method('PUT')
            <div class="text-center">
                <button class="btn btn-cta btn-danger btn-sm" type="submit" style="width: 100%;">{{ $buttonText }}</button>
            </div>
    </form>
    @endif
@endif
</div>
