@extends('layouts.main', ['pageTitle' => 'Menu Manager'])

@section('styles')
    <style>
        .draggable-list > li {
            display: flex;
            align-items: center;
            max-width: 450px;
        }

        .draggable-list > li > a {
            margin-left: 1rem;
        }

        .pull-right {
            margin-left: auto;
        }
    </style>
@endsection

{{--Toolbar--}}
@section('toolbar-breadcrumb')
    <li><a href="/admin/menus">Navigation</a></li>
    @if($menu->parent)
        <li><a href="/admin/menus/{{ $menu->parent['id'] }}/edit"> {{ $menu->parent['title'] }}</a></li>
        <li>{{ $menu->title }}</li>
    @else
        <li>{{ $menu->title }}</li>
    @endif
@endsection


@section('content')
    <div class="row">
        <div class="sidebar sidebar-1 sidebar-left" style="overflow: auto; max-height: 100vh;">
            <div class="panel-group" id="accordion" role="tablist" aria-multiselectable="true">
                <div class="space-y-3">
                    @include('menus.partials.pages')

                    @include('menus.partials.custom-pages')
                    @include('menus.partials.collections')
                    @include('menus.partials.categories')
                    @include('menus.partials.subcategories')
                    @include('menus.partials.custom-links')
                    @if(!$menu->submenu)
                        @include('menus.partials.submenu')
                    @endif
                </div>
            </div>
        </div>

        <div class="content">
            <div class="panel ">
                <div class="panel-body">
                    <div class="menuManager_menu-structure-container">
                        @if($menu->items->isEmpty())
                            <div class="form-group">
                                <div class="alert alert-info">This menu is empty. Items can be added from the panels on the left. Check an item then click "Add
                                    to Menu". You can check more then one item to add multiple items at once.
                                </div>
                            </div>
                        @else
                            <div class="alert alert-info">Click on a menu item's title to edit that item. Drag and drop menu items to arrange them in the order
                                you like.
                            </div>
                            <div id="menu-manager">
                                {{-- @include('menus.partials.edit-menu-item-modal') --}}
                                <menu-manager id="{{ $menu->id }}"></menu-manager>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
