<div x-data="{ expanded: false }" class="bg-white">
    <div>
        <!-- Expand/collapse question button -->
        <button x-on:click="expanded = ! expanded" type="button" class="px-4 py-3 flex w-full items-start justify-between text-left text-gray-900" aria-controls="faq-0" aria-expanded="false">
            <span class="text-base font-semibold leading-7">Product Collections</span>
            <span class="ml-6 flex h-7 items-center">
                <!-- Icon when question is collapsed. -->
                <svg class="h-6 w-6" x-bind:class="{ 'hidden': expanded }" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true" data-slot="icon">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M12 6v12m6-6H6" />
                </svg>
                <!-- Icon when question is expanded. -->
                <svg class="h-6 w-6 hidden" x-bind:class="{ 'hidden': !expanded }" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true" data-slot="icon">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M18 12H6" />
                </svg>
            </span>
        </button>
    </div>
    <div x-show="expanded" x-collapse class="pb-4">
        <div class="px-4 text-base leading-7 text-gray-600">
            <form action="{{ route('admin.menu-items.store') }}" method="POST">
                @csrf
                <input type="hidden" name="type" value="collection" />
                <input type="hidden" name="menu_id" value="{{ $menu->id }}" />
                <div class="panel-group--fixed-height">
                    <input type="hidden" name="items">
                    @foreach($collections as $collection)
                        <div class="checkbox">
                            <label>
                                <input type="checkbox" value="{{ $collection->id }}" name="items[]">{{ $collection->title }}
                            </label>
                        </div>
                    @endforeach
                </div>
                <div class="text-right">
                    <button type="submit" class="btn btn-default btn-sm">Add to Menu</button>
                </div>
            </form>
        </div>
    </div>
</div>
