@extends('settings.layout', ['settingTitle' => 'Terms of Service'])

@section('setting_toolbar')
<button class="btn btn-success" @click="submitForm('settingsForm')">Save</button>
@stop

@section('setting_content')
<form action="{{ route('admin.settings.update') }}" method="POST" id="settingsForm">
    @csrf
    @method('PUT')
    <div class="panel">
        <div class="panel-body pa-0">
            <table class="table   table-striped table-settings">
                <tbody>
                <tr>
                    <td>
                        <h2>Require a Terms of Service Agreement</h2>
                        <p>Require your customers to agree to terms before placing their first order.</p>
                    </td>
                    <td>
                        <div class="radio">
                            <label class="mr-sm">
                                <input tabindex="1" type="radio" name="settings[require_checkout_agreement]" value="1" @if(setting('require_checkout_agreement', false)) checked @endif> Yes
                            </label>
                            <label>
                                <input tabindex="1" type="radio" name="settings[require_checkout_agreement]" value="0" @if(!setting('require_checkout_agreement', false)) checked @endif> No
                            </label>
                        </div>
                    </td>
                </tr>
                </tbody>
            </table>
        </div>
        <div class="panel-footer text-right">
            <button class="btn btn-action" @click="submitForm('settingsForm', $event)">Save</button>
        </div>
    </div>        
</form>
@stop