@php /** @var \App\Services\SubscriptionSettingsService $subscription_settings_service */ @endphp

<div class="space-y-6 pb-12 sm:px-6 lg:col-span-9 lg:px-0">

    <form action="{{ route('admin.integrations.subscribe-save.update') }}" method="POST">
        @csrf
        @method('PUT')
        <div class="shadow sm:overflow-hidden sm:rounded-lg">
            <div class="space-y-6 bg-white py-6 px-4 sm:p-6">
                <div class="md:grid md:grid-cols-3 md:gap-6">
                    <div class="md:col-span-1">
                        <h3 class="text-lg font-medium leading-6 text-gray-900">Included Product</h3>
                        <p class="mt-1 text-sm text-gray-500">Set the products that are available to be included with every subscription order (for example, a complementary item). When multiple options are available, customers confirm their selected product at checkout.</p>
                    </div>
                    <div class="mt-5 space-y-6 md:col-span-2 md:mt-0">
                        <div class="grid grid-cols-3 gap-6">
                            <div class="col-span-3">
                                <label for="recurring_orders_default_item" class="block text-sm font-medium text-gray-700">Default</label>
                                <div class="mt-1">
                                    <select
                                            id="recurring_orders_default_item"
                                            class="block w-64 rounded-md border-gray-300 py-2 pl-3 pr-10 text-base focus:border-keppel-500 focus:outline-none focus:ring-keppel-500 sm:text-sm select2--products"
                                            name="settings[recurring_orders_default_item]"
                                            tabindex="1"
                                            data-placeholder="Select product..."
                                    >
                                        <option value="{{ old('recurring_orders_default_item', $subscription_settings_service->defaultProductIncentiveId()) }}" selected="selected">
                                            {{ $subscription_settings_service->defaultProductIncentive()?->title ?? "Select product..." }}
                                        </option>
                                    </select>
                                </div>
                                <p class="m-0 mt-2 text-sm leading-5 text-gray-500">Automatically selected upon subscription opt-in.</p>
                            </div>
                            <div class="col-span-3">
                                <label for="recurring_orders_option_one" class="block text-sm font-medium text-gray-700">Alternative One</label>
                                <div class="mt-1">
                                    <select
                                            id="recurring_orders_option_one"
                                            class="block w-64 rounded-md border-gray-300 py-2 pl-3 pr-10 text-base focus:border-keppel-500 focus:outline-none focus:ring-keppel-500 sm:text-sm select2--products"
                                            name="settings[recurring_orders_option_one]"
                                            tabindex="1"
                                            data-placeholder="Select product..."
                                    >
                                        <option value="{{ old('recurring_orders_option_one', $subscription_settings_service->alternativeOneProductIncentiveId()) }}" selected="selected">
                                            {{ $subscription_settings_service->alternativeOneProductIncentive()?->title ?? "Select product..." }}
                                        </option>
                                    </select>
                                </div>
                                <p class="m-0 mt-2 text-sm leading-5 text-gray-500">Available to select at checkout.</p>
                            </div>

                            <div class="col-span-3">
                                <label for="recurring_orders_option_two" class="block text-sm font-medium text-gray-700">Alternative Two</label>
                                <div class="mt-1">
                                    <select
                                            id="recurring_orders_option_two"
                                            class="block w-64 rounded-md border-gray-300 py-2 pl-3 pr-10 text-base focus:border-keppel-500 focus:outline-none focus:ring-keppel-500 sm:text-sm select2--products"
                                            name="settings[recurring_orders_option_two]"
                                            tabindex="1"
                                            data-placeholder="Select product..."
                                    >
                                        <option value="{{ old('recurring_orders_option_two', $subscription_settings_service->alternativeTwoProductIncentiveId()) }}" selected="selected">
                                            {{ $subscription_settings_service->alternativeTwoProductIncentive()?->title ?? "Select product..." }}
                                        </option>
                                    </select>
                                </div>
                                <p class="m-0 mt-2 text-sm leading-5 text-gray-500">Available to select at checkout.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="bg-gray-50 px-4 py-3 text-right sm:px-6">
                <button type="submit" class="inline-flex justify-center rounded-md border border-transparent bg-keppel-600 py-2 px-4 text-sm font-medium text-white shadow-sm hover:bg-keppel-700 focus:outline-none focus:ring-2 focus:ring-keppel-500 focus:ring-offset-2">Save</button>
            </div>
        </div>
    </form>
</div>
