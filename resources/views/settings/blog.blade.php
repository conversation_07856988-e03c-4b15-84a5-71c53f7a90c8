@extends('settings.layout', ['settingTitle' => 'Blog'])

@section('setting_toolbar')
<button class="btn btn-success" @click="submitForm('settingsForm')">Save</button>
@stop

@section('setting_content')
<form action="{{ route('admin.settings.update') }}" method="POST" id="settingsForm">
    @csrf
    @method('PUT')
    <div class="panel">
        <div class="panel-body pa-0">
            <table class="table table-striped table-settings">
                <tbody>
                <tr>
                    <td>
                        <h2>Blog Posts</h2>
                        <p>How many blog posts should show at one time.</p>
                    </td>
                    <td>
                        <input type="number" name="settings[blog_posts_per_page]" class="form-control"
                               value="{{ old('blog_posts_per_page', setting('blog_posts_per_page')) }}"/>
                    </td>
                </tr>

                <tr>
                    <td>
                        <h2>Enable Comments</h2>
                        <p>Should comments be allowed on blog posts.</p>
                    </td>
                    <td>
                       <select class="form-control" name="settings[blog_comments_enabled]">
                           {!! selectOptions([
                               1 => 'Enabled',
                               0 => 'Disabled'
                           ], setting('blog_comments_enabled', 1)) !!}
                       </select>
                    </td>
                </tr>
                
                @if(setting('blog_comments_enabled', 1))
                    <tr>
                        <td>
                            <h2>Comments Provider</h2>
                            <p>Which service to use for blog post comments.</p>
                        </td>
                        <td>
                        <select class="form-control" name="settings[comments_provider]">
                            {!! selectOptions([
                                'facebook' => 'Facebook',
                                'disqus' => 'Disqus'
                            ], setting('comments_provider', 'facebook')) !!}
                        </select>
                        </td>
                    </tr>

                    @if(setting('comments_provider') === 'facebook')
                    <tr>
                        <td>
                            <h2>Facebook App ID</h2>
                        </td>
                        <td>
                            <input 
                                type="text" 
                                name="settings[facebook_app_id]" 
                                class="form-control"
                                value="{{ old('facebook_app_id', setting('facebook_app_id')) }}"
                            />
                        </td>
                    </tr>
                    @endif
                
                    @if(setting('comments_provider') === 'disqus')
                    <tr>
                        <td>
                            <h2>Discus Website Shortname</h2>
                            <p>Reqired. This can be found in your Disqus settings.</p>
                        </td>
                        <td>
                            <input 
                                type="text" 
                                name="settings[disqus_forum_shortname]" 
                                class="form-control"
                                value="{{ old('disqus_forum_shortname', setting('disqus_forum_shortname')) }}"
                            />
                        </td>
                    </tr>
                    @endif
                @endif    
                </tbody>
            </table>
        </div>
        <div class="panel-footer text-right">
            <button class="btn btn-action" @click="submitForm('settingsForm', $event)">Save</button>
        </div>
    </div>           
</form>
@stop
