@extends('settings.layout', ['settingTitle' => 'Customers'])

@php
    $settings_service = app(\App\Services\SettingsService::class);
    $starting_credit_balance = $settings_service->startingStoreCreditBalance();
    $new_customer_payout = $settings_service->newCustomerReferralBonus();
    $show_checkout_registration_message =  ! app()->environment('production');
@endphp

@section('setting_toolbar')
    <button class="btn btn-success" @click="submitForm('settingsForm')">Save</button>
@stop

@section('setting_content')
    <div class="max-w-7xl mx-auto">
        <form action="{{ route('admin.settings.customer.update') }}" method="POST" id="settingsForm">
            @csrf
            @method('PUT')
            <div class="space-y-6 mb-10 ">
                <div class="bg-white shadow px-4 py-5 sm:rounded-lg sm:p-6">
                    <div class="md:grid md:grid-cols-3 md:gap-6">
                        <div class="align-top md:col-span-1 px-4 sm:px-0">
                            <h3 class="text-lg font-medium leading-6 text-gray-900">Authentication</h3>
                            <p class="mt-1 text-sm text-gray-500 max-w-lg">Set where and when your visitors are required to sign in/sign up as well as provide their postal code.</p>
                        </div>
                        <div class="mt-5 md:mt-0 md:col-span-2 px-4 sm:px-0">
                            <label for="registration_wall" class="block text-sm font-medium text-gray-700">Sign in / Sign up Location</label>
                            <p class="mt-1 text-sm text-gray-500 max-w-lg">Set where visitors must sign in to your site. All customers must be signed in to place an order.</p>

                            @php $registration_wall = $settings_service->registrationWall(); @endphp
                            <select id="registration_wall"
                                    name="registration_wall"
                                    class="mt-1 block pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-keppel-500 focus:border-keppel-500 sm:text-sm rounded-md"
                            >
                                @if ( ! app()->environment('production'))
                                    <option value="checkout" @if($registration_wall === 'checkout') selected @endif>
                                        Checkout (Recommended)
                                    </option>
                                @endif
                                <option value="cart" @if($registration_wall === 'cart') selected @endif>Add product to
                                    cart
                                </option>
                                <option value="prices" @if($registration_wall === 'prices') selected @endif>View product
                                    prices
                                </option>
                                <option value="store" @if($registration_wall === 'store') selected @endif>View store
                                </option>
                                <option value="site" @if($registration_wall === 'site') selected @endif>View site
                                </option>
                            </select>

                            @php $location_wall = $settings_service->customerLocationWall(); @endphp
                            <label for="customer_location_wall" class="mt-8 block text-sm font-medium text-gray-700">Geofencing</label>
                            <p class="mt-1 text-sm text-gray-500 max-w-lg">Set where visitors are required to enter their postal code and choose how to shop (delivery vs pickup). @if($show_checkout_registration_message)
                                    This setting only has an effect when the sign in/sign up location is set to"Checkout".
                                @endif</p>
                            <select id="customer_location_wall"
                                    name="customer_location_wall"
                                    class="mt-1 w-56 block pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-keppel-500 focus:border-keppel-500 sm:text-sm rounded-md"
                            >
                                @if ( ! app()->environment('production'))
                                    <option value="off" @if(($location_wall === 'off')) selected @endif>Disabled
                                    </option>
                                @endif
                                <option value="cart" @if($location_wall === 'cart') selected @endif>On cart start
                                </option>
                                <option value="store" @if($location_wall === 'store') selected @endif>On store view
                                </option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="bg-white shadow px-4 py-5 sm:rounded-lg sm:p-6">
                    <div class="md:grid md:grid-cols-3 md:gap-6">
                        <div class="align-top md:col-span-1 px-4 sm:px-0">
                            <h3 class="text-lg font-medium leading-6 text-gray-900">Registration</h3>
                            <p class="mt-1 text-sm text-gray-500 max-w-lg">Customize how your registration process works.</p>
                        </div>
                        <div class="mt-5 md:mt-0 md:col-span-2 px-4 sm:px-0">
                            <label for="user_registration_credit" class="mt-8 block text-sm font-medium text-gray-700">Starting credit balance</label>
                            <p class="mt-1 text-sm text-gray-500 max-w-lg">The amount of store credit each new customer starts with</p>

                            <div class="mt-1 relative rounded-md shadow-sm w-56">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <span class="text-gray-500 sm:text-sm"> $ </span>
                                </div>
                                <input type="text" id="user_registration_credit" name="user_registration_credit"
                                       value="{{ old('user_registration_credit', number_format($starting_credit_balance / 100, 2)) }}"
                                       class="focus:ring-keppel-500 focus:border-keppel-500 block w-full pl-7 pr-12 sm:text-sm border-gray-300 rounded-md"
                                       placeholder="0.00"/>
                            </div>

                            <div class="mt-8">
                                <label class="block text-sm font-medium text-gray-700">Manual account activation</label>
                                <p class="mb-0 text-sm leading-5 text-gray-500 max-w-lg">Set whether all new accounts require manual activation. New customers cannot place orders until after account activation. This setting is ignored when authenticating at checkout.</p>
                                <fieldset class="mt-2">
                                    <div class="space-y-4 sm:flex sm:items-center sm:space-y-0 sm:space-x-10">
                                        <div class="flex items-center">
                                            <input id="require_user_activation" name="require_user_activation"
                                                   value="1" type="radio"
                                                   @if(setting('require_user_activation', false)) checked
                                                   @endif class="focus:ring-keppel-500 h-4 w-4 text-keppel-600 border-gray-300"/>
                                            <label for="require_user_activation"
                                                   class="ml-3 block text-sm font-medium text-gray-700">
                                                Enabled </label>
                                        </div>

                                        <div class="flex items-center">
                                            <input id="do_not_require_user_activation"
                                                   name="require_user_activation" value="0" type="radio"
                                                   @if(!setting('require_user_activation', false)) checked
                                                   @endif class="focus:ring-keppel-500 h-4 w-4 text-keppel-600 border-gray-300"/>
                                            <label for="do_not_require_user_activation"
                                                   class="ml-3 block text-sm font-medium text-gray-700">
                                                Disabled </label>
                                        </div>
                                    </div>
                                </fieldset>
                            </div>
                            <div class="mt-8">
                                <label class="block text-sm font-medium text-gray-700">Auto Subscribe</label>
                                <p class="mb-0 text-sm leading-5 text-gray-500 max-w-lg">Automatically subscribe a new user to all integrated email marketing services when they create an account.</p>
                                <fieldset class="mt-2">
                                    <div class="space-y-4 sm:flex sm:items-center sm:space-y-0 sm:space-x-10">
                                        <div class="flex items-center">
                                            <input id="newsletter_auto_opt_in_on" name="newsletter_auto_opt_in"
                                                   value="1" type="radio"
                                                   @if(setting('newsletter_auto_opt_in', false)) checked
                                                   @endif class="focus:ring-keppel-500 h-4 w-4 text-keppel-600 border-gray-300"/>
                                            <label for="newsletter_auto_opt_in_on"
                                                   class="ml-3 block text-sm font-medium text-gray-700">
                                                Enabled </label>
                                        </div>

                                        <div class="flex items-center">
                                            <input id="newsletter_auto_opt_in_off" name="newsletter_auto_opt_in"
                                                   value="0" type="radio"
                                                   @if(!setting('newsletter_auto_opt_in', false)) checked
                                                   @endif class="focus:ring-keppel-500 h-4 w-4 text-keppel-600 border-gray-300"/>
                                            <label for="newsletter_auto_opt_in_off"
                                                   class="ml-3 block text-sm font-medium text-gray-700">
                                                Disabled </label>
                                        </div>
                                    </div>
                                </fieldset>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="bg-white shadow px-4 py-5 sm:rounded-lg sm:p-6">
                    <div class="md:grid md:grid-cols-3 md:gap-6">
                        <div class="align-top md:col-span-1 px-4 sm:px-0">
                            <h3 class="text-lg font-medium leading-6 text-gray-900">Referrals</h3>
                            <p class="mt-1 text-sm text-gray-500 max-w-lg">Allow new and existing customers to earn
                                bonus store credit after a new customer places their first order with an existing
                                customers unique referral link.</p>
                        </div>
                        <div class="mt-5 md:mt-0 md:col-span-2 px-4 sm:px-0">
                            <label class="block text-sm font-medium text-gray-700">Status</label>
                            <p class="mt-1 mb-0 text-sm text-gray-500">Set whether your referral program is active or
                                not.</p>

                            <fieldset class="mt-1">
                                <div class="space-y-4 sm:flex sm:items-center sm:space-y-0 sm:space-x-10">
                                    <div class="flex items-center">
                                        <input id="enable_referrals" name="enable_referrals" value="1" type="radio"
                                               @if(setting('enable_referrals', false)) checked
                                               @endif class="focus:ring-keppel-500 h-4 w-4 text-keppel-600 border-gray-300">
                                        <label for="enable_referrals"
                                               class="ml-3 block text-sm font-medium text-gray-700"> Enabled </label>
                                    </div>

                                    <div class="flex items-center">
                                        <input id="do_not_enable_referrals" name="enable_referrals" value="0"
                                               type="radio" @if(!setting('enable_referrals', false)) checked
                                               @endif class="focus:ring-keppel-500 h-4 w-4 text-keppel-600 border-gray-300">
                                        <label for="do_not_enable_referrals"
                                               class="ml-3 block text-sm font-medium text-gray-700"> Disabled </label>
                                    </div>
                                </div>
                            </fieldset>

                            @if(setting('enable_referrals'))
                                <label for="referral_bonus" class="mt-8 block text-sm font-medium text-gray-700">Existing
                                    customer bonus</label>
                                <p class="m-0 mt-1 text-sm text-gray-500 max-w-lg">Set the amount of store credit that
                                    should go to the customer who refers a new customer.</p>


                                <div class="mt-2 relative rounded-md shadow-sm w-56">
                                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                        <span class="text-gray-500 sm:text-sm"> $ </span>
                                    </div>
                                    <input type="text" id="referral_bonus" name="referral_bonus"
                                           value="{{ old('referral_bonus', number_format($settings_service->existingCustomerReferralBonus() / 100, 2)) }}"
                                           class="focus:ring-keppel-500 focus:border-keppel-500 block w-full pl-7 pr-12 sm:text-sm border-gray-300 rounded-md"
                                           placeholder="0.00"/>
                                </div>

                                <div class="mt-3 inline-flex items-center bg-gray-50 px-4 py-3 rounded-md">
                                    <svg class="-ml-1 w-4 h-4 text-gray-400" xmlns="http://www.w3.org/2000/svg"
                                         fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round"
                                              d="M11.25 11.25l.041-.02a.75.75 0 011.063.852l-.708 2.836a.75.75 0 001.063.853l.041-.021M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-9-3.75h.008v.008H12V8.25z"/>
                                    </svg>
                                    <p class="m-0 ml-3 text-sm text-gray-500">Credit awarded upon payment of referred
                                        customer's first order</p>
                                </div>


                                <label for="referral_payout" class="mt-8 block text-sm font-medium text-gray-700">New
                                    customer bonus</label>
                                <p class="m-0 mt-1 text-sm text-gray-500 max-w-lg">Set the amount of store credit that
                                    should go to new customer. This is added to the starting credit balance (if
                                    enabled).</p>

                                <div class="mt-2 flex rounded-md shadow-sm md:w-1/2">
                                    <span class="inline-flex items-center px-3 rounded-l-md border border-r-0 border-gray-300 bg-gray-50 text-gray-500 sm:text-sm">
                                        ${{ number_format($starting_credit_balance / 100 , 2) }}&nbsp; + </span>
                                    <input type="text" name="referral_payout" id="referral_payout"
                                           class="flex-1 min-w-0 block w-full px-3 py-2 rounded-none focus:ring-keppel-500 focus:border-keppel-500 sm:text-sm border-gray-300"
                                           value="{{ old('referral_payout', number_format($new_customer_payout / 100, 2)) }}">
                                    <span class="inline-flex items-center px-3 rounded-r-md border border-l-0 border-gray-300 bg-gray-50 text-gray-500 sm:text-sm">
                                        =
                                        &nbsp;${{ number_format(($starting_credit_balance + $new_customer_payout) / 100, 2) }}
                                        &nbsp;</span>
                                </div>

                                <div class="mt-3 inline-flex items-center bg-gray-50 px-4 py-3 rounded-md">
                                    <svg class="-ml-1 w-4 h-4 text-gray-400" xmlns="http://www.w3.org/2000/svg"
                                         fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round"
                                              d="M11.25 11.25l.041-.02a.75.75 0 011.063.852l-.708 2.836a.75.75 0 001.063.853l.041-.021M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-9-3.75h.008v.008H12V8.25z"/>
                                    </svg>

                                    <p class="m-0 ml-3 text-sm text-gray-500">Credit awarded upon registration of new
                                        customer</p>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
                <div class="bg-white shadow px-4 py-5 sm:rounded-lg sm:p-6">
                    <div class="md:grid md:grid-cols-3 md:gap-6">
                        <div class="align-top md:col-span-1 px-4 sm:px-0">
                            <h3 class="text-lg font-medium leading-6 text-gray-900">Redirects</h3>
                            <p class="mt-1 text-sm text-gray-500 max-w-lg">Set the URL customers are redirected to after
                                completing various actions.</p>
                        </div>
                        <div class="mt-5 md:mt-0 md:col-span-2 px-4 sm:px-0">
                            <label for="user_registration_credit" class="block text-sm font-medium text-gray-700">After
                                registration</label>
                            <div class="mt-1 md:w-1/2">
                                <input type="text" name="user_registration_redirect"
                                       class="shadow-sm focus:ring-keppel-500 focus:border-keppel-500 block w-full sm:text-sm border-gray-300 rounded-md"
                                       value="{{ old('user_registration_redirect', setting('user_registration_redirect')) }}"
                                       placeholder="Last viewed page"/>
                            </div>
                            @if ( ! app()->environment('production'))
                                <p class="mt-2 text-sm text-gray-500">Ignored when authenticating at checkout.</p>
                            @endif

                            <label for="user_login_redirect" class="mt-8 block text-sm font-medium text-gray-700">After
                                log in</label>
                            <div class="mt-1 md:w-1/2">
                                <input type="text" name="user_login_redirect"
                                       class="shadow-sm focus:ring-keppel-500 focus:border-keppel-500 block w-full sm:text-sm border-gray-300 rounded-md"
                                       value="{{ old('user_login_redirect', setting('user_login_redirect')) }}"
                                       placeholder="Last viewed page"/>
                            </div>
                            @if ( ! app()->environment('production'))
                                <p class="mt-2 text-sm text-gray-500">Ignored when authenticating at checkout.</p>
                            @endif

                            <label for="lead_capture_url" class="mt-8 block text-sm font-medium text-gray-700">After
                                lead capture</label>
                            <div class="mt-1 md:w-1/2">
                                <input type="text" name="lead_capture_url" id="lead_capture_url"
                                       class="shadow-sm focus:ring-keppel-500 focus:border-keppel-500 block w-full sm:text-sm border-gray-300 rounded-md"
                                       value="{{ old('lead_capture_url', setting('lead_capture_url')) }}"
                                       placeholder="Last viewed page"/>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="bg-white shadow px-4 py-5 sm:rounded-lg sm:p-6">
                    <div class="md:grid md:grid-cols-3 md:gap-6">
                        <div class="align-top md:col-span-1 px-4 sm:px-0">
                            <h3 class="text-lg font-medium leading-6 text-gray-900">Exports</h3>
                        </div>
                        <div class="mt-5 md:mt-0 md:col-span-2 px-4 sm:px-0">
                            <label for="user_export_format"
                                   class="block text-sm font-medium text-gray-700">Format</label>
                            <select name="user_export_format"
                                    class="mt-1 block pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-keppel-500 focus:border-keppel-500 sm:text-sm rounded-md">
                                {!! selectOptions([
                                    'default' => 'Default',
                                    'drip' => 'Drip'
                                ], setting('user_export_format')) !!}
                            </select>
                        </div>
                    </div>
                </div>

                <div class="flex justify-end">
                    <button type="button" @click="submitFormAsync('settingsForm', $event)"
                            class="ml-3 inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-keppel-600 hover:bg-keppel-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-keppel-500">
                        Save
                    </button>
                </div>
            </div>
        </form>
    </div>
@stop
