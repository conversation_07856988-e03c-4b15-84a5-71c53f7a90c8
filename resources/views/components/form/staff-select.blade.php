@props(['selected' => null, 'placeholder' => null, 'include_not_assigned' => false])

@php
    $staff = \App\Models\User::query()
         ->staff()
         ->select(['id', 'first_name', 'last_name', 'full_name'])
         ->orderBy('first_name')
         ->get()
         ->pluck('full_name', 'id')
@endphp
<select {{ $attributes }}>
    @if($placeholder)
        <option value="">{{ $placeholder }}</option>
    @endif

    @foreach($staff as $id => $full_name)
        <option value="{{ $id }}" @selected($id == $selected)>{{ $full_name }}</option>
    @endforeach
    @if($include_not_assigned)
        <option value="0" @selected(! is_null($selected) && 0 === ((int) $selected))>Not assigned</option>
    @endif
</select>
