@props(['selected' => null, 'placeholder' => null])

@php
    $pages = \App\Models\Page::pluck('title', 'slug');

    $redirects = [
        'redirect:store' => 'Redirect to store',
        'redirect:locations' => 'Redirect to locations map',
        'redirect:register' => 'Redirect to registration',
        'redirect:login' => 'Redirect to login',
    ];
@endphp

<select {{ $attributes }}>
    @if($placeholder)
        <option value="">{{ $placeholder }}</option>
    @endif
    @foreach($pages as $slug => $title)
        <option value="{{ $slug }}" @selected($slug === $selected)>{{ $title }}</option>
    @endforeach

    <optgroup label="Redirect">
        @foreach($redirects as $slug => $title)
            <option value="{{ $slug }}" @selected($slug === $selected)>{{ $title }}</option>
        @endforeach
    </optgroup>
</select>
