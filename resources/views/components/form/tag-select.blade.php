@props(['selected' => null, 'placeholder' => null])

@php
    $selected_array = \Illuminate\Support\Arr::wrap($selected);
@endphp

<select {{ $attributes }}>
    @if($placeholder)
        <option value="">{{ $placeholder }}</option>
    @endif
    @foreach(\App\Models\Tag::pluck('title', 'id') as $id => $title)
        <option value="{{ $id }}" @selected(in_array($id, $selected_array))>{{ $title }}</option>
    @endforeach
</select>

