@props([
    'head' => null,
    'count' => 0,
    'total' => 0,
    'values' => [],
])
<div
        x-data="{
            selected: [],
            bulkSelectOption: null,
            handleBulkTypeChange(event) {
                let valuesToSelect = [];

                if (event.detail !== null) {
                    valuesToSelect = @json($values);
                }

                this.selected = valuesToSelect;
                this.bulkSelectOption = event.detail;
            }
        }"
        x-on:bulk-type-change="handleBulkTypeChange"
        x-init="$watch('selected', value => {
            if (value.length < {{ $count }}) {
                $dispatch('deleselect-bulk-type');
            }
        })"
        {{ $attributes }}
>
    <div class="flow-root">
        <div class="-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
            <div class="min-w-full sm:px-6 lg:px-8">
                <div class="flex items-center border-b border-gray-200 ">
                    <div class="relative h-12 min-w-full px-7 sm:w-12 sm:px-6 ">
                        <div class="absolute z-[1] left-0 top-1/2 translate-y-[-50%]">
                            <div class="flex items-center ">
                                <x-select-table.bulk-selector :count="$count" :total="$total"/>
                                <!-- Selected row actions, only show when rows are selected. -->
                                <div x-show="selected.length > 0" x-cloak class="pl-4 relative flex-auto">
                                    <div class="flex items-center space-x-3 bg-white sm:left-12">
                                        <button type="button" x-on:click="$dispatch('bulk-edit-selected', { values: selected, bulkSelectOption });" class="inline-flex items-center rounded bg-white px-2 py-1 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50 disabled:cursor-not-allowed disabled:opacity-30 disabled:hover:bg-white">Bulk edit</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
            <div class="inline-block min-w-full align-middle sm:px-6 lg:px-8">
                <div class="relative">
                    <table class="min-w-full table-fixed divide-y divide-gray-300">
                        {{ $head }}
                        <tbody class="divide-y divide-gray-200 bg-white">
                        {{ $slot }}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
