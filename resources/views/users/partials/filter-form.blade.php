<div class="filterPanel" id="filterPanel">
    <div class="settingsPanel pa-0">
        <div class="settingsPanel__header flex align-items-m">
            <div class="flex-item-fill">Filter Customers</div>
            <button
                    type="button"
                    class="btn btn-alt flex-item"
                    @click="hidePanel('filterPanel')"
            ><i class="fas fa-times fa-lg"></i></button>
        </div>
        <div class="settingsPanel__body">
            {{--Location--}}
            <div class="form-group">
                <label for="pickup_id">Delivery Method</label>
                <x-form.delivery-method-select
                        class="form-control select2 autofocus"
                        name="pickup_id[]"
                        tabindex="1"
                        data-placeholder="Select zones or locations"
                        multiple
                        style="width: 100%"
                        :selected="request('pickup_id')"
                />
            </div>

            {{--Date Range--}}
            <div x-data="dateRange('last_purchase_start', 'last_purchase_end')" class="form-group">
                <div class="flex items-center justify-between">
                    <label for="last_purchase">Order activity</label>
                    <x-form.date-range-dropdown/>
                </div>

                <div class="mt-1 flex space-x-2 items-center">

                    <x-form.pikaday-input
                            name="last_purchase[start]"
                            class="form-control"
                            value="{{ request('last_purchase')['start'] ?? '' }}"
                            id="last_purchase_start"
                            placeholder="Start date"
                            tabindex="1"
                    />

                    <x-form.pikaday-input
                            name="last_purchase[end]"
                            class="form-control"
                            value="{{ request('last_purchase')['end'] ?? '' }}"
                            id="last_purchase_end"
                            placeholder="End date"
                            tabindex="1"
                    />

                </div>
            </div>

            <div class="form-group">
                <label for="tags">Tags</label>
                <x-form.tag-select
                        class="form-control select2"
                        name="tags[]"
                        multiple
                        style="width: 100%"
                        data-placeholder="Select some tags"
                        :selected="request('tags')"
                />
            </div>

            <div class="form-group">
                <label for="pickup_id">Subscription Status</label>
                <select
                        class="form-control"
                        name="subscription_status"
                        tabindex="1"
                >
                    <option value="">Show All</option>
                    {!! selectOptions(['active' => 'Active', 'inactive' => 'Inactive'], request('subscription_status')) !!}
                </select>
            </div>

            <div class="form-group">
                <label for="state">State</label>
                <x-form.state-select
                        class="form-control"
                        name="state"
                        placeholder="All"
                        :selected="request('state')"
                />
            </div>

            <div class="form-group">
                <label for="newsletter">Newsletter</label>
                <select class="form-control" name="newsletter">
                    <option value="">Show All</option>
                    {!! selectOptions([1 => 'Subscribed', 0 => 'Not Subscribed'], request('newsletter')) !!}
                </select>
            </div>

            <div class="form-group">
                <label for="order_deadline_email_reminder">Deadline Reminders</label>
                <select class="form-control" name="order_deadline_email_reminder">
                    <option value="">Show All</option>
                    {!! selectOptions([1 => 'Subscribed', 0 => 'Not Subscribed'], request('order_deadline_email_reminder')) !!}
                </select>
            </div>

            <div class="form-group">
                <label for="role_id">Role</label>
                <select
                        class="form-control select2"
                        name="role_id[]"
                        multiple
                        style="width: 100%"
                        data-placeholder="Select user roles"
                >
                    <option value="">Show All</option>
                    {!! selectOptions(\App\Support\Enums\UserRole::all(), request('role_id')) !!}
                </select>
            </div>

            <div class="form-group">
                <label for="active">Status</label>
                <select
                        class="form-control"
                        name="active"
                        tabindex="1"
                >
                    {!! selectOptions(['' => 'All', '1' => 'Active', '0' => 'Inactive'], request('active')) !!}
                </select>
            </div>

            <div class="form-group">
                <div class="checkbox">
                    <label for="referral_user_id">
                        <input type="checkbox" id="referral_user_id" name="referral_user_id" tabindex="1" value="true" @if(Request::has('referral_user_id')) checked @endif>
                        Was Referred</label>
                </div>
            </div>
        </div>
        <div class="settingsPanel__footer flex align-items-m">
            <button type="submit" class="btn btn-alt mr-md" name="export" value="true"><i class="fas fa-cloud-download"></i></button>
            <button type="submit" class="btn btn-action btn-block btn-lg" @click="submitForm('filterUsersForm')">Filter</button>
        </div>
    </div>
</div>
