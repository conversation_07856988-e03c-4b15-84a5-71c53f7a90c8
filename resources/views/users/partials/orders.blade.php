<div class="panel">
    <div class="panel-body panel-tabs">
        <table class="table table-striped">
            <thead>
            <tr>
                <th>Order #</th>
                <th>Status</th>
                <th>Date</th>
                <th>Delivery Method</th>
                <th>Total</th>
            </tr>
            </thead>
            <tbody>
            @foreach($orders as $order)
                <tr>
                    <td>
                        <a href="{{ route('admin.orders.edit', [$order->id]) }}" class="btn btn-light btn-sm">#{{ $order->id }}</a>
                        @if(!$order->confirmed)
                            @if($order->subtotal > 0)
                                <span class="label label-default">Unconfirmed</span>
                            @else
                                <span class="label label-default">Empty Cart</span>
                            @endif
                        @endif
                    </td>
                    <td>{{ $order->status() }}</td>
                    <td>{{ $order->present()->confirmedDate('m/d/y') }}</td>
                    <td>{{ $order->pickup ? $order->pickup->title : 'N/A' }}</td>
                    <td>&#36;{{ number_format($order->total / 100, 2) }}</td>
                </tr>
            @endforeach
            @if($orders->isEmpty())
                <tr>
                    <td colspan="100%">No orders found.</td>
                </tr>
            @endif
            </tbody>
        </table>

        <div class="mt-lg text-center">
            @if($orders->isNotEmpty())
                {{ $orders->links() }}
            @endif
        </div>

    </div>


</div>    
