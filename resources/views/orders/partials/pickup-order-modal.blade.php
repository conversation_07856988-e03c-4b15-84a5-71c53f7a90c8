<div class="gc-modal gc-modal-mask" id="pickupOrderModal" @click="hideModal('pickupOrderModal')">
    <div class="gc-modal-wrapper">
        <div class="gc-modal-container" @click.stop>
            <form action="/admin/orders/{{ $order->id }}" method="POST">
                @csrf
                @method('PUT')
                <div class="gc-modal-header">
                    Order Logistics
                </div>

                <div class="gc-modal-body">
                    {{--Pickup_id--}}
                    <div class="form-group">
                        <label for="pickup_id">Delivery Method</label>
                        <x-form.delivery-method-select
                                class="form-control"
                                name="pickup_id"
                                :selected="$order->pickup_id"
                        />
                    </div>

                    @if($order->pickup && $order->pickup->schedule_id)

                        <div class="form-group">
                            <label for="deadline_date">Order Deadline</label>
                            <input
                                    type="text"
                                    class="form-control pickup-date no-auto-select"
                                    name="deadline_date"
                                    value="{{ old('deadline_date', $order->deadline_date ? carbonDate($order->deadline_date, 'm/d/Y') : date('m/d/Y')) }}"
                            >
                        </div>
                        <div class="form-group">
                            <label for="pack_deadline_at">Pack Deadline Date</label>
                            <input type="text"
                                   class="form-control pack_deadline_at no-auto-select"
                                   name="pack_deadline_at"
                                   value="{{ old('pack_deadline_at', $order->pack_deadline_at
                                   ? carbonDate($order->pack_deadline_at, 'm/d/Y hh:mm')
                                   : carbonDate($order->pickup_date->copy()->endOfDay(), 'm/d/Y hh:mm')) }}" />
                        </div>
                        <div class="form-group">
                            <label for="pickup_date">Pickup/Delivery Date</label>
                            <input type="text" class="form-control pickup-date no-auto-select" name="pickup_date" value="{{ old('pickup_date', carbonDate($order->pickup_date, 'm/d/Y')) }}" />
                        </div>
                    @else
                        <div class="form-group">
                            <label for="pickup_date">Pickup/Delivery Date</label>
                            <input
                                    type="text"
                                    class="form-control pickup-date no-auto-select"
                                    name="pickup_date"
                                    value="{{ $order->pickup_date ? $order->pickup_date->format('m/d/Y') : date('m/d/Y') }}"
                            >
                        </div>
                    @endif
                    <div class="form-group">
                        <label for="pickup_date">Sales Channel</label>
                        <x-form.channel-select class="form-control" name="type_id" :selected="$order->type_id" />
                    </div>
                </div>

                <div class="gc-modal-footer">
                    <button type="button" class="btn btn-alt" @click="hideModal('pickupOrderModal')">Cancel</button>
                    <button type="submit" class="btn btn-action">Save</button>
                </div>
            </form>
        </div>
    </div>
</div>
