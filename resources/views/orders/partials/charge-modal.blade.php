<div class="gc-modal gc-modal-mask" id="chargeOrderModal" @click="hideModal('chargeOrderModal')">
    <div class="gc-modal-wrapper">
        <div class="gc-modal-container" @click.stop>
            <form action="/admin/orders/{{ $order->id }}/payments" method="POST" id="chargeOrderForm">
            @csrf
            <div class="gc-modal-header">
                Charge Customer's Saved Card
            </div>

            <div class="gc-modal-body">
                 Charge the customer's saved credit card the order balance due of &#36;<span class="orderTotal">{{ money($order->total) }}</span>?
            </div>

            <div class="gc-modal-footer">
                <button type="button" class="btn btn-alt" @click="hideModal('chargeOrderModal')">Cancel</button>
                <button type="submit" class="btn btn-danger" @click="submitForm('chargeOrderForm', $event)">Charge</button>
            </div>
            </form>
        </div>
    </div>
</div>

<div class="gc-modal gc-modal-mask" id="payOrderModal" @click="hideModal('payOrderModal')">
    <div class="gc-modal-wrapper">
        <div class="gc-modal-container" @click.stop>
            <form action="/admin/orders/{{ $order->id }}/paid" method="POST" id="payOrderForm">
            @csrf
            <div class="gc-modal-header">
                Mark Order as Paid
            </div>

            <div class="gc-modal-body">
                 Mark the order balance of &#36;<span class="orderTotal">{{ money($order->total) }}</span> as paid?
            </div>

            <div class="gc-modal-footer">
                <button type="button" class="btn btn-alt" @click="hideModal('payOrderModal')">Cancel</button>
                <button type="submit" class="btn btn-danger" @click="submitForm('payOrderForm', $event)">Mark as Paid</button>
            </div>
            </form>
        </div>
    </div>
</div>